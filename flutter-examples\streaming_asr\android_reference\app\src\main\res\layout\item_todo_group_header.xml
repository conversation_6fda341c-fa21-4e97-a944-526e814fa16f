<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="?attr/selectableItemBackground"
    android:padding="16dp"
    android:gravity="center_vertical">

    <!-- 折叠/展开图标 -->
    <ImageView
        android:id="@+id/iv_expand_collapse"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_expand_more"
        android:contentDescription="展开/折叠"
        android:tint="@color/apple_secondary_label" />

    <!-- 分组标题 -->
    <TextView
        android:id="@+id/tv_group_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="已完成"
        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
        android:textColor="@color/apple_secondary_label"
        android:textSize="14sp"
        android:textStyle="bold" />

    <!-- 数量标识 -->
    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/badge_background"
        android:text="0"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:minWidth="20dp"
        android:gravity="center" />

</LinearLayout>
