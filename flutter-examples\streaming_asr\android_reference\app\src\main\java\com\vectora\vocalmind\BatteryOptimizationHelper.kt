package com.vectora.vocalmind

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog

/**
 * 电池优化帮助类
 * 用于请求用户将应用加入电池优化白名单，确保提醒功能正常工作
 */
class BatteryOptimizationHelper {
    
    companion object {
        private const val TAG = "BatteryOptimizationHelper"
        private const val REQUEST_IGNORE_BATTERY_OPTIMIZATIONS = 1001
        
        /**
         * 检查应用是否在电池优化白名单中
         */
        fun isIgnoringBatteryOptimizations(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                powerManager.isIgnoringBatteryOptimizations(context.packageName)
            } else {
                true // Android 6.0 以下版本不需要处理
            }
        }
        
        /**
         * 请求用户将应用加入电池优化白名单
         */
        fun requestIgnoreBatteryOptimizations(activity: Activity) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!isIgnoringBatteryOptimizations(activity)) {
                    showBatteryOptimizationDialog(activity)
                }
            }
        }
        
        /**
         * 显示电池优化说明对话框
         */
        private fun showBatteryOptimizationDialog(activity: Activity) {
            AlertDialog.Builder(activity)
                .setTitle("提醒功能需要权限")
                .setMessage("""
                    为了确保 TODO 提醒功能在后台正常工作，需要将应用加入电池优化白名单。
                    
                    请在接下来的设置页面中：
                    1. 找到「VocalMind AI」应用
                    2. 选择「不优化」或「允许」
                    
                    这样可以确保即使在应用关闭时也能收到提醒。
                """.trimIndent())
                .setPositiveButton("去设置") { _, _ ->
                    openBatteryOptimizationSettings(activity)
                }
                .setNegativeButton("稍后设置") { dialog, _ ->
                    dialog.dismiss()
                    Log.w(TAG, "用户选择稍后设置电池优化")
                }
                .setCancelable(false)
                .show()
        }
        
        /**
         * 打开电池优化设置页面
         */
        private fun openBatteryOptimizationSettings(activity: Activity) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                    intent.data = Uri.parse("package:${activity.packageName}")
                    activity.startActivityForResult(intent, REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                }
            } catch (e: Exception) {
                Log.e(TAG, "无法打开电池优化设置", e)
                // 备用方案：打开应用设置页面
                openAppSettings(activity)
            }
        }
        
        /**
         * 打开应用设置页面（备用方案）
         */
        private fun openAppSettings(activity: Activity) {
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.parse("package:${activity.packageName}")
                activity.startActivity(intent)
            } catch (e: Exception) {
                Log.e(TAG, "无法打开应用设置", e)
            }
        }
        
        /**
         * 检查并请求精确闹钟权限（Android 12+）
         */
        fun checkAndRequestExactAlarmPermission(activity: Activity) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val alarmManager = activity.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
                if (!alarmManager.canScheduleExactAlarms()) {
                    showExactAlarmPermissionDialog(activity)
                }
            }
        }
        
        /**
         * 显示精确闹钟权限说明对话框
         */
        private fun showExactAlarmPermissionDialog(activity: Activity) {
            AlertDialog.Builder(activity)
                .setTitle("需要精确闹钟权限")
                .setMessage("""
                    为了确保 TODO 提醒能够准时触发，需要开启精确闹钟权限。
                    
                    请在接下来的设置页面中：
                    1. 找到「VocalMind AI」应用
                    2. 开启「精确闹钟」权限
                """.trimIndent())
                .setPositiveButton("去设置") { _, _ ->
                    openExactAlarmSettings(activity)
                }
                .setNegativeButton("稍后设置") { dialog, _ ->
                    dialog.dismiss()
                }
                .show()
        }
        
        /**
         * 打开精确闹钟设置页面
         */
        private fun openExactAlarmSettings(activity: Activity) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM)
                    intent.data = Uri.parse("package:${activity.packageName}")
                    activity.startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "无法打开精确闹钟设置", e)
                openAppSettings(activity)
            }
        }
        
        /**
         * 显示厂商特定的电池优化设置指导
         */
        fun showManufacturerSpecificGuidance(activity: Activity) {
            val manufacturer = Build.MANUFACTURER.lowercase()
            val guidance = when {
                manufacturer.contains("xiaomi") -> """
                    小米手机额外设置：
                    1. 设置 → 应用设置 → 应用管理 → VocalMind AI
                    2. 电量和性能 → 无限制
                    3. 自启动管理 → 允许自启动
                    4. 锁屏显示 → 允许
                """.trimIndent()
                
                manufacturer.contains("huawei") || manufacturer.contains("honor") -> """
                    华为/荣耀手机额外设置：
                    1. 设置 → 应用和服务 → 应用管理 → VocalMind AI
                    2. 电池 → 启动管理 → 手动管理 → 全部开启
                    3. 通知 → 允许通知 → 锁屏通知
                """.trimIndent()
                
                manufacturer.contains("oppo") -> """
                    OPPO 手机额外设置：
                    1. 设置 → 电池 → 应用耗电管理 → VocalMind AI → 不限制
                    2. 设置 → 应用管理 → VocalMind AI → 权限 → 自启动
                """.trimIndent()
                
                manufacturer.contains("vivo") -> """
                    vivo 手机额外设置：
                    1. 设置 → 电池 → 后台应用管理 → VocalMind AI → 允许后台活动
                    2. 设置 → 应用与权限 → 权限管理 → 自启动 → VocalMind AI
                """.trimIndent()
                
                manufacturer.contains("samsung") -> """
                    三星手机额外设置：
                    1. 设置 → 设备保养 → 电池 → 应用电源管理 → VocalMind AI → 不优化
                    2. 设置 → 应用程序 → VocalMind AI → 电池 → 优化电池使用量 → 关闭
                """.trimIndent()
                
                else -> """
                    通用设置建议：
                    1. 在系统设置中找到电池或电源管理
                    2. 将 VocalMind AI 加入白名单或设为不限制
                    3. 允许应用在后台运行和自启动
                """.trimIndent()
            }
            
            AlertDialog.Builder(activity)
                .setTitle("厂商特定设置指导")
                .setMessage(guidance)
                .setPositiveButton("知道了") { dialog, _ ->
                    dialog.dismiss()
                }
                .show()
        }
    }
}
