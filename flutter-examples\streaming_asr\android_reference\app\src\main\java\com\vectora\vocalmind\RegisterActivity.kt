package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Patterns
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.google.android.material.checkbox.MaterialCheckBox
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.vectora.vocalmind.server.ServerApiService
import kotlinx.coroutines.launch

/**
 * 注册页面 - SaaS标准注册界面
 * 提供清晰的分步注册流程
 */
class RegisterActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "RegisterActivity"
    }

    // UI组件
    private lateinit var tilName: TextInputLayout
    private lateinit var etName: TextInputEditText
    private lateinit var tilEmail: TextInputLayout
    private lateinit var etEmail: TextInputEditText
    private lateinit var tilPassword: TextInputLayout
    private lateinit var etPassword: TextInputEditText
    private lateinit var tilConfirmPassword: TextInputLayout
    private lateinit var etConfirmPassword: TextInputEditText
    private lateinit var cbTerms: MaterialCheckBox
    private lateinit var btnRegister: MaterialButton
    private lateinit var tvLogin: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvError: TextView
    private lateinit var tvPasswordStrength: TextView

    private lateinit var apiService: ServerApiService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_register)

        initViews()
        initServices()
        setupEventListeners()
        setupFormValidation()
    }

    private fun initViews() {
        tilName = findViewById(R.id.til_name)
        etName = findViewById(R.id.et_name)
        tilEmail = findViewById(R.id.til_email)
        etEmail = findViewById(R.id.et_email)
        tilPassword = findViewById(R.id.til_password)
        etPassword = findViewById(R.id.et_password)
        tilConfirmPassword = findViewById(R.id.til_confirm_password)
        etConfirmPassword = findViewById(R.id.et_confirm_password)
        cbTerms = findViewById(R.id.cb_terms)
        btnRegister = findViewById(R.id.btn_register)
        tvLogin = findViewById(R.id.tv_login)
        progressBar = findViewById(R.id.progress_bar)
        tvError = findViewById(R.id.tv_error)
        tvPasswordStrength = findViewById(R.id.tv_password_strength)
    }

    private fun initServices() {
        apiService = ServerApiService.getInstance(this)
    }

    private fun setupEventListeners() {
        btnRegister.setOnClickListener { performRegister() }
        
        tvLogin.setOnClickListener {
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
        }

        cbTerms.setOnCheckedChangeListener { _, _ ->
            validateForm()
        }

        // 返回按钮
        findViewById<ImageView>(R.id.iv_back).setOnClickListener {
            onBackPressed()
        }

        // 服务条款链接
        findViewById<TextView>(R.id.tv_terms_link).setOnClickListener {
            // TODO: 打开服务条款页面
            showToast("服务条款页面")
        }

        // 隐私政策链接
        findViewById<TextView>(R.id.tv_privacy_link).setOnClickListener {
            // TODO: 打开隐私政策页面
            showToast("隐私政策页面")
        }
    }

    private fun setupFormValidation() {
        val textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                validateForm()
                clearError()
            }
        }

        etName.addTextChangedListener(textWatcher)
        etEmail.addTextChangedListener(textWatcher)
        etPassword.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                validateForm()
                clearError()
                updatePasswordStrength(s.toString())
            }
        })
        etConfirmPassword.addTextChangedListener(textWatcher)
    }

    private fun validateForm(): Boolean {
        val name = etName.text.toString().trim()
        val email = etEmail.text.toString().trim()
        val password = etPassword.text.toString().trim()
        val confirmPassword = etConfirmPassword.text.toString().trim()

        var isValid = true

        // 验证姓名
        if (name.isEmpty()) {
            tilName.error = "请输入姓名"
            isValid = false
        } else if (name.length < 2) {
            tilName.error = "姓名至少2个字符"
            isValid = false
        } else {
            tilName.error = null
        }

        // 验证邮箱
        if (email.isEmpty()) {
            tilEmail.error = "请输入邮箱"
            isValid = false
        } else if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            tilEmail.error = "请输入有效的邮箱地址"
            isValid = false
        } else {
            tilEmail.error = null
        }

        // 验证密码
        if (password.isEmpty()) {
            tilPassword.error = "请输入密码"
            isValid = false
        } else if (password.length < 8) {
            tilPassword.error = "密码至少8位"
            isValid = false
        } else if (!isPasswordStrong(password)) {
            tilPassword.error = "密码强度不够"
            isValid = false
        } else {
            tilPassword.error = null
        }

        // 验证确认密码
        if (confirmPassword.isEmpty()) {
            tilConfirmPassword.error = "请确认密码"
            isValid = false
        } else if (password != confirmPassword) {
            tilConfirmPassword.error = "两次输入的密码不一致"
            isValid = false
        } else {
            tilConfirmPassword.error = null
        }

        // 验证服务条款
        if (!cbTerms.isChecked) {
            isValid = false
        }

        btnRegister.isEnabled = isValid
        return isValid
    }

    private fun isPasswordStrong(password: String): Boolean {
        return password.length >= 8 &&
                password.any { it.isUpperCase() } &&
                password.any { it.isLowerCase() } &&
                password.any { it.isDigit() }
    }

    private fun updatePasswordStrength(password: String) {
        when {
            password.isEmpty() -> {
                tvPasswordStrength.visibility = View.GONE
            }
            password.length < 8 -> {
                tvPasswordStrength.text = "密码强度：弱"
                tvPasswordStrength.setTextColor(getColor(R.color.error_color))
                tvPasswordStrength.visibility = View.VISIBLE
            }
            isPasswordStrong(password) -> {
                tvPasswordStrength.text = "密码强度：强"
                tvPasswordStrength.setTextColor(getColor(R.color.success_color))
                tvPasswordStrength.visibility = View.VISIBLE
            }
            else -> {
                tvPasswordStrength.text = "密码强度：中"
                tvPasswordStrength.setTextColor(getColor(R.color.warning_color))
                tvPasswordStrength.visibility = View.VISIBLE
            }
        }
    }

    private fun clearError() {
        tvError.visibility = View.GONE
    }

    private fun showError(message: String) {
        tvError.text = message
        tvError.visibility = View.VISIBLE
    }

    private fun performRegister() {
        if (!validateForm()) return

        val name = etName.text.toString().trim()
        val email = etEmail.text.toString().trim()
        val password = etPassword.text.toString().trim()

        setLoading(true)
        clearError()

        lifecycleScope.launch {
            try {
                val result = apiService.register(email, password, name)

                runOnUiThread {
                    setLoading(false)
                    
                    if (result.isSuccess()) {
                        // 注册成功，跳转到登录页面
                        showToast("注册成功，请登录")
                        startActivity(Intent(this@RegisterActivity, LoginActivity::class.java))
                        finish()
                    } else {
                        showError(result.getErrorMessage() ?: "注册失败")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    setLoading(false)
                    showError("网络错误，请检查网络连接")
                }
            }
        }
    }

    private fun setLoading(loading: Boolean) {
        progressBar.visibility = if (loading) View.VISIBLE else View.GONE
        btnRegister.isEnabled = !loading && validateForm()
        etName.isEnabled = !loading
        etEmail.isEnabled = !loading
        etPassword.isEnabled = !loading
        etConfirmPassword.isEnabled = !loading
        cbTerms.isEnabled = !loading
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onBackPressed() {
        startActivity(Intent(this, WelcomeActivity::class.java))
        finish()
    }
}
