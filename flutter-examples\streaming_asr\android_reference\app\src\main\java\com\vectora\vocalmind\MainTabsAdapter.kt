package com.vectora.vocalmind

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * 主页Tab适配器
 * 管理三个Tab的Fragment切换
 */
class MainTabsAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {

    companion object {
        const val TAB_MEETING_RECORDS = 0
        const val TAB_RECORDING = 1
        const val TAB_TODO = 2
        const val TAB_COUNT = 3
    }

    override fun getItemCount(): Int = TAB_COUNT

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            TAB_MEETING_RECORDS -> MeetingRecordsFragment.newInstance()
            TAB_RECORDING -> RecordingFragment.newInstance()
            TAB_TODO -> TodoFragment.newInstance()
            else -> throw IllegalArgumentException("Invalid tab position: $position")
        }
    }

    /**
     * 获取Tab标题
     */
    fun getTabTitle(position: Int): String {
        return when (position) {
            TAB_MEETING_RECORDS -> "会议记录"
            TAB_RECORDING -> "录音"
            TAB_TODO -> "待办"
            else -> ""
        }
    }

    /**
     * 获取Tab图标资源ID
     */
    fun getTabIcon(position: Int): Int {
        return when (position) {
            TAB_MEETING_RECORDS -> R.drawable.ic_meeting_records_apple
            TAB_RECORDING -> R.drawable.ic_mic
            TAB_TODO -> R.drawable.ic_todo
            else -> 0
        }
    }
}
