# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# 保留调试信息
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# 保留注解
-keepattributes *Annotation*

# 保留序列化相关
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# 保留Gson相关类
-keep class com.google.gson.** { *; }
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# 保留OkHttp相关类
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

# 保留Markwon相关类
-keep class io.noties.markwon.** { *; }
-dontwarn io.noties.markwon.**

# 保留应用主要类和数据模型
-keep class com.vectora.vocalmind.** { *; }

# 保留Activity、Service、BroadcastReceiver等组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# 保留View相关
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...); 
    *** get*();
}

# 保留Kotlin相关
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }
-dontwarn kotlin.**
-dontwarn kotlinx.**

# 保留协程相关
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# 如果使用WebView，取消注释以下规则
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}