<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 毛玻璃效果背景 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#F0FFFFFF" />
            <corners
                android:topLeftRadius="24dp"
                android:topRightRadius="24dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
            <!-- 添加微妙的边框来替代分隔线 -->
            <stroke
                android:width="0.5dp"
                android:color="#0A000000" />
        </shape>
    </item>

    <!-- 内部光效 -->
    <item android:top="1dp" android:left="0.5dp" android:right="0.5dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#08FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="90" />
            <corners
                android:topLeftRadius="23dp"
                android:topRightRadius="23dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>
</layer-list>
