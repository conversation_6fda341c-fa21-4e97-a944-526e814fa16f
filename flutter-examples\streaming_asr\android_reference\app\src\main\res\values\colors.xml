<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Apple Design System Colors -->

    <!-- 苹果标准蓝色系统 -->
    <color name="apple_blue">#007AFF</color>
    <color name="apple_blue_dark">#0051D5</color>
    <color name="apple_blue_light">#40A9FF</color>
    <color name="apple_blue_ultra_light">#E3F2FD</color>

    <!-- 苹果系统绿色 -->
    <color name="apple_green">#34C759</color>
    <color name="apple_green_dark">#248A3D</color>
    <color name="apple_green_light">#6ED16E</color>

    <!-- 苹果系统红色 -->
    <color name="apple_red">#FF3B30</color>
    <color name="apple_red_dark">#D70015</color>
    <color name="apple_red_light">#FF6961</color>

    <!-- 苹果系统橙色 -->
    <color name="apple_orange">#FF9500</color>
    <color name="apple_orange_dark">#FF8C00</color>
    <color name="apple_orange_light">#FFB84D</color>

    <!-- 苹果系统黄色 -->
    <color name="apple_yellow">#FFCC00</color>
    <color name="apple_yellow_dark">#FFB000</color>

    <!-- 苹果灰色系统 -->
    <color name="apple_gray">#8E8E93</color>
    <color name="apple_gray_2">#AEAEB2</color>
    <color name="apple_gray_3">#C7C7CC</color>
    <color name="apple_gray_4">#D1D1D6</color>
    <color name="apple_gray_5">#E5E5EA</color>
    <color name="apple_gray_6">#F2F2F7</color>

    <!-- 苹果标准文本颜色 -->
    <color name="apple_label">#1C1C1E</color>
    <color name="apple_secondary_label">#3C3C43</color>
    <color name="apple_tertiary_label">#3C3C4399</color>
    <color name="apple_quaternary_label">#3C3C432E</color>

    <!-- 苹果背景颜色 -->
    <color name="apple_system_background">#FFFFFF</color>
    <color name="apple_secondary_system_background">#F2F2F7</color>
    <color name="apple_tertiary_system_background">#FFFFFF</color>

    <!-- 苹果分组背景 -->
    <color name="apple_system_grouped_background">#F2F2F7</color>
    <color name="apple_secondary_system_grouped_background">#FFFFFF</color>
    <color name="apple_tertiary_system_grouped_background">#F2F2F7</color>

    <!-- 苹果分隔符颜色 -->
    <color name="apple_separator">#3C3C4349</color>
    <color name="apple_opaque_separator">#C6C6C8</color>

    <!-- 苹果填充颜色 -->
    <color name="apple_system_fill">#78788033</color>
    <color name="apple_secondary_system_fill">#78788028</color>
    <color name="apple_tertiary_system_fill">#7676801E</color>
    <color name="apple_quaternary_system_fill">#74748014</color>

    <!-- 应用特定颜色 -->
    <color name="recording_pulse">#FF3B30</color>
    <color name="recording_pulse_light">#FF3B3040</color>
    <color name="waveform_active">#007AFF</color>
    <color name="waveform_inactive">#C7C7CC</color>

    <!-- 透明度变体 -->
    <color name="apple_blue_alpha_10">#1A007AFF</color>
    <color name="apple_blue_alpha_20">#33007AFF</color>
    <color name="apple_blue_alpha_30">#4D007AFF</color>
    <color name="apple_red_alpha_10">#1AFF3B30</color>
    <color name="apple_red_alpha_20">#33FF3B30</color>

    <!-- 兼容性颜色 -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="transparent">#00000000</color>

    <!-- Legacy colors for compatibility -->
    <color name="purple_200">@color/apple_blue_light</color>
    <color name="purple_500">@color/apple_blue</color>
    <color name="purple_700">@color/apple_blue_dark</color>
    <color name="teal_200">@color/apple_green_light</color>
    <color name="teal_700">@color/apple_green_dark</color>

    <!-- Legacy UI colors mapping to Apple colors -->
    <color name="primary_blue">@color/apple_blue</color>
    <color name="primary_blue_dark">@color/apple_blue_dark</color>
    <color name="primary_blue_light">@color/apple_blue_light</color>
    <color name="accent_green">@color/apple_green</color>
    <color name="accent_orange">@color/apple_orange</color>
    <color name="accent_red">@color/apple_red</color>
    <color name="background_light">@color/apple_secondary_system_background</color>
    <color name="background_white">@color/apple_system_background</color>
    <color name="background_card">@color/apple_tertiary_system_background</color>
    <color name="text_primary">@color/apple_label</color>
    <color name="text_secondary">@color/apple_secondary_label</color>
    <color name="text_tertiary">@color/apple_tertiary_label</color>
    <color name="text_hint">@color/apple_tertiary_label</color>
    <color name="text_preview">@color/apple_blue_dark</color>
    <color name="status_ready">@color/apple_green</color>
    <color name="status_recording">@color/apple_red</color>
    <color name="status_processing">@color/apple_orange</color>
    <color name="status_error">@color/apple_red</color>
    <color name="white_alpha_70">#B3FFFFFF</color>
    <color name="black_alpha_12">#1F000000</color>
    <color name="primary_alpha_12">#1F007AFF</color>
    <color name="divider_color">@color/apple_separator</color>
    <color name="border_color">@color/apple_opaque_separator</color>
    <color name="preview_background">@color/apple_blue_ultra_light</color>
    <color name="results_background">@color/apple_system_background</color>
    <color name="count_background">@color/apple_green_light</color>

    <!-- SaaS标准认证界面颜色主题 -->

    <!-- 主色调 -->
    <color name="primary_color">#007AFF</color>
    <color name="primary_dark">#0056CC</color>
    <color name="primary_light">#4DA3FF</color>

    <!-- 辅助色 -->
    <color name="secondary_color">#5856D6</color>
    <color name="accent_color">#FF9500</color>

    <!-- 文本颜色 -->
    <color name="primary_text">#1D1D1F</color>
    <color name="secondary_text">#6D6D80</color>
    <color name="tertiary_text">#8E8E93</color>

    <!-- 背景颜色 -->
    <color name="welcome_background">#FFFFFF</color>
    <color name="login_background">#FAFAFA</color>
    <color name="card_background">#FFFFFF</color>

    <!-- 状态颜色 -->
    <color name="success_color">#34C759</color>
    <color name="warning_color">#FF9500</color>
    <color name="error_color">#FF3B30</color>
    <color name="info_color">#007AFF</color>

    <!-- 分割线和边框 -->
    <color name="divider_color_new">#E5E5EA</color>
    <color name="border_color_new">#D1D1D6</color>

    <!-- 按钮颜色 -->
    <color name="button_primary">#007AFF</color>
    <color name="button_secondary">#F2F2F7</color>
    <color name="button_disabled">#C7C7CC</color>

    <!-- 输入框颜色 -->
    <color name="input_background">#FFFFFF</color>
    <color name="input_border">#D1D1D6</color>
    <color name="input_focus">#007AFF</color>

    <!-- 覆盖层和阴影 -->
    <color name="overlay_light">#80FFFFFF</color>
    <color name="overlay_dark">#80000000</color>
    <color name="shadow_color">#1A000000</color>

    <!-- 渐变色 -->
    <color name="gradient_start">#007AFF</color>
    <color name="gradient_end">#5856D6</color>

    <!-- 特殊状态 -->
    <color name="pressed_state">#E5E5EA</color>
    <color name="selected_state">#007AFF</color>
    <color name="disabled_state">#F2F2F7</color>

</resources>