<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <layer-list>
            <!-- 背景光晕 -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="20dp" />
                    <solid android:color="#15007AFF" />
                </shape>
            </item>
            <!-- 内部背景 -->
            <item android:top="4dp" android:bottom="4dp" android:left="8dp" android:right="8dp">
                <shape android:shape="rectangle">
                    <corners android:radius="16dp" />
                    <solid android:color="#25007AFF" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 按压状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="20dp" />
            <solid android:color="#10007AFF" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="20dp" />
            <solid android:color="#00000000" />
        </shape>
    </item>
</selector>
