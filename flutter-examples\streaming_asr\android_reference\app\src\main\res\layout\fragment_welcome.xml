<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp"
    android:minHeight="300dp">

    <!-- 功能图标 -->
    <ImageView
        android:id="@+id/iv_welcome_image"
        android:layout_width="160dp"
        android:layout_height="160dp"
        android:layout_marginBottom="24dp"
        android:contentDescription="Feature Image"
        android:scaleType="centerInside" />

    <!-- 功能标题 -->
    <TextView
        android:id="@+id/tv_welcome_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="功能标题"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:maxLines="2"
        android:ellipsize="end" />

    <!-- 功能描述 -->
    <TextView
        android:id="@+id/tv_welcome_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="功能描述文本"
        android:textSize="16sp"
        android:textColor="@color/secondary_text"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:alpha="0.9"
        android:maxLines="3"
        android:ellipsize="end"
        android:paddingHorizontal="16dp" />

</LinearLayout>

</ScrollView>
