<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- Header Section -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="24dp"
        android:paddingBottom="16dp"
        android:background="@color/apple_system_background"
        android:elevation="2dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back_apple"
            android:contentDescription="返回"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💬 AI问答"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_meeting_context"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="基于会议记录内容"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label" />

        </LinearLayout>

        <ImageButton
            android:id="@+id/btn_clear_chat"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_delete_apple"
            android:contentDescription="清空聊天记录"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Chat Messages -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_chat_messages"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="100dp"
        android:layout_marginBottom="80dp"
        android:padding="16dp"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- Input Section -->
    <LinearLayout
        android:id="@+id/layout_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:padding="10dp"
        android:gravity="center_vertical"
        android:background="@drawable/bottom_nav_background"
        android:elevation="4dp">

        <EditText
            android:id="@+id/et_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="输入您的问题..."
            android:background="@drawable/chat_input_background"
            android:padding="12dp"
            android:maxLines="4"
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
            android:layout_marginEnd="12dp" />

<!--        <Button-->
<!--            android:id="@+id/btn_send"-->
<!--            android:layout_width="48dp"-->
<!--            android:layout_height="48dp"-->
<!--            android:background="@drawable/chat_send_button_background"-->
<!--            android:text="➤"-->
<!--            android:textColor="@android:color/white"-->
<!--            android:textSize="18sp"-->
<!--            android:enabled="false" />-->

        <ImageView
            android:id="@+id/btn_send"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/chat_send_button_background" />

    </LinearLayout>

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progress_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>