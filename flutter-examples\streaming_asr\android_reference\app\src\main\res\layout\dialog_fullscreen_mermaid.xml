<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:clickable="true"
    android:focusable="true">

    <!-- 拖动手柄 -->
    <View
        android:id="@+id/dragHandle"
        android:layout_width="60dp"
        android:layout_height="6dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_drag_handle" />

    <!-- 操作提示文本 -->
    <TextView
        android:id="@+id/tvOperationHint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/dragHandle"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:text="拖动移动 • 双指缩放 • 双击重置"
        android:textColor="#666666"
        android:textSize="12sp"
        android:alpha="0.8" />

    <!-- 关闭按钮 -->
    <ImageButton
        android:id="@+id/btnCloseFullscreen"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@drawable/bg_close_button"
        android:contentDescription="关闭全屏"
        android:elevation="8dp"
        android:src="@drawable/ic_close" />

    <!-- 缩放指示器 -->
    <TextView
        android:id="@+id/tvZoomIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_margin="16dp"
        android:background="#80000000"
        android:padding="8dp"
        android:text="100%"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone"
        android:elevation="8dp" />

    <!-- 全屏WebView -->
    <WebView
        android:id="@+id/webViewFullscreenMermaid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tvOperationHint"
        android:layout_marginTop="8dp" />

</RelativeLayout>