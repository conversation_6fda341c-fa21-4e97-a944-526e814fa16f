package com.vectora.vocalmind.server

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.gson.Gson
import java.util.*

/**
 * 用户认证管理器
 * 管理用户登录状态、JWT Token、用户信息等
 */
object UserAuthManager {
    private const val TAG = "UserAuthManager"
    private const val PREFS_NAME = "user_auth_prefs"
    
    // 存储键名
    private const val KEY_ACCESS_TOKEN = "access_token"
    private const val KEY_TOKEN_TYPE = "token_type"
    private const val KEY_TOKEN_EXPIRES_AT = "token_expires_at"
    private const val KEY_USER_INFO = "user_info"
    private const val KEY_IS_LOGGED_IN = "is_logged_in"
    private const val KEY_LAST_LOGIN_TIME = "last_login_time"
    
    private val gson = Gson()
    
    /**
     * 获取加密的SharedPreferences
     */
    private fun getSecurePrefs(context: Context): SharedPreferences {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
        
        return EncryptedSharedPreferences.create(
            context,
            PREFS_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    /**
     * 保存登录信息
     */
    fun saveLoginData(context: Context, loginData: LoginData): Boolean {
        return try {
            val currentTime = System.currentTimeMillis()
            val expiresAt = currentTime + (loginData.expiresIn * 1000) // 转换为毫秒
            
            val prefs = getSecurePrefs(context)
            prefs.edit()
                .putString(KEY_ACCESS_TOKEN, loginData.accessToken)
                .putString(KEY_TOKEN_TYPE, loginData.tokenType)
                .putLong(KEY_TOKEN_EXPIRES_AT, expiresAt)
                .putString(KEY_USER_INFO, gson.toJson(loginData.user))
                .putBoolean(KEY_IS_LOGGED_IN, true)
                .putLong(KEY_LAST_LOGIN_TIME, currentTime)
                .apply()
            
            Log.d(TAG, "登录信息已保存，用户: ${loginData.user.email}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存登录信息失败", e)
            false
        }
    }
    
    /**
     * 获取访问令牌
     */
    fun getAccessToken(context: Context): String? {
        return try {
            if (!isTokenValid(context)) {
                Log.d(TAG, "Token已过期或无效")
                return null
            }
            getSecurePrefs(context).getString(KEY_ACCESS_TOKEN, null)
        } catch (e: Exception) {
            Log.e(TAG, "获取访问令牌失败", e)
            null
        }
    }
    
    /**
     * 获取完整的Authorization头
     */
    fun getAuthorizationHeader(context: Context): String? {
        return try {
            val token = getAccessToken(context) ?: return null
            val tokenType = getSecurePrefs(context).getString(KEY_TOKEN_TYPE, "Bearer") ?: "Bearer"
            "$tokenType $token"
        } catch (e: Exception) {
            Log.e(TAG, "获取Authorization头失败", e)
            null
        }
    }
    
    /**
     * 检查Token是否有效
     */
    fun isTokenValid(context: Context): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            val isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false)
            if (!isLoggedIn) return false
            
            val expiresAt = prefs.getLong(KEY_TOKEN_EXPIRES_AT, 0)
            val currentTime = System.currentTimeMillis()
            
            // 提前5分钟判断过期，避免请求时刚好过期
            val bufferTime = 5 * 60 * 1000 // 5分钟
            val isValid = currentTime < (expiresAt - bufferTime)
            
            if (!isValid) {
                Log.d(TAG, "Token已过期")
            }
            
            isValid
        } catch (e: Exception) {
            Log.e(TAG, "检查Token有效性失败", e)
            false
        }
    }
    
    /**
     * 获取当前用户信息
     */
    fun getCurrentUser(context: Context): User? {
        return try {
            if (!isLoggedIn(context)) return null
            
            val userJson = getSecurePrefs(context).getString(KEY_USER_INFO, null)
            if (userJson != null) {
                gson.fromJson(userJson, User::class.java)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取用户信息失败", e)
            null
        }
    }
    
    /**
     * 检查是否已登录
     */
    fun isLoggedIn(context: Context): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            val isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false)
            isLoggedIn && isTokenValid(context)
        } catch (e: Exception) {
            Log.e(TAG, "检查登录状态失败", e)
            false
        }
    }
    
    /**
     * 登出用户
     */
    fun logout(context: Context): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            prefs.edit()
                .remove(KEY_ACCESS_TOKEN)
                .remove(KEY_TOKEN_TYPE)
                .remove(KEY_TOKEN_EXPIRES_AT)
                .remove(KEY_USER_INFO)
                .putBoolean(KEY_IS_LOGGED_IN, false)
                .apply()
            
            Log.d(TAG, "用户已登出")
            true
        } catch (e: Exception) {
            Log.e(TAG, "登出失败", e)
            false
        }
    }
    
    /**
     * 清除所有认证数据
     */
    fun clearAllAuthData(context: Context): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            prefs.edit().clear().apply()
            Log.d(TAG, "所有认证数据已清除")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清除认证数据失败", e)
            false
        }
    }
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(context: Context, user: User): Boolean {
        return try {
            if (!isLoggedIn(context)) {
                Log.w(TAG, "用户未登录，无法更新用户信息")
                return false
            }
            
            getSecurePrefs(context).edit()
                .putString(KEY_USER_INFO, gson.toJson(user))
                .apply()
            
            Log.d(TAG, "用户信息已更新")
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新用户信息失败", e)
            false
        }
    }
    
    /**
     * 获取Token剩余有效时间（秒）
     */
    fun getTokenRemainingTime(context: Context): Long {
        return try {
            if (!isLoggedIn(context)) return 0
            
            val expiresAt = getSecurePrefs(context).getLong(KEY_TOKEN_EXPIRES_AT, 0)
            val currentTime = System.currentTimeMillis()
            val remainingMs = expiresAt - currentTime
            
            if (remainingMs > 0) remainingMs / 1000 else 0
        } catch (e: Exception) {
            Log.e(TAG, "获取Token剩余时间失败", e)
            0
        }
    }
    
    /**
     * 获取上次登录时间
     */
    fun getLastLoginTime(context: Context): Date? {
        return try {
            val timestamp = getSecurePrefs(context).getLong(KEY_LAST_LOGIN_TIME, 0)
            if (timestamp > 0) Date(timestamp) else null
        } catch (e: Exception) {
            Log.e(TAG, "获取上次登录时间失败", e)
            null
        }
    }
    
    /**
     * 获取认证状态摘要（用于调试）
     */
    fun getAuthStatusSummary(context: Context): String {
        return try {
            val user = getCurrentUser(context)
            val remainingTime = getTokenRemainingTime(context)
            val lastLogin = getLastLoginTime(context)
            
            """
            认证状态摘要:
            - 登录状态: ${isLoggedIn(context)}
            - Token有效: ${isTokenValid(context)}
            - 当前用户: ${user?.email ?: "未登录"}
            - Token剩余时间: ${remainingTime}秒
            - 上次登录: ${lastLogin ?: "无记录"}
            """.trimIndent()
        } catch (e: Exception) {
            "获取认证状态失败: ${e.message}"
        }
    }
}
