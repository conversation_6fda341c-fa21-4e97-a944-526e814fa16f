
package com.vectora.vocalmind

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.view.View
import android.view.animation.OvershootInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.interpolator.view.animation.FastOutSlowInInterpolator

/**
 * 自定义底部导航控制器
 * 提供炫酷的动画效果和高级感的交互体验
 */
class CustomBottomNavigation(private val context: Context) {
    
    private var currentSelectedTab = MainTabsAdapter.TAB_RECORDING
    private var onTabSelectedListener: ((Int) -> Unit)? = null
    
    // Tab视图引用
    private lateinit var tabMeetingRecords: FrameLayout
    private lateinit var tabRecording: FrameLayout
    private lateinit var tabTodo: FrameLayout
    
    // 图标引用
    private lateinit var iconMeetingRecords: ImageView
    private lateinit var iconRecording: ImageView
    private lateinit var iconTodo: ImageView
    
    fun initialize(rootView: View) {
        // 获取视图引用
        tabMeetingRecords = rootView.findViewById(R.id.tab_meeting_records)
        tabRecording = rootView.findViewById(R.id.tab_recording)
        tabTodo = rootView.findViewById(R.id.tab_todo)

        iconMeetingRecords = rootView.findViewById(R.id.icon_meeting_records)
        iconRecording = rootView.findViewById(R.id.icon_recording)
        iconTodo = rootView.findViewById(R.id.icon_todo)

        // 设置点击监听器
        setupClickListeners()

        // 直接设置初始状态，避免selectTab中的条件检查导致状态不更新
        updateTabState(MainTabsAdapter.TAB_MEETING_RECORDS, currentSelectedTab == MainTabsAdapter.TAB_MEETING_RECORDS, false)
        updateTabState(MainTabsAdapter.TAB_RECORDING, currentSelectedTab == MainTabsAdapter.TAB_RECORDING, false)
        updateTabState(MainTabsAdapter.TAB_TODO, currentSelectedTab == MainTabsAdapter.TAB_TODO, false)
    }
    
    private fun setupClickListeners() {
        tabMeetingRecords.setOnClickListener {
            selectTab(MainTabsAdapter.TAB_MEETING_RECORDS)
        }
        
        tabRecording.setOnClickListener {
            selectTab(MainTabsAdapter.TAB_RECORDING)
        }
        
        tabTodo.setOnClickListener {
            selectTab(MainTabsAdapter.TAB_TODO)
        }
    }
    
    fun selectTab(tabIndex: Int, animate: Boolean = true) {
        if (currentSelectedTab == tabIndex) return
        
        val previousTab = currentSelectedTab
        currentSelectedTab = tabIndex
        
        // 更新所有Tab的状态
        updateTabState(MainTabsAdapter.TAB_MEETING_RECORDS, tabIndex == MainTabsAdapter.TAB_MEETING_RECORDS, animate)
        updateTabState(MainTabsAdapter.TAB_RECORDING, tabIndex == MainTabsAdapter.TAB_RECORDING, animate)
        updateTabState(MainTabsAdapter.TAB_TODO, tabIndex == MainTabsAdapter.TAB_TODO, animate)
        
        // 触发回调
        onTabSelectedListener?.invoke(tabIndex)
    }
    
    private fun updateTabState(tabIndex: Int, isSelected: Boolean, animate: Boolean) {
        val (tab, icon) = when (tabIndex) {
            MainTabsAdapter.TAB_MEETING_RECORDS -> Pair(tabMeetingRecords, iconMeetingRecords)
            MainTabsAdapter.TAB_RECORDING -> Pair(tabRecording, iconRecording)
            MainTabsAdapter.TAB_TODO -> Pair(tabTodo, iconTodo)
            else -> return
        }

        tab.isSelected = isSelected

        if (animate) {
            animateTabSelection(tab, icon, isSelected, tabIndex)
        } else {
            updateTabAppearance(tab, icon, isSelected, tabIndex)
        }
    }
    
    private fun animateTabSelection(tab: FrameLayout, icon: ImageView, isSelected: Boolean, tabIndex: Int) {
        if (isSelected) {
            if (tabIndex == MainTabsAdapter.TAB_RECORDING) {
                // 中间录音按钮：直接设置放大状态，无动画
                tab.scaleX = 1.15f
                tab.scaleY = 1.15f
            } else {
                // 其他Tab：正常的选中动画
                val scaleX = ObjectAnimator.ofFloat(tab, "scaleX", 0.9f, 1.1f, 1.0f)
                val scaleY = ObjectAnimator.ofFloat(tab, "scaleY", 0.9f, 1.1f, 1.0f)

                val animatorSet = AnimatorSet()
                animatorSet.playTogether(scaleX, scaleY)
                animatorSet.duration = 300
                animatorSet.interpolator = OvershootInterpolator(1.2f)
                animatorSet.start()
            }
        } else {
            // 取消选中时恢复原始大小
            if (tabIndex == MainTabsAdapter.TAB_RECORDING) {
                // 直接恢复原始大小，无动画
                tab.scaleX = 1.0f
                tab.scaleY = 1.0f

                // 停止脉动动画
                tab.clearAnimation()
            }
        }

        // 颜色渐变动画
        animateColorTransition(icon, isSelected, tabIndex)
    }
    
    private fun animateColorTransition(icon: ImageView, isSelected: Boolean, tabIndex: Int) {
        val colorFrom = if (isSelected) {
            ContextCompat.getColor(context, R.color.apple_secondary_label)
        } else {
            ContextCompat.getColor(context, R.color.apple_blue)
        }

        val colorTo = if (isSelected) {
            if (tabIndex == MainTabsAdapter.TAB_RECORDING) {
                ContextCompat.getColor(context, R.color.white)
            } else {
                ContextCompat.getColor(context, R.color.apple_blue)
            }
        } else {
            ContextCompat.getColor(context, R.color.apple_secondary_label)
        }

        val colorAnimator = ValueAnimator.ofArgb(colorFrom, colorTo)
        colorAnimator.duration = 200
        colorAnimator.interpolator = FastOutSlowInInterpolator()
        colorAnimator.addUpdateListener { animator ->
            val color = animator.animatedValue as Int
            if (tabIndex != MainTabsAdapter.TAB_RECORDING) {
                icon.setColorFilter(color)
            }
        }
        colorAnimator.start()
    }
    
    private fun updateTabAppearance(tab: FrameLayout, icon: ImageView, isSelected: Boolean, tabIndex: Int) {
        // 设置颜色
        val color = if (isSelected) {
            if (tabIndex == MainTabsAdapter.TAB_RECORDING) {
                ContextCompat.getColor(context, R.color.white)
            } else {
                ContextCompat.getColor(context, R.color.apple_blue)
            }
        } else {
            ContextCompat.getColor(context, R.color.apple_secondary_label)
        }

        if (tabIndex != MainTabsAdapter.TAB_RECORDING) {
            icon.setColorFilter(color)
        }

        // 设置缩放状态（特别处理中间录音Tab）
        if (tabIndex == MainTabsAdapter.TAB_RECORDING) {
            if (isSelected) {
                // 选中时放大
                tab.scaleX = 1.15f
                tab.scaleY = 1.15f
            } else {
                // 未选中时恢复原始大小
                tab.scaleX = 1.0f
                tab.scaleY = 1.0f
                tab.clearAnimation()
            }
        }
    }

    
    fun setOnTabSelectedListener(listener: (Int) -> Unit) {
        onTabSelectedListener = listener
    }
    
    fun getCurrentTab(): Int = currentSelectedTab
}

