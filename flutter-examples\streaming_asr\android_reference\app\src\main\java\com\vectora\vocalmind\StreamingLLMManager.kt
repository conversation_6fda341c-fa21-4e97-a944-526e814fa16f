package com.vectora.vocalmind

import android.content.Context
import android.util.Log
import com.vectora.vocalmind.server.ServerConfigManager
import com.vectora.vocalmind.server.ServerStreamingLLMManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.util.concurrent.TimeUnit

/**
 * 流式LLM管理器
 * 负责处理LLM的流式响应
 * 支持直连模式和服务器模式
 */
class StreamingLLMManager(private val context: Context) {

    companion object {
        private const val TAG = "StreamingLLMManager"

        @Volatile
        private var INSTANCE: StreamingLLMManager? = null

        fun getInstance(context: Context): StreamingLLMManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: StreamingLLMManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .readTimeout(0, TimeUnit.MILLISECONDS) // 设置为0以保持SSE连接
        .connectTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    /**
     * 发起流式聊天请求（支持会议内容和聊天历史）
     */
    fun streamChat(
        provider: LLMProvider,
        userMessage: String,
        meetingContent: String,
        chatHistory: List<ChatMessage> = emptyList(),
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始流式聊天请求 - 提供商: $provider")

        if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器流式LLM
            Log.d(TAG, "使用服务器模式进行流式聊天")
            ServerStreamingLLMManager.getInstance(context).streamChat(
                provider, userMessage, meetingContent, chatHistory, callback
            )
        } else {
            // 隐私模式：使用直连流式LLM
            Log.d(TAG, "使用隐私模式进行流式聊天")
            streamChatDirect(provider, userMessage, meetingContent, chatHistory, callback)
        }
    }

    /**
     * 发起流式聊天请求（兼容旧版本，使用prompt）
     */
    fun streamChat(
        provider: LLMProvider,
        prompt: String,
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始流式聊天请求 - 提供商: $provider, 提示长度: ${prompt.length}")

        if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：将prompt作为用户消息
            Log.d(TAG, "使用服务器模式进行流式聊天（兼容模式）")
            ServerStreamingLLMManager.getInstance(context).streamChat(
                provider, prompt, "", emptyList(), callback
            )
        } else {
            // 隐私模式：使用原有逻辑
            Log.d(TAG, "使用隐私模式进行流式聊天（兼容模式）")
            streamChatDirectWithPrompt(provider, prompt, callback)
        }
    }

    /**
     * 隐私模式的流式聊天（使用会议内容和聊天历史）
     */
    private fun streamChatDirect(
        provider: LLMProvider,
        userMessage: String,
        meetingContent: String,
        chatHistory: List<ChatMessage>,
        callback: StreamingCallback
    ) {
        try {
            val config = when (provider) {
                LLMProvider.DEEPSEEK -> DeepSeekConfig
                LLMProvider.GEMINI -> GeminiConfig
            }

            // 检查API密钥
            Log.d(TAG, "检查API密钥配置...")
            if (!config.isApiKeyConfigured(context)) {
                Log.e(TAG, "API密钥未配置 - 提供商: $provider")
                callback.onError("API密钥未配置")
                return
            }
            Log.d(TAG, "API密钥配置正常")

            // 构建完整的提示词
            val prompt = LLMManager.buildChatPromptForStreaming(userMessage, meetingContent, "", chatHistory)
            streamChatDirectWithPrompt(provider, prompt, callback)

        } catch (e: Exception) {
            Log.e(TAG, "隐私模式流式聊天异常", e)
            callback.onError("流式聊天异常: ${e.message}")
        }
    }

    /**
     * 隐私模式的流式聊天（使用prompt）
     */
    private fun streamChatDirectWithPrompt(
        provider: LLMProvider,
        prompt: String,
        callback: StreamingCallback
    ) {
        try {
            val config = when (provider) {
                LLMProvider.DEEPSEEK -> DeepSeekConfig
                LLMProvider.GEMINI -> GeminiConfig
            }

            // 检查API密钥
            if (!config.isApiKeyConfigured(context)) {
                callback.onError("API密钥未配置")
                return
            }

            // 构建请求
            Log.d(TAG, "构建请求体...")
            val requestBody = config.buildStreamingRequestBody(prompt)
            val headers = config.getStreamingHeaders(context)

            val url = when (provider) {
                LLMProvider.DEEPSEEK -> config.getApiUrl(context)
                LLMProvider.GEMINI -> (config as GeminiConfig).getStreamingApiUrl(context)
            }

            Log.d(TAG, "请求URL: $url")
            Log.d(TAG, "请求体长度: ${requestBody.length}")
            Log.d(TAG, "请求头: $headers")

            val requestBuilder = Request.Builder()
                .url(url)
                .post(requestBody.toRequestBody("application/json".toMediaType()))

            // 添加请求头
            headers.forEach { (key, value) ->
                requestBuilder.addHeader(key, value)
            }

            val request = requestBuilder.build()

            Log.d(TAG, "发起HTTP请求...")
            // 使用OkHttp发起异步请求
            okHttpClient.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "请求失败", e)
                    callback.onError("网络请求失败: ${e.message}")
                }

                override fun onResponse(call: Call, response: Response) {
                    Log.d(TAG, "收到HTTP响应: ${response.code}, 成功: ${response.isSuccessful}")
                    if (!response.isSuccessful) {
                        Log.e(TAG, "HTTP请求失败: ${response.code} - ${response.message}")
                        callback.onError("HTTP错误: ${response.code}")
                        return
                    }

                    callback.onConnectionOpened()
                    Log.d(TAG, "开始处理SSE流")

                    // 在后台线程处理SSE流
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            response.body?.let { responseBody ->
                                Log.d(TAG, "获取到响应体，开始读取流")
                                val reader = BufferedReader(InputStreamReader(responseBody.byteStream()))
                                var line: String?
                                var lineCount = 0

                                // 收集完整内容用于onCompleted回调
                                val fullContentBuilder = StringBuilder()

                                // 在协程内部重新获取config
                                val streamConfig = when (provider) {
                                    LLMProvider.DEEPSEEK -> DeepSeekConfig
                                    LLMProvider.GEMINI -> GeminiConfig
                                }

                                Log.d(TAG, "开始读取SSE行")
                                while (reader.readLine().also { line = it } != null) {
                                    lineCount++
                                    line?.let { currentLine ->
                                        Log.d(TAG, "收到SSE行 #$lineCount: $currentLine")

                                        // 处理SSE数据行
                                        if (currentLine.startsWith("data: ")) {
                                            val chunk = streamConfig.parseStreamingChunk(currentLine)
                                            if (chunk != null) {
                                                Log.d(TAG, "解析到数据块: $chunk")
                                                fullContentBuilder.append(chunk) // 收集完整内容
                                                callback.onDataReceived(chunk)
                                            } else if (currentLine.contains("[DONE]")) {
                                                Log.d(TAG, "收到[DONE]标记，结束流")
                                                reader.close()
                                                val fullContent = fullContentBuilder.toString()
                                                Log.d(TAG, "流式完成，完整内容长度: ${fullContent.length}")
                                                callback.onCompleted(fullContent)
                                                return@launch
                                            }
                                        }
                                    }
                                }

                                Log.d(TAG, "SSE流读取完成，总共读取 $lineCount 行")
                                reader.close()
                                val fullContent = fullContentBuilder.toString()
                                Log.d(TAG, "流式完成，完整内容长度: ${fullContent.length}")
                                callback.onCompleted(fullContent) // 传递完整内容
                            } ?: run {
                                Log.e(TAG, "响应体为空")
                                callback.onError("响应体为空")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "处理SSE流异常", e)
                            callback.onError("处理响应流失败: ${e.message}")
                        }
                    }
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "流式请求失败", e)
            callback.onError("请求失败: ${e.message}")
        }
    }
}