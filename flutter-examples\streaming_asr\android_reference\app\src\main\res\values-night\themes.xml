<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Apple-inspired Voice Assistant Dark Theme -->
    <style name="Theme.VoiceAssistant" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Apple Blue Primary Colors (Dark Mode) -->
        <item name="colorPrimary">@color/apple_blue_light</item>
        <item name="colorPrimaryVariant">@color/apple_blue</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Apple Green Secondary Colors (Dark Mode) -->
        <item name="colorSecondary">@color/apple_green_light</item>
        <item name="colorSecondaryVariant">@color/apple_green</item>
        <item name="colorOnSecondary">@color/black</item>

        <!-- Dark Mode Background Colors -->
        <item name="android:colorBackground">#1C1C1E</item>
        <item name="colorOnBackground">@color/white</item>
        <item name="colorSurface">#2C2C2E</item>
        <item name="colorOnSurface">@color/white</item>

        <!-- Apple Error Colors -->
        <item name="colorError">@color/apple_red_light</item>
        <item name="colorOnError">@color/white</item>

        <!-- Status Bar - Dark Mode -->
        <item name="android:statusBarColor">#1C1C1E</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:navigationBarColor">#1C1C1E</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Window Properties -->
        <item name="android:windowBackground">#1C1C1E</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>

        <!-- Text Appearance (Dark Mode) -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">#EBEBF599</item>
        <item name="android:textColorTertiary">#EBEBF54D</item>
    </style>

    <!-- Legacy Theme for Compatibility -->
    <style name="Theme.SherpaOnnx2Pass" parent="Theme.VoiceAssistant" />
<style name="Theme.SherpaOnnx2Pass.Optimized" parent="Theme.VoiceAssistant" />
</resources>
