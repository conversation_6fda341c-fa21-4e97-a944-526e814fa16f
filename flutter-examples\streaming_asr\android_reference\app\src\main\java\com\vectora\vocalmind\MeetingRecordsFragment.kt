package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.launch

/**
 * 会议记录Fragment
 * 从MeetingRecordsActivity迁移而来，用于Tab布局
 */
class MeetingRecordsFragment : Fragment() {

    companion object {
        private const val TAG = "MeetingRecordsFragment"
        
        fun newInstance(): MeetingRecordsFragment {
            return MeetingRecordsFragment()
        }
    }

    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyView: LinearLayout
    private lateinit var tvEmptyMessage: TextView
    private lateinit var btnClearAll: ImageButton
    private lateinit var btnSettings: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvRecordCount: TextView
    private lateinit var etSearch: EditText
    private lateinit var btnClearSearch: ImageButton

    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var adapter: MeetingRecordsAdapter
    private var meetingRecords = mutableListOf<MeetingRecord>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_meeting_records, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initMeetingRecordManager()
        setupRecyclerView()
        setupSearchListener()
        loadMeetingRecords()
    }

    private fun initViews(view: View) {
        recyclerView = view.findViewById(R.id.recycler_meeting_records)
        emptyView = view.findViewById(R.id.ll_empty_view)
        tvEmptyMessage = view.findViewById(R.id.tv_empty_message)
        btnClearAll = view.findViewById(R.id.btn_clear_all)
        btnSettings = view.findViewById(R.id.btn_settings)
        tvTitle = view.findViewById(R.id.tv_title)
        tvRecordCount = view.findViewById(R.id.tv_record_count)
        etSearch = view.findViewById(R.id.et_search)
        btnClearSearch = view.findViewById(R.id.btn_clear_search)

        // 设置点击事件
        btnClearAll.setOnClickListener { showClearAllDialog() }
        btnClearSearch.setOnClickListener { clearSearch() }
        btnSettings.setOnClickListener { openSettings() }

        // 设置标题
        tvTitle.text = "📝 历史记录"
    }

    private fun initMeetingRecordManager() {
        meetingRecordManager = MeetingRecordManager.getInstance(requireContext())
    }

    private fun setupRecyclerView() {
        adapter = MeetingRecordsAdapter(meetingRecords) { meetingRecord ->
            openMeetingDetail(meetingRecord)
        }
        
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = adapter
    }

    private fun setupSearchListener() {
        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s?.toString()?.trim() ?: ""
                adapter.filter(query)
                updateUI()
                
                // 显示/隐藏清除搜索按钮
                btnClearSearch.visibility = if (query.isNotEmpty()) View.VISIBLE else View.GONE
            }
        })
    }

    private fun loadMeetingRecords() {
        try {
            val allRecords = meetingRecordManager.getAllRecords()
            meetingRecords.clear()
            meetingRecords.addAll(allRecords)
            
            adapter.updateData(allRecords)
            updateUI()
            
            Log.d(TAG, "加载了 ${meetingRecords.size} 条会议记录")
        } catch (e: Exception) {
            Log.e(TAG, "加载会议记录失败", e)
            showToast("加载会议记录失败: ${e.message}")
        }
    }

    private fun updateUI() {
        val totalCount = adapter.getOriginalCount()
        val filteredCount = adapter.getFilteredCount()
        val isSearching = etSearch.text.toString().isNotEmpty()
        
        if (filteredCount == 0) {
            recyclerView.visibility = View.GONE
            emptyView.visibility = View.VISIBLE
            
            if (isSearching) {
                tvEmptyMessage.text = "未找到匹配的会议记录\n\n请尝试其他关键词"
            } else {
                tvEmptyMessage.text = "暂无会议记录\n\n开始录音后会自动保存会议记录"
            }
            
            btnClearAll.visibility = View.GONE
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyView.visibility = View.GONE
            btnClearAll.visibility = if (totalCount > 0) View.VISIBLE else View.GONE
        }
        
        // 更新记录数量显示
        tvRecordCount.text = if (isSearching && filteredCount != totalCount) {
            "找到 $filteredCount 条记录（共 $totalCount 条）"
        } else {
            "$totalCount 条记录"
        }
    }

    private fun clearSearch() {
        etSearch.setText("")
        btnClearSearch.visibility = View.GONE
    }

    private fun showClearAllDialog() {
        AppleInfoDialog(
            context = requireContext(),
            title = "清空所有记录",
            message = "确定要删除所有会议记录吗？\n\n此操作不可撤销，所有会议记录和相关的音频文件都将被永久删除。",
            positiveButtonText = "确定删除",
            negativeButtonText = "取消",
            onPositiveClick = {
                clearAllRecords()
            }
        ).show()
    }

    private fun clearAllRecords() {
        lifecycleScope.launch {
            try {
                val success = meetingRecordManager.clearAllRecords()
                if (success) {
                    loadMeetingRecords()
                    showToast("所有会议记录已清空")
                } else {
                    showToast("清空失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "清空会议记录失败", e)
                showToast("清空失败: ${e.message}")
            }
        }
    }

    private fun openMeetingDetail(meetingRecord: MeetingRecord) {
        try {
            val intent = Intent(requireContext(), MeetingDetailActivity::class.java)
            intent.putExtra("meeting_record_id", meetingRecord.id)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开会议详情失败", e)
            showToast("打开会议详情失败: ${e.message}")
        }
    }

    override fun onResume() {
        super.onResume()
        // 重新加载数据，以防其他地方有更新
        loadMeetingRecords()
    }

    private fun openSettings() {
        (activity as? SingleModelActivity)?.openSettings()
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
}
