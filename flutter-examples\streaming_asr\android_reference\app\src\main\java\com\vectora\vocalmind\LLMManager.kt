package com.vectora.vocalmind

import android.content.Context
import android.util.Log
import com.vectora.vocalmind.server.ServerConfigManager
import com.vectora.vocalmind.server.ServerLLMProvider
import com.vectora.vocalmind.server.ServerStreamingLLMManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL

/**
 * 统一的LLM管理器
 * 管理所有LLM提供商，提供统一的API调用接口
 * 支持直连模式和服务器模式
 */
object LLMManager {
    private const val TAG = "LLMManager"

    // 所有支持的LLM配置
    private val llmConfigs = mapOf(
        LLMProvider.GEMINI to GeminiConfig,
        LLMProvider.DEEPSEEK to DeepSeekConfig
    )
    
    /**
     * 获取当前选择的LLM配置
     */
    fun getCurrentLLMConfig(context: Context): LLMConfig {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(context)
        return llmConfigs[currentProvider] ?: GeminiConfig
    }
    
    /**
     * 获取指定提供商的LLM配置
     */
    fun getLLMConfig(provider: LLMProvider): LLMConfig {
        return llmConfigs[provider] ?: GeminiConfig
    }
    
    /**
     * 检查当前LLM是否可用
     */
    suspend fun isCurrentLLMAvailable(context: Context): Boolean {
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：检查服务器LLM可用性
            ServerLLMProvider.getInstance(context).isAvailable()
        } else {
            // 隐私模式：检查API密钥配置
            val config = getCurrentLLMConfig(context)
            config.isApiKeyConfigured(context)
        }
    }

    /**
     * 检查指定LLM是否可用
     */
    suspend fun isLLMAvailable(context: Context, provider: LLMProvider): Boolean {
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：检查服务器LLM可用性
            ServerLLMProvider.getInstance(context).isAvailable()
        } else {
            // 隐私模式：检查API密钥配置
            val config = getLLMConfig(provider)
            config.isApiKeyConfigured(context)
        }
    }
    
    /**
     * 使用当前LLM进行ASR内容优化
     */
    suspend fun optimizeAsrContent(context: Context, originalContent: String): LLMResult {
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器LLM
            ServerLLMProvider.getInstance(context).optimizeAsrContent(originalContent)
        } else {
            // 隐私模式：使用直连LLM
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
                )
            }

            val prompt = buildAsrOptimizationPrompt(originalContent)
            callLLMAPI(context, config, prompt)
        }
    }

    /**
     * 选择最佳内容：优先使用ASR优化内容，如果没有则使用原始内容
     */
    private fun selectBestContent(originalContent: String, optimizedContent: String): String {
        return if (optimizedContent.isNotBlank()) {
            Log.d(TAG, "使用ASR优化内容进行LLM调用")
            optimizedContent
        } else {
            Log.d(TAG, "ASR优化内容为空，使用原始转录内容进行LLM调用")
            originalContent
        }
    }

    /**
     * 使用当前LLM生成指定场景的总结（流式）
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    fun generateSummaryStream(
        context: Context,
        originalContent: String,
        optimizedContent: String,
        summaryType: SummaryType,
        callback: StreamingCallback
    ) {
        // 选择最佳内容
        val speechContent = selectBestContent(originalContent, optimizedContent)
        Log.d(TAG, "开始流式总结生成 - 类型: ${summaryType.displayName}")

        if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器流式LLM
            Log.d(TAG, "使用服务器模式进行流式总结")
            val summaryTypeStr = when (summaryType) {
                SummaryType.MEETING -> "detailed"
                SummaryType.BRIEF -> "brief"
                SummaryType.DETAILED -> "detailed"
                SummaryType.ACTION_ITEMS -> "action_items"
                SummaryType.DECISIONS -> "decisions"
                SummaryType.INTERVIEW -> "detailed"
                SummaryType.LECTURE -> "detailed"
                SummaryType.SPEECH -> "detailed"
                SummaryType.INTERVIEW_MEDIA -> "detailed"
                SummaryType.DISCUSSION -> "detailed"
                SummaryType.TRAINING -> "detailed"
                SummaryType.PRESENTATION -> "detailed"
                SummaryType.DIARY -> "brief"
                SummaryType.DEV_LOG -> "detailed"
            }
            ServerStreamingLLMManager.getInstance(context).streamSummary(
                speechContent, summaryTypeStr, callback
            )
        } else {
            // 隐私模式：使用直连流式LLM
            Log.d(TAG, "使用隐私模式进行流式总结")
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                callback.onError("当前LLM (${config.getProvider().displayName}) 的API密钥未配置")
                return
            }

            val prompt = buildSummaryPrompt(speechContent, summaryType)
            val provider = config.getProvider()

            StreamingLLMManager.getInstance(context).streamChat(
                provider, prompt, callback
            )
        }
    }

    /**
     * 使用当前LLM进行ASR内容优化（流式）
     */
    fun optimizeAsrContentStream(
        context: Context,
        originalContent: String,
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始流式ASR内容优化")

        if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器流式LLM
            Log.d(TAG, "使用服务器模式进行流式ASR优化")
            ServerStreamingLLMManager.getInstance(context).streamOptimizeContent(
                originalContent, callback
            )
        } else {
            // 隐私模式：使用直连流式LLM
            Log.d(TAG, "使用隐私模式进行流式ASR优化")
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                callback.onError("当前LLM (${config.getProvider().displayName}) 的API密钥未配置")
                return
            }

            val prompt = buildAsrOptimizationPrompt(originalContent)
            val provider = config.getProvider()

            StreamingLLMManager.getInstance(context).streamChat(
                provider, prompt, callback
            )
        }
    }

    /**
     * 使用当前LLM生成指定类型的Mermaid图表（流式）
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    fun generateMermaidDiagramStream(
        context: Context,
        originalContent: String,
        optimizedContent: String,
        mermaidType: MermaidType,
        callback: StreamingCallback
    ) {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        Log.d(TAG, "开始流式Mermaid图表生成 - 类型: ${mermaidType.displayName}")

        if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器流式LLM
            Log.d(TAG, "使用服务器模式进行流式Mermaid生成")
            val mermaidTypeStr = when (mermaidType) {
                MermaidType.MINDMAP -> "mindmap"
                MermaidType.FLOWCHART_TD -> "flowchart_td"
            }
            ServerStreamingLLMManager.getInstance(context).streamMermaidGeneration(
                meetingContent, mermaidTypeStr, callback
            )
        } else {
            // 隐私模式：使用直连流式LLM
            Log.d(TAG, "使用隐私模式进行流式Mermaid生成")
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                callback.onError("当前LLM (${config.getProvider().displayName}) 的API密钥未配置")
                return
            }

            val prompt = buildMermaidPrompt(meetingContent, mermaidType)
            val provider = config.getProvider()

            StreamingLLMManager.getInstance(context).streamChat(
                provider, prompt, callback
            )
        }
    }

    /**
     * 核心API调用方法
     */
    private suspend fun callLLMAPI(context: Context, config: LLMConfig, prompt: String): LLMResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "使用 ${config.getProvider().displayName} 进行API调用")
                
                val url = URL(config.getApiUrl(context))
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                
                // 设置请求头
                val headers = config.getHeaders(context)
                headers.forEach { (key, value) ->
                    connection.setRequestProperty(key, value)
                }
                
                // 构建请求体
                val requestBody = config.buildRequestBody(prompt)
                
                // 发送请求
                val writer = OutputStreamWriter(connection.outputStream)
                writer.write(requestBody)
                writer.flush()
                writer.close()
                
                // 读取响应
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val reader = BufferedReader(InputStreamReader(connection.inputStream))
                    val response = reader.readText()
                    reader.close()
                    
                    // 解析响应
                    val content = config.parseResponse(response)
                    Log.d(TAG, "${config.getProvider().displayName} API调用成功")
                    
                    LLMResult(success = true, content = content)
                } else {
                    val errorReader = BufferedReader(InputStreamReader(connection.errorStream))
                    val errorResponse = errorReader.readText()
                    errorReader.close()
                    
                    val errorMsg = "${config.getProvider().displayName} API调用失败: $responseCode - $errorResponse"
                    Log.e(TAG, errorMsg)
                    
                    LLMResult(success = false, content = "", error = errorMsg)
                }
            } catch (e: Exception) {
                val errorMsg = "${config.getProvider().displayName} API调用异常: ${e.message}"
                Log.e(TAG, errorMsg, e)
                LLMResult(success = false, content = "", error = errorMsg)
            }
        }
    }
    
    /**
     * 构建ASR优化提示词
     */
    fun buildAsrOptimizationPrompt(originalContent: String): String {
        // 分析内容格式，提供更精准的处理策略
        val formatAnalysis = analyzeContentFormat(originalContent)

        return """
            请对以下语音识别(ASR)内容进行优化和修正，要求：

            1. **语义准确性**：修正明显的识别错误，如同音字错误、语音识别常见错误
            2. **语言流畅性**：优化语句结构，使表达更加自然流畅
            3. **逻辑合理性**：整理语序，确保逻辑清晰、前后连贯
            4. **保持原意**：完全保持原始含义和重要信息，不添加、不删除关键内容
            5. **标点符号**：添加合适的标点符号，提高可读性
            6. **说话人分段**：将同一说话人的连续发言合并为一个段落
            6. **英文格式化**: 英文使用合适的大小写、标点符号

            **输出格式要求**：
            - 只合并**连续的**相同说话人发言，保持对话的时间顺序
            - 格式：[说话人姓名]：完整的连续发言内容。
            - 不同说话人之间用空行分隔
            - 如果同一说话人在对话中多次发言但被其他人打断，则分别保留为独立段落
            - 如果没有说话人标识，则按语义和逻辑分段，每段用空行分隔
            - 移除时间戳等技术信息
            - 避免原始的一句话一行的日志格式
            - 整合碎片化的句子为连贯段落

            **内容分析**：
            $formatAnalysis

            **处理策略**：
            - 将**连续的**同一说话人的多行发言合并为一个自然段落
            - 修正语音识别常见错误（同音字、语法错误等）
            - 保持口语化特点，不要过度书面化
            - 确保段落间逻辑连贯，语义完整
            - 严格按照时间顺序，不要重新排列说话人的发言顺序

            **示例说明**：
            原始内容：
            [张三] HELLO EVERYONE
            [张三] 今天我们讨论项目
            [李四] 好的
            [李四] 我先汇报
            [张三] WAIT A MINUTE            
            [张三] 我还有补充

            正确输出：
            [张三]：Hello everyone, 今天我们讨论项目。

            [李四]：好的，我先汇报。

            [张三]：Wait a minute, 我还有补充。

            原始ASR内容：
            $originalContent

            请按照上述格式要求输出优化后的内容：
        """.trimIndent()
    }
    
    /**
     * 分析内容格式，提供精准的处理策略
     */
    fun analyzeContentFormat(content: String): String {
        val lines = content.split('\n').filter { it.trim().isNotEmpty() }
        val hasSpeakerInfo = lines.any { it.contains(":") && (it.contains("[") || it.contains("说话人")) }
        val hasTimestamp = lines.any { it.matches(Regex(".*\\d{2}:\\d{2}:\\d{2}.*")) }
        val avgLineLength = if (lines.isNotEmpty()) lines.map { it.length }.average() else 0.0
        val fragmentedSentences = lines.count { it.trim().length < 10 }

        return buildString {
            append("内容特征：")
            if (hasSpeakerInfo) append("包含说话人标识，")
            if (hasTimestamp) append("包含时间戳，")
            append("共${lines.size}行，")
            append("平均行长度${avgLineLength.toInt()}字符")
            if (fragmentedSentences > lines.size / 3) {
                append("，存在较多碎片化句子")
            }
            append("。")
        }
    }

    /**
     * 构建不同场景的总结提示词
     */
    fun buildSummaryPrompt(speechContent: String, summaryType: SummaryType): String {
        return when (summaryType) {
            SummaryType.MEETING -> """
                请对以下会议内容进行总结，要求：
                1. **会议概述**：简要概括会议主题和目的
                2. **关键要点**：提取讨论的核心内容和重要观点
                3. **决策事项**：列出会议中达成的决定和共识
                4. **行动项目**：整理具体的任务分配和责任人（如果有）
                5. **后续安排**：记录下次会议时间或其他安排（如果有）
                6. 用中文回复，使用清晰的标题和要点格式

                会议内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.INTERVIEW -> """
                请对以下面试内容进行总结，要求：
                1. **面试概况**：面试职位、面试官和候选人基本信息
                2. **问题回答**：整理主要面试问题和候选人回答要点
                3. **技能评估**：总结候选人展示的专业技能和经验
                4. **个人特质**：记录候选人的性格特点和沟通能力
                5. **面试官评价**：提取面试官的评价和建议（如果有）
                6. **综合印象**：整体评估和是否推荐录用的倾向
                7. 用中文回复，客观公正地记录面试内容

                面试内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.LECTURE -> """
                请对以下课堂内容进行总结，要求：
                1. **课程信息**：课程名称、授课老师、课程主题
                2. **知识要点**：提取课堂讲解的核心知识点
                3. **重点概念**：整理重要的概念、定义和原理
                4. **案例分析**：总结课堂中的实例和案例说明
                5. **课堂互动**：记录师生问答和讨论内容
                6. **作业任务**：整理布置的作业或课后任务（如果有）
                7. **学习建议**：提取老师给出的学习方法和建议
                8. 用中文回复，便于课后复习和理解

                课堂内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.SPEECH -> """
                请对以下演讲内容进行总结，要求：
                1. **演讲主题**：明确演讲的核心主题和目的
                2. **核心观点**：提取演讲者的主要观点和论述
                3. **论证逻辑**：整理演讲的逻辑结构和论证过程
                4. **精彩语录**：记录有影响力的金句和名言
                5. **案例故事**：总结演讲中的案例、故事和实例
                6. **行动号召**：提取演讲者的呼吁和建议
                7. **演讲技巧**：记录值得学习的演讲技巧和表达方式
                8. 用中文回复，突出演讲的感染力和说服力

                演讲内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.INTERVIEW_MEDIA -> """
                请对以下访谈内容进行总结，要求：
                1. **访谈背景**：访谈主题、受访者身份和访谈目的
                2. **核心话题**：整理访谈涉及的主要话题和议题
                3. **观点立场**：总结受访者的观点、态度和立场
                4. **重要信息**：提取访谈中透露的关键信息和内幕
                5. **争议焦点**：记录有争议或敏感的话题讨论
                6. **个人见解**：整理受访者的独特见解和经验分享
                7. **社会意义**：分析访谈内容的社会价值和影响
                8. 用中文回复，保持客观中立的记录态度

                访谈内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.DISCUSSION -> """
                请对以下讨论内容进行总结，要求：
                1. **讨论主题**：明确讨论的核心话题和背景
                2. **参与者观点**：整理各参与者的不同观点和立场
                3. **争论焦点**：识别讨论中的分歧点和争议话题
                4. **共识内容**：总结大家达成一致的观点和结论
                5. **解决方案**：整理提出的解决方案和建议
                6. **未决问题**：记录仍需进一步讨论的问题
                7. **讨论价值**：评估讨论的成果和意义
                8. 用中文回复，平衡展现不同观点

                讨论内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.TRAINING -> """
                请对以下培训内容进行总结，要求：
                1. **培训概况**：培训主题、讲师信息和培训目标
                2. **核心内容**：提取培训的主要知识点和技能要求
                3. **操作步骤**：整理具体的操作流程和实践方法
                4. **注意事项**：记录重要的注意点和常见错误
                5. **实践练习**：总结培训中的练习环节和案例操作
                6. **考核要求**：整理培训考核标准和评估方式（如果有）
                7. **应用建议**：提取讲师给出的实际应用建议
                8. 用中文回复，便于后续实践和应用

                培训内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.PRESENTATION -> """
                请对以下汇报内容进行总结，要求：
                1. **汇报主题**：明确汇报的主要内容和目的
                2. **关键数据**：提取汇报中的重要数据和指标
                3. **成果展示**：总结取得的成绩和完成的工作
                4. **问题分析**：整理发现的问题和面临的挑战
                5. **解决方案**：记录提出的改进措施和解决方案
                6. **未来规划**：总结下一步的工作计划和目标
                7. **资源需求**：整理需要的支持和资源配置
                8. 用中文回复，突出汇报的逻辑性和完整性

                汇报内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.DIARY -> """
                请对以下日记内容进行总结，要求：
                1. **日期时间**：记录日记的时间和地点信息
                2. **主要事件**：总结当天发生的重要事情和活动
                3. **情感体验**：整理记录者的情感变化和心理感受
                4. **思考感悟**：提取个人的思考、感悟和领悟
                5. **人际互动**：记录与他人的交流和互动情况
                6. **学习成长**：总结学到的知识和个人成长体验
                7. **计划展望**：整理对未来的计划和期望
                8. 用中文回复，保持温暖真实的记录风格

                日记内容：
                $speechContent
            """.trimIndent()
            
            SummaryType.DEV_LOG -> """
                请对以下开发日志内容进行总结，要求：
                1. **开发概况**：项目名称、开发阶段和当前进度
                2. **技术要点**：记录使用的技术栈、框架和工具
                3. **功能实现**：总结完成的功能模块和代码逻辑
                4. **问题解决**：整理遇到的技术问题和解决方案
                5. **代码优化**：记录代码重构、性能优化的内容
                6. **测试调试**：总结测试过程和bug修复情况
                7. **学习收获**：提取新学到的技术知识和经验
                8. **下步计划**：整理后续的开发任务和技术规划
                9. 用中文回复，突出技术细节和开发思路

                开发日志内容：
                $speechContent
            """.trimIndent()

            SummaryType.BRIEF -> """
                请对以下内容进行简要总结，要求：
                1. **核心要点**：提取最重要的信息
                2. **关键结论**：总结主要结论和决策
                3. 用中文回复，保持简洁明了

                内容：
                $speechContent
            """.trimIndent()

            SummaryType.DETAILED -> """
                请对以下内容进行详细总结，要求：
                1. **详细分析**：深入分析各个方面
                2. **完整记录**：保留重要细节和背景信息
                3. **结构化呈现**：使用清晰的层次结构
                4. 用中文回复，内容详尽

                内容：
                $speechContent
            """.trimIndent()

            SummaryType.ACTION_ITEMS -> """
                请从以下内容中提取行动项和待办事项，要求：
                1. **任务清单**：列出具体的行动项目
                2. **责任分配**：标明负责人（如果有）
                3. **时间要求**：注明截止时间或时间安排（如果有）
                4. **优先级**：按重要性排序
                5. 用中文回复，格式清晰

                内容：
                $speechContent
            """.trimIndent()

            SummaryType.DECISIONS -> """
                请从以下内容中提取重要决策和结论，要求：
                1. **决策要点**：列出所有重要决定
                2. **决策依据**：说明决策的理由和背景
                3. **影响范围**：分析决策的影响
                4. **执行要求**：说明如何执行这些决策
                5. 用中文回复，逻辑清晰

                内容：
                $speechContent
            """.trimIndent()
        }
    }

    /**
     * 构建指定类型的Mermaid图表提示词（公共方法，供服务器模式使用）
     */
    fun buildMermaidPrompt(meetingContent: String, mermaidType: MermaidType): String {
        return when (mermaidType) {
            MermaidType.MINDMAP -> """
                请基于以下内容生成一个详细的Mermaid思维导图，要求：
                
                **重要提示**：由于思维导图支持缩放功能，用户可以放大查看详细内容，因此节点文本可以包含更多信息，无需担心显示问题。
                
                1. **图表类型**：
                   - 使用flowchart LR语法（从左到右的流程图）
                   - 以会议主题为根节点，从左侧开始
                   - 按照逻辑关系组织分支，形成从左到右的树形结构
                   - 使用不同的节点形状表示不同层级
                   
                2. **内容要求**（重点优化）：
                   - 提取会议的关键主题、要点、决策、行动项和详细信息
                   - 根据节点层级设置不同的文本长度：
                     * 根节点：5-10个字的简洁主题描述
                     * 一级分支：5-10个字的分类标题
                     * 二级分支：5-10个字的要点概述
                     * 最终节点：10-30个字的详细描述，充分利用缩放功能
                   - 体现清晰的层次关系：主题→分类→具体内容→详细说明
                   - 突出重要的决策和结论，并包含相关的背景信息
                   - 合理分组，每个分支应包含丰富的信息层次
                   - 最终节点包含具体的数据、时间、人员、方法等详细信息
                   
                3. **节点文本详细化要求**：
                   - 根节点：简洁的主题概括，如"技术方案总结"、"项目要点分析"
                   - 一级分支：简洁的分类标题，
                   - 二级分支：要点概述，
                   - 最终节点：详细的具体内容、关键信息、重要细节，包含完整描述
                   - 只有最终节点使用详细的描述性语句，其他层级保持简洁明了
                   
                4. **节点样式要求**：
                   - 根节点使用方括号 A[详细主题描述] - 蓝色主题色
                   - 一级分支使用方括号 B[详细分类说明] - 紫色分类色
                   - 二级分支使用方括号 C[具体内容描述] - 绿色内容色
                   - 重要节点使用方括号 D[重要决策详情] - 红色强调色
                   - 统一使用矩形节点，不使用菱形、圆形等其他形状                   
                   
                5. **样式定义**：
                   - 必须在图表末尾添加classDef样式定义
                   - 根节点：蓝色主题 (#4a90e2) 白色文字，粗体
                   - 一级分支：紫色主题 (#7b68ee) 白色文字，半粗体
                   - 二级分支：绿色主题 (#50c878) 白色文字
                   - 重要节点：红色主题 (#ff6b6b) 白色文字，半粗体
                   - 统一使用圆角矩形，保持视觉一致性
                   
                6. **语法要求**（重要）：
                   - 只输出Mermaid内容，不要代码标记，不要其他说明文字
                   - 严格使用标准Mermaid flowchart LR语法
                   - 确保节点ID唯一且简洁（如A、B1、B2、C1等）
                   - 节点文本可以较长，但要确保不包含特殊字符
                   - 避免在节点文本中使用换行符，保持文本连贯性，充分利用缩放功能查看详细内容
                   
                7. **详细示例格式**：
                flowchart LR
                    A["内容要点总结"] --> B1["核心观点"]
                    A --> B2["关键信息"]
                    A --> B3["重要细节"]
                    A --> B4["补充说明"]
                    
                    B1 --> C1["主要论点"]
                    B1 --> C2["核心概念"]
                    B1 --> C3["基本原理"]
                    
                    B2 --> C4["关键的数据信息"]
                    B2 --> C5["重要事实信息"]
                    B2 --> C6["核心关键指标"]
                    
                    B3 --> C7["具体方法"]
                    B3 --> C8["实施步骤"]
                    B3 --> C9["操作要点"]
                    
                    B4 --> C10["注意事项"]
                    B4 --> C11["相关背景"]
                    B4 --> C12["延伸思考"]
                    
                    C1 --> D1["详细阐述主要观点的核心内容，包含具体的论证逻辑、支撑材料和关键证据，确保论点清晰完整"]
                    C2 --> D2["深入解释核心概念的定义、特征和应用范围，提供具体的实例说明和理论依据"]
                    C3 --> D3["全面介绍基本原理的运作机制、适用条件和实际效果，包含相关的科学依据"]
                    
                    C4 --> D4["提供准确的数据信息，包含具体数值、统计结果、趋势分析和数据来源说明"]
                    C5 --> D5["列举重要的事实信息，包含时间、地点、人物、事件经过和影响结果"]
                    C6 --> D6["说明关键指标的计算方法、评估标准、目标值和实际表现情况"]
                    
                    C7 --> D7["详细描述具体的实施方法，包含操作流程、技术要求、工具使用和质量控制措施"]
                    C8 --> D8["明确各个实施步骤的先后顺序、时间安排、责任分工和完成标准"]
                    C9 --> D9["强调操作过程中的关键要点，包含注意事项、常见问题和解决方案"]
                    
                    classDef rootNode fill:#4a90e2,stroke:#2c5aa0,stroke-width:3px,color:#fff,border-radius:12px,font-weight:bold
                    classDef categoryNode fill:#7b68ee,stroke:#5a4fcf,stroke-width:2px,color:#fff,border-radius:10px,font-weight:600
                    classDef contentNode fill:#50c878,stroke:#3a9b5c,stroke-width:2px,color:#fff,border-radius:8px
                    classDef importantNode fill:#ff6b6b,stroke:#e55555,stroke-width:3px,color:#fff,border-radius:10px,font-weight:600
                    
                    class A rootNode
                    class B1,B2,B3,B4 categoryNode
                    class C1,C2,C3,C4,C5,C7,C8,C9,C10,C11,C12 contentNode
                    class C6 importantNode
                      
                会议内容：
                $meetingContent
                
                请严格按照上述要求生成详细的Mermaid代码，确保每个节点都包含丰富的信息和具体细节：
            """.trimIndent()
            
            MermaidType.FLOWCHART_TD -> """
                请基于以下内容生成一个Mermaid流程图，要求：
                
                1. **图表类型选择**：
                   - 使用flowchart TD（自上而下流程图）
                   - 如果是决策过程，使用flowchart TD
                   - 如果是时间序列，使用flowchart TD
                   - 如果是并行流程，使用flowchart LR（左右布局）
                   
                2. **内容要求**：                   
                   - 节点名称简洁明了，使用中文
                   - 体现逻辑关系和流程顺序
                   - 突出重要的决策节点和结果
                   
                3. **语法要求**（重要）：
                   - 只输出Mermaid内容，不要代码标记，不要其他说明文字
                   - 严格使用标准Mermaid语法
                   - 节点ID必须使用英文字母和数字，不能包含中文
                   - 节点文本使用方括号[]包围，决策节点使用花括号{}
                   - 连接线标签使用|文本|格式
                   - 避免使用特殊字符，如引号、尖括号等
                   
                4. **示例格式**：            
                flowchart TD
                    A[开始] --> B{是否同意方案}
                    B -->|同意| C[执行计划]
                    B -->|不同意| D[重新讨论]
                    C --> E[完成]
                    D --> B
                    
                5. **注意事项**：
                   - 确保所有节点ID唯一
                   - 连接关系清晰明确
                   - 避免循环引用导致的渲染问题
                   - 文本内容不要包含HTML标签
                            
                会议内容：
                $meetingContent
                
                请严格按照上述要求生成Mermaid代码：
            """.trimIndent()
        }
    }
    
    /**
     * 生成会议标题
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateMeetingTitle(context: Context, originalContent: String, optimizedContent: String): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器LLM
            ServerLLMProvider.getInstance(context).generateMeetingTitle(originalContent, optimizedContent)
        } else {
            // 隐私模式：使用直连LLM
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
                )
            }

            val prompt = buildMeetingTitlePrompt(meetingContent)
            callLLMAPI(context, config, prompt)
        }
    }

    /**
     * 生成TODO待办事项
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateTodos(context: Context, originalContent: String, optimizedContent: String, meetingTitle: String = ""): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器LLM
            ServerLLMProvider.getInstance(context).generateTodos(originalContent, optimizedContent, meetingTitle)
        } else {
            // 隐私模式：使用直连LLM
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
                )
            }

            val prompt = buildTodoGenerationPrompt(meetingContent, meetingTitle)
            callLLMAPI(context, config, prompt)
        }
    }

    /**
     * 生成会议标签
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateTags(context: Context, originalContent: String, optimizedContent: String): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器LLM
            ServerLLMProvider.getInstance(context).generateTags(meetingContent)
        } else {
            // 隐私模式：使用直连LLM
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
                )
            }

            val prompt = buildTagGenerationPrompt(meetingContent)
            callLLMAPI(context, config, prompt)
        }
    }

    /**
     * 同时生成会议标题和标签
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     * 返回JSON格式的结果，包含title和tags字段
     */
    suspend fun generateTitleAndTags(context: Context, originalContent: String, optimizedContent: String): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        return if (ServerConfigManager.isUseServerMode(context)) {
            // 服务器模式：使用服务器LLM
            ServerLLMProvider.getInstance(context).generateTitleAndTags(originalContent, optimizedContent)
        } else {
            // 隐私模式：使用直连LLM
            val config = getCurrentLLMConfig(context)

            if (!config.isApiKeyConfigured(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "当前LLM (${config.getProvider().displayName}) 的API密钥未配置"
                )
            }

            val prompt = buildTitleAndTagsPrompt(meetingContent)
            callLLMAPI(context, config, prompt)
        }
    }
    
    /**
     * 构建会议标题生成提示词
     */
    fun buildMeetingTitlePrompt(meetingContent: String): String {
        return """
            请为以下会议内容生成一个简洁明了的标题，要求：
            1. 标题长度控制在10-20个字符
            2. 准确概括会议的主要内容或主题
            3. 使用中文
            4. 不要包含时间、日期等信息
            5. 只输出标题文本，不要其他说明
            
            会议内容：
            ${meetingContent.take(1000)}
            
            标题：
        """.trimIndent()
    }
    
    /**
     * 构建聊天对话提示词
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    fun buildChatPrompt(userMessage: String, originalContent: String, optimizedContent: String, chatHistory: List<ChatMessage> = emptyList()): String {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        val chatHistoryText = if (chatHistory.isNotEmpty()) {
            val historyBuilder = StringBuilder()
            historyBuilder.append("\n**聊天历史：**\n")
            chatHistory.takeLast(10).forEach { message ->
                val sender = if (message.isFromUser) "用户" else "助手"
                historyBuilder.append("$sender: ${message.content}\n")
            }
            historyBuilder.toString()
        } else {
            ""
        }
        
        return """
            你是一个智能会议助手，请基于以下会议记录内容和聊天历史回答用户的问题。
            
            **会议记录内容：**
            $meetingContent$chatHistoryText
            
            **用户问题：**
            $userMessage
            
            **回答要求：**
            1. 基于会议记录内容和聊天历史进行回答，确保信息准确
            2. 如果问题与会议内容无关，请礼貌地说明并引导用户提问相关内容
            3. 回答要简洁明了，重点突出
            4. 用中文回复
            5. 如果会议记录中没有相关信息，请明确说明
            6. 参考聊天历史中的上下文，保持对话的连贯性
            
            请回答：
        """.trimIndent()
    }

    /**
     * 获取当前LLM提供商
     */
    fun getCurrentProvider(context: Context): LLMProvider {
        return LLMApiKeyManager.getCurrentProvider(context)
    }

    /**
     * 清理Mermaid代码中的代码标记
     * 移除可能存在的 ```mermaid 和 ``` 标记
     */
    fun cleanMermaidCodeMarkers(mermaidCode: String): String {
        return mermaidCode.trim()
            // 移除开头的代码标记
            .replace(Regex("^```\\s*mermaid\\s*\\n?", RegexOption.IGNORE_CASE), "")
            .replace(Regex("^```\\s*\\n?", RegexOption.IGNORE_CASE), "")
            // 移除结尾的代码标记
            .replace(Regex("\\n?\\s*```\\s*$"), "")
            .trim()
    }

    /**
     * 构建聊天提示词（用于StreamingLLMManager）
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    fun buildChatPromptForStreaming(userMessage: String, originalContent: String, optimizedContent: String, chatHistory: List<ChatMessage>): String {
        return buildChatPrompt(userMessage, originalContent, optimizedContent, chatHistory)
    }

    /**
     * 构建Mermaid图表提示词（供服务器模式使用，字符串类型参数）
     */
    fun buildMermaidPrompt(meetingContent: String, mermaidTypeStr: String): String {
        val mermaidType = when (mermaidTypeStr) {
            "mindmap" -> MermaidType.MINDMAP
            "flowchart_td" -> MermaidType.FLOWCHART_TD
            else -> MermaidType.FLOWCHART_TD
        }
        return buildMermaidPrompt(meetingContent, mermaidType)
    }

    /**
     * 构建标签生成提示词
     */
    fun buildTagGenerationPrompt(meetingContent: String): String {
        return """
        请基于以下 ASR 转录文本，生成 1–5 个简洁标签，用于快速识别内容的主题和类型。

        要求：
        1. 仅生成 1–5 个标签，每个标签 2–6 个字
        2. 标签应准确概括文本的主要内容、场景或主题
        3. 优先选择具体业务领域、项目名称或内容类型
        4. 标签需简短明了，避免空格与特殊符号
        5. 仅使用中文
        6. 仅返回标签，使用逗号分隔，不要其他说明文字

        示例输出：
        学习感悟,沟通技巧,领导力

        转录文本：
        $meetingContent

        请生成标签：
        """.trimIndent()
    }

    /**
     * 构建标题和标签同时生成的提示词
     */
    fun buildTitleAndTagsPrompt(meetingContent: String): String {
        return """
        请基于以下会议内容，同时生成一个简洁的标题和相关标签。

        要求：
        1. 标题要求：
           - 长度控制在10-20个字符
           - 准确概括会议的主要内容或主题
           - 使用中文，不要包含时间、日期等信息

        2. 标签要求：
           - 生成1-5个标签，每个标签2-6个字
           - 标签应准确概括文本的主要内容、场景或主题
           - 优先选择具体业务领域、项目名称或内容类型
           - 仅使用中文，避免空格与特殊符号

        3. 输出格式：
           请严格按照以下JSON格式输出，不要包含其他说明文字：
           {
             "title": "会议标题",
             "tags": ["标签1", "标签2", "标签3"]
           }

        会议内容：
        ${meetingContent.take(1000)}

        请生成JSON结果：
        """.trimIndent()
    }

    /**
     * 构建TODO生成提示词
     */
    fun buildTodoGenerationPrompt(meetingContent: String, meetingTitle: String): String {
        // 获取当前系统时间信息
        val currentTime = getCurrentTimeContext()

        return """
        请分析以下会议记录内容，提取出具体的待办事项和行动项。

        当前系统时间信息：
        $currentTime

        会议标题：$meetingTitle
        
        分析要求：
        1. 只提取明确的任务和行动项，不要包含讨论内容
        2. 每个待办事项应该是具体可执行的
        3. 根据重要性和紧急程度设置优先级：
           - 紧急：需要立即处理的事项
           - 高：重要且有明确截止时间的事项
           - 中：一般重要性的事项
           - 低：可以延后处理的事项
        4. 为每个待办事项分配合适的分类：
           - 工作：工作相关任务
           - 会议：需要安排或参加的会议
           - 项目：项目相关任务
           - 沟通：需要联系或跟进的事项
           - 文档：需要编写或整理的文档
           - 审核：需要审核或确认的事项
           - 其他：其他类型的任务
        5. 截止时间设置规则：
           - 只有在会议内容中明确提到具体时间、日期或截止期限时才设置dueDate
           - 如果没有明确的时间信息，dueDate字段应该省略或为空
           - 不要根据任务的重要性或类型推测截止时间
           - 使用上面提供的当前系统时间作为基准来计算相对时间
           - 例如："明天"、"下周"、"月底"等相对时间应该基于当前时间计算
           - 例如："今年年底"应该基于当前年份计算，而不是训练数据中的年份
        6. 为每个TODO项提供简洁但有用的描述

        输出格式：
        请以JSON数组格式返回，每个TODO项包含以下字段：
        {
          "title": "待办事项标题（简洁明确）",
          "description": "详细描述（包含背景信息和具体要求）",
          "priority": "优先级（紧急/高/中/低）",
          "category": "分类（工作/会议/项目/沟通/文档/审核/其他）",
          "dueDate": "截止时间（仅在明确提到时间时设置，格式：yyyy-MM-dd HH:mm）"
        }
        
        示例输出（假设当前时间是2025-01-10 14:30:00）：
        [
          {
            "title": "完成项目方案设计",
            "description": "根据会议讨论的需求，完成新产品的技术方案设计，包含架构图和实现计划",
            "priority": "高",
            "category": "项目",
            "dueDate": "2025-01-15 18:00"
          },
          {
            "title": "联系供应商确认报价",
            "description": "联系ABC公司确认设备采购报价，并获取详细的技术规格说明",
            "priority": "中",
            "category": "沟通"
          },
          {
            "title": "明天提交周报",
            "description": "整理本周工作进展，提交周报给主管",
            "priority": "高",
            "category": "工作",
            "dueDate": "2025-01-11 17:00"
          },
          {
            "title": "年底前完成系统升级",
            "description": "完成生产系统的版本升级工作",
            "priority": "高",
            "category": "项目",
            "dueDate": "2025-12-31 23:59"
          }
        ]

        注意：
        - 使用当前系统时间作为基准计算相对时间
        - "明天"基于当前时间计算为2025-01-11
        - "年底"基于当前年份2025计算，而不是训练数据中的年份
        - 没有明确时间信息的任务不设置dueDate字段
        
        会议内容：
        $meetingContent
        
        请仔细分析会议内容，提取出所有可执行的待办事项：
        """.trimIndent()
    }

    /**
     * 获取当前时间上下文信息
     */
    private fun getCurrentTimeContext(): String {
        val now = java.util.Calendar.getInstance()
        val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
        val dayOfWeekFormat = java.text.SimpleDateFormat("EEEE", java.util.Locale.getDefault())

        // 计算一些有用的时间点
        val tomorrow = java.util.Calendar.getInstance().apply { add(java.util.Calendar.DAY_OF_YEAR, 1) }
        val nextWeek = java.util.Calendar.getInstance().apply { add(java.util.Calendar.WEEK_OF_YEAR, 1) }
        val endOfMonth = java.util.Calendar.getInstance().apply {
            set(java.util.Calendar.DAY_OF_MONTH, getActualMaximum(java.util.Calendar.DAY_OF_MONTH))
        }
        val endOfYear = java.util.Calendar.getInstance().apply {
            set(java.util.Calendar.MONTH, java.util.Calendar.DECEMBER)
            set(java.util.Calendar.DAY_OF_MONTH, 31)
        }

        return """
        - 当前时间：${dateFormat.format(now.time)} (${dayOfWeekFormat.format(now.time)})
        - 当前年份：${now.get(java.util.Calendar.YEAR)}
        - 当前月份：${now.get(java.util.Calendar.MONTH) + 1}月
        - 明天：${dateFormat.format(tomorrow.time)} (${dayOfWeekFormat.format(tomorrow.time)})
        - 下周同一天：${dateFormat.format(nextWeek.time)}
        - 本月底：${dateFormat.format(endOfMonth.time)}
        - 今年年底：${dateFormat.format(endOfYear.time)}

        请基于以上当前时间信息来理解和计算会议内容中提到的相对时间。
        """.trimIndent()
    }
}
