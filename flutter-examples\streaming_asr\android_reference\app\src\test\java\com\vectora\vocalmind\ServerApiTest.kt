package com.vectora.vocalmind

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vectora.vocalmind.server.*
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner

/**
 * 服务器API测试
 */
@RunWith(MockitoJUnitRunner::class)
class ServerApiTest {

    @Mock
    private lateinit var mockContext: Context

    @Before
    fun setup() {
        // 模拟Context
        `when`(mockContext.applicationContext).thenReturn(mockContext)
    }

    @Test
    fun testServerConfigManager() {
        // 测试服务器配置管理器的基本功能
        
        // 测试URL验证
        assert(ServerConfigManager.isValidServerUrl("http://127.0.0.1:8000"))
        assert(ServerConfigManager.isValidServerUrl("https://api.example.com"))
        assert(!ServerConfigManager.isValidServerUrl("invalid-url"))
        assert(!ServerConfigManager.isValidServerUrl(""))
        
        println("✓ ServerConfigManager URL验证测试通过")
    }

    @Test
    fun testApiModels() {
        // 测试API数据模型的创建
        
        val user = User(
            id = "test-id",
            email = "<EMAIL>",
            name = "测试用户",
            role = "user"
        )
        
        assert(user.email == "<EMAIL>")
        assert(user.name == "测试用户")
        
        val loginRequest = LoginRequest(
            email = "<EMAIL>",
            password = "password123"
        )
        
        assert(loginRequest.email == "<EMAIL>")
        assert(loginRequest.password == "password123")
        
        val llmMessage = LLMMessage(
            role = "user",
            content = "你好"
        )
        
        assert(llmMessage.role == "user")
        assert(llmMessage.content == "你好")
        
        println("✓ API数据模型测试通过")
    }

    @Test
    fun testLLMChatRequest() {
        // 测试LLM聊天请求模型
        
        val messages = listOf(
            LLMMessage("user", "你好"),
            LLMMessage("assistant", "你好！有什么可以帮助您的吗？"),
            LLMMessage("user", "请总结这段会议内容")
        )
        
        val chatRequest = LLMChatRequest(
            provider = "deepseek",
            messages = messages,
            stream = false
        )
        
        assert(chatRequest.provider == "deepseek")
        assert(chatRequest.messages.size == 3)
        assert(!chatRequest.stream)
        
        // 测试流式请求
        val streamRequest = LLMChatRequest(
            provider = "gemini",
            messages = messages,
            stream = true
        )
        
        assert(streamRequest.stream)
        
        println("✓ LLM聊天请求模型测试通过")
    }

    @Test
    fun testApiResponse() {
        // 测试API响应模型
        
        val successResponse = ApiResponse(
            success = true,
            data = "测试数据",
            message = "操作成功"
        )
        
        assert(successResponse.success)
        assert(successResponse.data == "测试数据")
        assert(successResponse.error == null)
        
        val errorResponse = ApiResponse<String>(
            success = false,
            error = ApiError(
                code = "INVALID_CREDENTIALS",
                message = "用户名或密码错误"
            )
        )
        
        assert(!errorResponse.success)
        assert(errorResponse.error?.code == "INVALID_CREDENTIALS")
        assert(errorResponse.data == null)
        
        println("✓ API响应模型测试通过")
    }

    @Test
    fun testUsageStats() {
        // 测试使用量统计模型
        
        val llmCallStats = LLMCallStats(
            total = 150,
            deepseek = 80,
            gemini = 70
        )
        
        val usageStats = UsageStats(
            period = "2024-01",
            llmCalls = llmCallStats,
            tokensUsed = 15000,
            cost = 3.50
        )
        
        assert(usageStats.period == "2024-01")
        assert(usageStats.llmCalls.total == 150)
        assert(usageStats.llmCalls.deepseek == 80)
        assert(usageStats.llmCalls.gemini == 70)
        assert(usageStats.tokensUsed == 15000)
        assert(usageStats.cost == 3.50)
        
        println("✓ 使用量统计模型测试通过")
    }

    @Test
    fun testApiKeyInfo() {
        // 测试API密钥信息模型
        
        val apiKeyInfo = ApiKeyInfo(
            id = "key-123",
            name = "我的应用密钥",
            keyPrefix = "sk_live_1234",
            permissions = listOf("llm:chat", "llm:stream"),
            isActive = true,
            createdAt = "2024-01-01T00:00:00Z"
        )
        
        assert(apiKeyInfo.id == "key-123")
        assert(apiKeyInfo.name == "我的应用密钥")
        assert(apiKeyInfo.permissions.contains("llm:chat"))
        assert(apiKeyInfo.permissions.contains("llm:stream"))
        assert(apiKeyInfo.isActive)
        
        println("✓ API密钥信息模型测试通过")
    }

    @Test
    fun testStreamChunk() {
        // 测试流式响应数据块模型
        
        val contentChunk = StreamChunk(
            type = "chunk",
            content = "你好"
        )
        
        assert(contentChunk.type == "chunk")
        assert(contentChunk.content == "你好")
        assert(contentChunk.usage == null)
        
        val doneChunk = StreamChunk(
            type = "done",
            usage = LLMUsage(tokensUsed = 150, cost = 0.003)
        )
        
        assert(doneChunk.type == "done")
        assert(doneChunk.content == null)
        assert(doneChunk.usage?.tokensUsed == 150)
        assert(doneChunk.usage?.cost == 0.003)
        
        println("✓ 流式响应数据块模型测试通过")
    }

    @Test
    fun testHealthResponse() {
        // 测试健康检查响应模型
        
        val healthResponse = HealthResponse(
            status = "healthy",
            timestamp = "2024-01-01T12:00:00Z",
            version = "1.0.0"
        )
        
        assert(healthResponse.status == "healthy")
        assert(healthResponse.timestamp == "2024-01-01T12:00:00Z")
        assert(healthResponse.version == "1.0.0")
        
        println("✓ 健康检查响应模型测试通过")
    }

    @Test
    fun testApiResult() {
        // 测试API结果封装
        
        val successResult = ApiResult.Success("操作成功")
        assert(successResult.isSuccess())
        assert(!successResult.isError())
        assert(successResult.getOrNull() == "操作成功")
        assert(successResult.getErrorMessage() == null)
        
        val errorResult = ApiResult.Error("操作失败")
        assert(!errorResult.isSuccess())
        assert(errorResult.isError())
        assert(errorResult.getOrNull() == null)
        assert(errorResult.getErrorMessage() == "操作失败")
        
        println("✓ API结果封装测试通过")
    }

    @Test
    fun testLLMResultExtension() {
        // 测试扩展的LLMResult
        
        val result = LLMResult(
            success = true,
            content = "这是AI的回复",
            error = null,
            tokensUsed = 150,
            cost = 0.003
        )
        
        assert(result.success)
        assert(result.content == "这是AI的回复")
        assert(result.tokensUsed == 150)
        assert(result.cost == 0.003)
        
        println("✓ 扩展LLMResult测试通过")
    }

    @Test
    fun testLoginResponseJsonParsing() {
        // 测试登录响应的JSON解析修复
        val gson = Gson()

        // 模拟服务器返回的JSON响应（与错误日志中的相同）
        val jsonResponse = """
        {
            "success": true,
            "data": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.N69S3PIjECwRoWcKbj0Y0-umgo2JxUeeVRypPdFXW9Q",
                "token_type": "Bearer",
                "expires_in": 86400,
                "user": {
                    "email": "<EMAIL>",
                    "name": "qiaogaojian",
                    "id": "c1c39e84-bdc7-4ac4-a197-63e784a774f2",
                    "role": "user",
                    "is_active": true,
                    "is_verified": true,
                    "total_llm_calls": 0,
                    "monthly_llm_calls": 0,
                    "created_at": "2025-07-03T11:55:17",
                    "updated_at": "2025-07-05T14:00:41",
                    "last_login_at": "2025-07-05T14:00:42"
                }
            },
            "message": "登录成功"
        }
        """.trimIndent()

        // 使用TypeToken正确解析泛型类型
        val type = object : TypeToken<ApiResponse<LoginData>>() {}.type
        val response = gson.fromJson<ApiResponse<LoginData>>(jsonResponse, type)

        // 验证解析结果
        assert(response.success)
        assert(response.data != null)
        assert(response.data!!.accessToken.isNotEmpty())
        assert(response.data!!.tokenType == "Bearer")
        assert(response.data!!.expiresIn == 86400L)
        assert(response.data!!.user.email == "<EMAIL>")
        assert(response.data!!.user.name == "qiaogaojian")
        assert(response.data!!.user.role == "user")
        assert(response.message == "登录成功")

        println("✓ 登录响应JSON解析修复测试通过")
        println("  - 成功解析LoginData对象而不是LinkedTreeMap")
        println("  - 用户信息: ${response.data!!.user.email}")
        println("  - Token类型: ${response.data!!.tokenType}")
    }

    @Test
    fun testLLMChatResponseJsonParsing() {
        // 测试LLM聊天响应的JSON解析修复
        val gson = Gson()

        // 模拟服务器返回的JSON响应（与错误日志中的相同）
        val jsonResponse = """
        {
            "success": true,
            "data": {
                "content": "### 会议总结\n**会议日期**：未提供\n**会议时间**：22:45（UTC+8或其他时区，需确认）\n**参会人员**：未知声纹（未明确身份）",
                "usage": {
                    "tokens_used": 394,
                    "cost": 0.0007880000000000001
                }
            },
            "message": "聊天完成"
        }
        """.trimIndent()

        // 使用TypeToken正确解析泛型类型
        val type = object : TypeToken<ApiResponse<LLMChatData>>() {}.type
        val response = gson.fromJson<ApiResponse<LLMChatData>>(jsonResponse, type)

        // 验证解析结果
        assert(response.success)
        assert(response.data != null)
        assert(response.data!!.content.isNotEmpty())
        assert(response.data!!.content.contains("会议总结"))
        assert(response.data!!.usage != null)
        assert(response.data!!.usage!!.tokensUsed == 394)
        assert(response.data!!.usage!!.cost == 0.0007880000000000001)
        assert(response.message == "聊天完成")

        println("✓ LLM聊天响应JSON解析修复测试通过")
        println("  - 成功解析LLMChatData对象而不是LinkedTreeMap")
        println("  - 内容长度: ${response.data!!.content.length}")
        println("  - 使用的Token数: ${response.data!!.usage!!.tokensUsed}")
        println("  - 成本: ${response.data!!.usage!!.cost}")
    }
}
