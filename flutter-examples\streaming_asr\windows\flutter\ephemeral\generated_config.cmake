# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\SDK\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr" PROJECT_DIR)

set(FLUTTER_VERSION "1.12.7" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 12 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 7 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\SDK\\flutter"
  "PROJECT_DIR=D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr"
  "FLUTTER_ROOT=D:\\SDK\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr"
  "FLUTTER_TARGET=D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Git\\Learn\\sherpa-onnx\\flutter-examples\\streaming_asr\\.dart_tool\\package_config.json"
)
