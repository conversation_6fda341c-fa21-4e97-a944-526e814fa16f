<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:fillViewport="true"
    tools:context=".RegisterActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="600dp">

        <!-- 顶部导航栏 -->
        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="注册"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text"
                android:gravity="center" />

            <View
                android:layout_width="24dp"
                android:layout_height="24dp" />

        </LinearLayout>

        <!-- 主要内容区域 -->
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:padding="32dp"
            app:layout_constraintTop_toBottomOf="@id/layout_header"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Logo和标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_app_logo"
                    android:layout_marginBottom="16dp"
                    android:contentDescription="App Logo" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="创建账户"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="加入我们，开始您的智能语音之旅"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

            </LinearLayout>

            <!-- 注册表单 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <!-- 姓名输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="姓名"
                        android:inputType="textPersonName"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 邮箱输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="邮箱地址"
                        android:inputType="textEmailAddress"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:passwordToggleEnabled="true"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="密码（至少8位）"
                        android:inputType="textPassword"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码强度提示 -->
                <TextView
                    android:id="@+id/tv_password_strength"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="密码强度：弱"
                    android:textSize="12sp"
                    android:textColor="@color/warning_color"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone" />

                <!-- 确认密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_confirm_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:passwordToggleEnabled="true"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_confirm_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="确认密码"
                        android:inputType="textPassword"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 服务条款同意 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.checkbox.MaterialCheckBox
                        android:id="@+id/cb_terms"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        app:buttonTint="@color/primary_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="我已阅读并同意"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_text" />

                    <TextView
                        android:id="@+id/tv_terms_link"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="服务条款"
                        android:textSize="14sp"
                        android:textColor="@color/primary_color"
                        android:textStyle="bold"
                        android:padding="4dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="和"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_text"
                        android:paddingHorizontal="4dp" />

                    <TextView
                        android:id="@+id/tv_privacy_link"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="隐私政策"
                        android:textSize="14sp"
                        android:textColor="@color/primary_color"
                        android:textStyle="bold"
                        android:padding="4dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true" />

                </LinearLayout>

            </LinearLayout>

            <!-- 错误提示 -->
            <TextView
                android:id="@+id/tv_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="错误信息"
                android:textSize="14sp"
                android:textColor="@color/error_color"
                android:background="@drawable/error_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                android:drawableStart="@drawable/ic_error"
                android:drawablePadding="8dp"
                android:gravity="center_vertical" />

            <!-- 注册按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_register"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="注册"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:backgroundTint="@color/primary_color"
                app:cornerRadius="28dp"
                android:layout_marginBottom="16dp"
                android:enabled="false"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginBottom="24dp"
                android:visibility="gone"
                android:indeterminateTint="@color/primary_color" />

            <!-- 登录链接 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="已有账户？"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

                <TextView
                    android:id="@+id/tv_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="立即登录"
                    android:textSize="14sp"
                    android:textColor="@color/primary_color"
                    android:textStyle="bold"
                    android:padding="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
