<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 服务器机架 -->
    <path
        android:fillColor="@color/primary_color"
        android:pathData="M2,3h20c1.1,0 2,0.9 2,2v2c0,1.1 -0.9,2 -2,2H2C0.9,9 0,8.1 0,7V5C0,3.9 0.9,3 2,3z" />
    
    <path
        android:fillColor="@color/primary_color"
        android:pathData="M2,10h20c1.1,0 2,0.9 2,2v2c0,1.1 -0.9,2 -2,2H2c-1.1,0 -2,-0.9 -2,-2v-2C0,10.9 0.9,10 2,10z" />
    
    <path
        android:fillColor="@color/primary_color"
        android:pathData="M2,17h20c1.1,0 2,0.9 2,2v2c0,1.1 -0.9,2 -2,2H2c-1.1,0 -2,-0.9 -2,-2v-2C0,17.9 0.9,17 2,17z" />
    
    <!-- 服务器指示灯 -->
    <path
        android:fillColor="@color/white"
        android:pathData="M4,5.5m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M4,12.5m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M4,19.5m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0" />
    
    <!-- 服务器端口 -->
    <path
        android:fillColor="@color/white"
        android:pathData="M18,4h3v3h-3z" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M18,11h3v3h-3z" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M18,18h3v3h-3z" />
    
</vector>
