<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 顶部导航栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="服务器设置"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text"
                android:gravity="center" />

            <View
                android:layout_width="24dp"
                android:layout_height="24dp" />

        </LinearLayout>

        <!-- 模式选择卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔄 运行模式"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="12dp" />

                <!-- 服务器模式开关 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="使用服务器模式"
                            android:textSize="16sp"
                            android:textColor="@color/primary_text" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="通过自建服务器统一管理API调用"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_server_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:thumbTint="@color/white"
                        app:trackTint="@color/primary_color" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 服务器配置区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_server_config"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:id="@+id/layout_server_config"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🌐 服务器配置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="16dp" />

                <!-- 服务器URL -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_server_url"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="服务器地址"
                        android:text="http://127.0.0.1:8000"
                        android:inputType="textUri"
                        android:textSize="14sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 连接测试 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_connection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="测试连接"
                        android:textSize="14sp"
                        android:layout_marginEnd="16dp"
                        app:cornerRadius="20dp"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                    <TextView
                        android:id="@+id/text_connection_status"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="未测试"
                        android:textSize="14sp"
                        android:textColor="@color/secondary_text" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 用户认证区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_user_auth"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="👤 用户认证"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="16dp" />

                <!-- 当前用户信息 -->
                <TextView
                    android:id="@+id/text_user_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="未登录"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text"
                    android:background="@drawable/info_background"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    android:drawableStart="@drawable/ic_user"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <!-- 用户认证按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_user_auth"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="登录/注册"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:backgroundTint="@color/primary_color"
                    app:cornerRadius="28dp"
                    style="@style/Widget.MaterialComponents.Button" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 说明文本 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="1dp"
            app:cardBackgroundColor="#F8F9FA">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💡 模式说明"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="• 隐私模式：直接调用第三方LLM API，数据不经过服务器，保护隐私\n• 服务器模式：通过自建服务器调用LLM API，便于统一管理和使用量统计"
                    android:textSize="13sp"
                    android:textColor="@color/secondary_text"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
