<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:tint="?attr/colorOnSurface"
    android:viewportWidth="24"
    android:viewportHeight="24">


    <!-- 收纳盒（U 形托盘） -->
    <path
        android:fillColor="@android:color/transparent"
        android:pathData="
            M4,9
            v10
            h16
            v-7"
        android:strokeWidth="2"
        android:strokeColor="#000000"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />

    <!-- 竖直书 1 -->
    <path
        android:fillColor="@android:color/transparent"
        android:pathData="M8,3 v14"
        android:strokeWidth="2"
        android:strokeColor="#000000"
        android:strokeLineCap="round" />

    <!-- 竖直书 2 -->
    <path
        android:fillColor="@android:color/transparent"
        android:pathData="M12,3 v14"
        android:strokeWidth="2"
        android:strokeColor="#000000"
        android:strokeLineCap="round" />

    <!-- 倾斜书 -->
    <path
        android:fillColor="@android:color/transparent"
        android:pathData="M18,3 l-3.5,14"
        android:strokeWidth="2"
        android:strokeColor="#000000"
        android:strokeLineCap="round" />

</vector>
