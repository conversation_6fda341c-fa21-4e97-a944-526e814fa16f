<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:src="@drawable/ic_arrow_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="添加待办事项"
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Title"
            android:textSize="20sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            style="@style/Widget.VoiceAssistant.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="0dp"
            android:paddingHorizontal="16dp"
            android:text="保存"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Title Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="标题 *"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                        android:textColor="@color/apple_blue"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/edit_text_background"
                        android:hint="请输入待办事项标题"
                        android:inputType="text"
                        android:maxLines="2"
                        android:padding="12dp"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Description Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="描述"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                        android:textColor="@color/apple_blue"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/edit_text_background"
                        android:gravity="top"
                        android:hint="请输入详细描述（可选）"
                        android:inputType="textMultiLine"
                        android:maxLines="4"
                        android:minLines="3"
                        android:padding="12dp"
                        android:textSize="14sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Category and Priority Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Category -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="分类"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                        android:textColor="@color/apple_blue"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_category"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/edit_text_background"
                        android:hint="工作、生活、学习等"
                        android:inputType="text"
                        android:maxLines="1"
                        android:padding="12dp"
                        android:textSize="14sp" />

                    <!-- Priority -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="优先级"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                        android:textColor="@color/apple_blue"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinner_priority"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/spinner_background" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Due Date Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="设置截止时间"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                            android:textColor="@color/apple_blue"
                            android:textStyle="bold" />

                        <Switch
                            android:id="@+id/switch_due_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layout_due_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/btn_select_date"
                                style="@style/Widget.VoiceAssistant.Button.Outlined"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:layout_weight="1"
                                android:text="选择日期"
                                android:textSize="14sp" />

                            <Button
                                android:id="@+id/btn_select_time"
                                style="@style/Widget.VoiceAssistant.Button.Outlined"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:layout_weight="1"
                                android:text="选择时间"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_selected_datetime"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/selected_datetime_background"
                            android:gravity="center"
                            android:padding="12dp"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                            android:textColor="@color/apple_blue"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 提醒时间设置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="设置提醒时间"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                            android:textColor="@color/apple_blue"
                            android:textStyle="bold" />

                        <Switch
                            android:id="@+id/switch_reminder"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/layout_reminder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/btn_select_reminder_date"
                                style="@style/Widget.VoiceAssistant.Button.Outlined"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:layout_weight="1"
                                android:text="选择日期"
                                android:textSize="14sp" />

                            <Button
                                android:id="@+id/btn_select_reminder_time"
                                style="@style/Widget.VoiceAssistant.Button.Outlined"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:layout_weight="1"
                                android:text="选择时间"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_selected_reminder_datetime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/tag_background"
                            android:backgroundTint="@color/apple_orange"
                            android:padding="8dp"
                            android:text="2024-01-01 09:00"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Delete Button (only in edit mode) -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_delete"
        style="@style/Widget.VoiceAssistant.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="删除待办事项"
        android:textColor="@color/apple_red"
        android:visibility="gone"
        app:icon="@drawable/ic_delete"
        app:iconGravity="start"
        app:iconTint="@color/apple_red" />

</LinearLayout>
