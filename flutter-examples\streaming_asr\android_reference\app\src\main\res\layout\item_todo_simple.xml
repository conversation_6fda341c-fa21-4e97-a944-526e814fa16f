<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="@drawable/todo_item_background"
    android:layout_marginVertical="4dp"
    android:gravity="center_vertical">

    <!-- Checkbox -->
    <CheckBox
        android:id="@+id/cb_completed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp" />

    <!-- Content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Title -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Category and Priority -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/category_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="工作"
                android:textColor="@color/apple_blue"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tv_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/priority_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="中"
                android:textColor="@color/white"
                android:textSize="10sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
