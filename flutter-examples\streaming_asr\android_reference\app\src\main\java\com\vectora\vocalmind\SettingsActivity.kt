package com.vectora.vocalmind

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioRecord
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.k2fsa.sherpa.onnx.SpeakerRecognition
import com.vectora.vocalmind.server.ServerConfigManager
import kotlinx.coroutines.*

/**
 * LLM设置页面
 * 用于配置LLM提供商、API密钥和自动优化设置
 */
class SettingsActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SettingsActivity"
        private const val PERMISSION_REQUEST_CODE = 1001
        private const val SAMPLE_RATE = 16000
        private const val BUFFER_SIZE = 1024

        /**
         * 获取录音保存设置（静态方法，供其他类调用）
         */
        fun getSaveRecordingSetting(context: android.content.Context): Boolean {
            val sharedPrefs = context.getSharedPreferences("app_settings", android.content.Context.MODE_PRIVATE)
            return sharedPrefs.getBoolean("save_recording", true) // 默认为true
        }

        /**
         * 获取导出格式设置（静态方法，供其他类调用）
         */
        fun getExportFormat(context: android.content.Context): String {
            val sharedPrefs = context.getSharedPreferences("app_settings", android.content.Context.MODE_PRIVATE)
            return sharedPrefs.getString("export_format", "Markdown") ?: "Markdown" // 默认为Markdown
        }
    }

    // 声纹录音相关
    private var audioRecord: AudioRecord? = null
    private var speakerDataManager: SpeakerDataManager? = null
    @Volatile
    private var isSpeakerRecording = false
    private var speakerRecordingThread: Thread? = null

    // UI组件
    private lateinit var llUserAuth: LinearLayout
    private lateinit var tvUserStatus: TextView
    private lateinit var llLlmProvider: LinearLayout
    private lateinit var tvCurrentProvider: TextView
    private lateinit var llServerSettings: LinearLayout
    // private lateinit var tvServerModeStatus: TextView
    private lateinit var tvServerMode: TextView
    private lateinit var switchAutoOptimize: androidx.appcompat.widget.SwitchCompat
    private lateinit var llSpeakerManagement: LinearLayout
    private lateinit var tvSpeakerCount: TextView
    private lateinit var switchSaveRecording: com.google.android.material.switchmaterial.SwitchMaterial
    private lateinit var llExportFormat: LinearLayout
    private lateinit var tvExportFormat: TextView
    private lateinit var tvVersion: TextView

    // 悬浮窗设置UI组件
    private lateinit var switchFloatingWindow: com.google.android.material.switchmaterial.SwitchMaterial
    private lateinit var switchAutoStartFloating: com.google.android.material.switchmaterial.SwitchMaterial
    private lateinit var switchEdgeSnap: com.google.android.material.switchmaterial.SwitchMaterial

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings_apple)

        // 设置工具栏
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 初始化声纹数据管理器
        speakerDataManager = SpeakerDataManager(this)

        initViews()
        loadCurrentSettings()
        setupEventListeners()
        updateSpeakerCount()
    }

    override fun onResume() {
        super.onResume()
        // 从服务器设置页面返回时更新状态
        updateServerModeStatus()
    }

    private fun updateServerModeStatus() {
        val useServerMode = ServerConfigManager.isUseServerMode(this)
        tvServerMode.text = if (useServerMode) "服务器模式" else "隐私模式"

        // 更新用户状态
        updateUserStatus()
    }

    private fun updateUserStatus() {
        val useServerMode = ServerConfigManager.isUseServerMode(this)
        if (useServerMode) {
            val isLoggedIn = com.vectora.vocalmind.server.UserAuthManager.isLoggedIn(this)
            if (isLoggedIn) {
                val user = com.vectora.vocalmind.server.UserAuthManager.getCurrentUser(this)
                tvUserStatus.text = "已登录: ${user?.name}"
            } else {
                tvUserStatus.text = "未登录"
            }
        } else {
            tvUserStatus.text = "隐私模式 (无需登录)"
        }
    }

    private fun initViews() {
        llUserAuth = findViewById(R.id.ll_user_auth)
        tvUserStatus = findViewById(R.id.tv_user_status)
        llLlmProvider = findViewById(R.id.ll_llm_provider)
        tvCurrentProvider = findViewById(R.id.tv_current_provider)
        llServerSettings = findViewById(R.id.ll_server_settings)
        // tvServerModeStatus = findViewById(R.id.tv_server_mode_status)
        tvServerMode = findViewById(R.id.tv_server_mode)
        switchAutoOptimize = findViewById(R.id.switch_auto_optimize)
        llSpeakerManagement = findViewById(R.id.ll_speaker_management)
        tvSpeakerCount = findViewById(R.id.tv_speaker_count)
        switchSaveRecording = findViewById(R.id.switch_save_recording)
        llExportFormat = findViewById(R.id.ll_export_format)
        tvExportFormat = findViewById(R.id.tv_export_format)
        tvVersion = findViewById(R.id.tv_version)

        // 悬浮窗设置UI组件
        switchFloatingWindow = findViewById(R.id.switch_floating_window)
        switchAutoStartFloating = findViewById(R.id.switch_auto_start_floating)
        switchEdgeSnap = findViewById(R.id.switch_edge_snap)
    }

    private fun loadCurrentSettings() {
        try {
            // 加载当前选择的提供商
            val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
            tvCurrentProvider.text = currentProvider.displayName

            // 加载服务器模式状态
            val useServerMode = ServerConfigManager.isUseServerMode(this)
            // tvServerModeStatus.text = if (useServerMode) "服务器模式" else "隐私模式"

            // 加载自动优化设置
            switchAutoOptimize.isChecked = LLMApiKeyManager.getAutoOptimize(this)

            // 加载录音保存设置
            switchSaveRecording.isChecked = getSaveRecordingSetting()

            // 加载悬浮窗设置
            switchFloatingWindow.isChecked = FloatingWindowSettings.isFloatingWindowEnabled(this)
            switchAutoStartFloating.isChecked = FloatingWindowSettings.isAutoStartEnabled(this)
            switchEdgeSnap.isChecked = FloatingWindowSettings.isEdgeSnapEnabled(this)

            // 加载导出格式设置
            tvExportFormat.text = getExportFormat()

            // 加载导出格式设置
            tvExportFormat.text = getExportFormat()

            // 加载版本信息
            try {
                val packageInfo = packageManager.getPackageInfo(packageName, 0)
                tvVersion.text = packageInfo.versionName
            } catch (e: Exception) {
                tvVersion.text = "1.0.0"
            }

            Log.d(TAG, "设置加载完成")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            showToast("加载设置失败: ${e.message}")
        }
    }

    private fun setupEventListeners() {
        // 用户认证管理
        llUserAuth.setOnClickListener {
            handleUserAuthClick()
        }

        // LLM提供商选择
        llLlmProvider.setOnClickListener {
            showLLMProviderDialog()
        }

        // 服务器设置
        llServerSettings.setOnClickListener {
            val intent = Intent(this, ServerSettingsActivity::class.java)
            startActivity(intent)
        }

        // 自动优化开关
        switchAutoOptimize.setOnCheckedChangeListener { _, isChecked ->
            LLMApiKeyManager.setAutoOptimize(this, isChecked)
            showToast(if (isChecked) "自动优化已开启" else "自动优化已关闭")
        }

        // 录音保存开关
        switchSaveRecording.setOnCheckedChangeListener { _, isChecked ->
            setSaveRecordingSetting(isChecked)
            showToast(if (isChecked) "录音保存已开启" else "录音保存已关闭")
        }

        // 声纹管理
        llSpeakerManagement.setOnClickListener {
            showSpeakerManagementDialog()
        }

        // 导出格式
        llExportFormat.setOnClickListener {
            showExportFormatDialog()
        }

        // 悬浮窗功能开关
        switchFloatingWindow.setOnCheckedChangeListener { _, isChecked ->
            handleFloatingWindowToggle(isChecked)
        }

        // 自动启动悬浮窗开关
        switchAutoStartFloating.setOnCheckedChangeListener { _, isChecked ->
            FloatingWindowSettings.setAutoStartEnabled(this, isChecked)
            showToast(if (isChecked) "自动启动悬浮窗已开启" else "自动启动悬浮窗已关闭")
        }

        // 边缘吸附开关
        switchEdgeSnap.setOnCheckedChangeListener { _, isChecked ->
            FloatingWindowSettings.setEdgeSnapEnabled(this, isChecked)
            showToast(if (isChecked) "边缘吸附已开启" else "边缘吸附已关闭")
        }
    }

    private fun updateSpeakerCount() {
        try {
            val speakerCount = speakerDataManager?.getAllSpeakers()?.size ?: 0
            tvSpeakerCount.text = "$speakerCount 人"
        } catch (e: Exception) {
            Log.e(TAG, "更新声纹数量失败", e)
            tvSpeakerCount.text = "0 人"
        }
    }

    private fun handleUserAuthClick() {
        val useServerMode = ServerConfigManager.isUseServerMode(this)

        if (!useServerMode) {
            // 隐私模式，显示模式切换选项
            showModeSelectionDialog()
        } else {
            // 服务器模式，检查登录状态
            val isLoggedIn = com.vectora.vocalmind.server.UserAuthManager.isLoggedIn(this)
            if (isLoggedIn) {
                // 已登录，显示用户管理选项
                showUserManagementDialog()
            } else {
                // 未登录，跳转到认证流程
                startActivity(Intent(this, WelcomeActivity::class.java))
            }
        }
    }

    private fun showModeSelectionDialog() {
        AlertDialog.Builder(this)
            .setTitle("选择运行模式")
            .setMessage("当前为隐私模式，数据不经过服务器。是否要切换到服务器模式？")
            .setPositiveButton("切换到服务器模式") { _, _ ->
                ServerConfigManager.setUseServerMode(this, true)
                updateServerModeStatus()
                showToast("已切换到服务器模式")
            }
            .setNegativeButton("保持隐私模式", null)
            .show()
    }

    private fun showUserManagementDialog() {
        val currentUser = com.vectora.vocalmind.server.UserAuthManager.getCurrentUser(this)
        if (currentUser == null) return

        val options = arrayOf("查看个人信息", "登出", "切换到隐私模式")

        AlertDialog.Builder(this)
            .setTitle("用户管理")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> showUserProfile(currentUser)
                    1 -> logout()
                    2 -> switchToPrivacyMode()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showUserProfile(user: com.vectora.vocalmind.server.User) {
        val message = """
            姓名: ${user.name}
            邮箱: ${user.email}
            角色: ${user.role}
            状态: ${if (user.isActive) "活跃" else "非活跃"}
            LLM调用次数: ${user.totalLlmCalls}
            本月调用次数: ${user.monthlyLlmCalls}
        """.trimIndent()

        AlertDialog.Builder(this)
            .setTitle("个人信息")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun logout() {
        AlertDialog.Builder(this)
            .setTitle("确认登出")
            .setMessage("确定要登出当前账户吗？")
            .setPositiveButton("确定") { _, _ ->
                com.vectora.vocalmind.server.UserAuthManager.logout(this)
                updateUserStatus()
                showToast("已登出")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun switchToPrivacyMode() {
        AlertDialog.Builder(this)
            .setTitle("切换到隐私模式")
            .setMessage("切换到隐私模式后，数据将不经过服务器，直接调用第三方API。确定要切换吗？")
            .setPositiveButton("确定") { _, _ ->
                ServerConfigManager.setUseServerMode(this, false)
                updateServerModeStatus()
                showToast("已切换到隐私模式")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showLLMProviderDialog() {
        val providers = arrayOf("Gemini", "DeepSeek")
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
        val currentIndex = when (currentProvider) {
            LLMProvider.GEMINI -> 0
            LLMProvider.DEEPSEEK -> 1
        }

        AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("选择AI提供商")
            .setSingleChoiceItems(providers, currentIndex) { dialog, which ->
                val selectedProvider = when (which) {
                    0 -> LLMProvider.GEMINI
                    1 -> LLMProvider.DEEPSEEK
                    else -> LLMProvider.GEMINI
                }

                // 检查是否有API密钥
                lifecycleScope.launch {
                    if (!LLMManager.isLLMAvailable(this@SettingsActivity, selectedProvider)) {
                        showLLMConfigDialog(selectedProvider)
                    } else {
                        LLMApiKeyManager.setCurrentProvider(this@SettingsActivity, selectedProvider)
                        tvCurrentProvider.text = selectedProvider.displayName
                        showToast("已切换到 ${selectedProvider.displayName}")
                    }
                }
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showSpeakerManagementDialog() {
        val speakers = speakerDataManager?.getAllSpeakers()?.toList() ?: emptyList()

        val dialog = AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("🎙️ 声纹管理")
            .create()

        val scrollView = androidx.core.widget.NestedScrollView(this).apply {
            setPadding(24, 24, 24, 24)
        }

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }

        // 添加声纹按钮
        val addButton = com.google.android.material.button.MaterialButton(this).apply {
            text = "➕ 注册新声纹"
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 24
            }
            setBackgroundColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_blue))
            setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.white))
            cornerRadius = 12
        }

        addButton.setOnClickListener {
            dialog.dismiss()
            showSpeakerRegistrationDialog()
        }

        layout.addView(addButton)

        // 声纹列表
        if (speakers.isEmpty()) {
            val emptyText = TextView(this).apply {
                text = "暂无已注册的声纹\n点击上方按钮注册新声纹"
                textSize = 16f
                setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_secondary_label))
                gravity = android.view.Gravity.CENTER
                setPadding(0, 40, 0, 40)
            }
            layout.addView(emptyText)
        } else {
            speakers.forEach { speakerName ->
                val speakerCard = createSpeakerCard(speakerName, dialog)
                layout.addView(speakerCard)
            }
        }

        scrollView.addView(layout)
        dialog.setView(scrollView)
        dialog.show()
    }

    private fun createSpeakerCard(speakerName: String, parentDialog: AlertDialog): View {
        val card = androidx.cardview.widget.CardView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 12
            }
            setCardBackgroundColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_system_background))
            radius = 12f
            cardElevation = 2f
        }

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER_VERTICAL
            setPadding(16, 16, 16, 16)
        }

        val nameText = TextView(this).apply {
            text = speakerName
            textSize = 16f
            setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_label))
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }

        val deleteButton = com.google.android.material.button.MaterialButton(this).apply {
            text = "删除"
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setBackgroundColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_red))
            setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.white))
            cornerRadius = 8
            textSize = 14f
        }

        deleteButton.setOnClickListener {
            showDeleteSpeakerConfirmDialog(speakerName, parentDialog)
        }

        layout.addView(nameText)
        layout.addView(deleteButton)
        card.addView(layout)

        return card
    }

    private fun showDeleteSpeakerConfirmDialog(speakerName: String, parentDialog: AlertDialog) {
        AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("删除声纹")
            .setMessage("确定要删除声纹 \"$speakerName\" 吗？\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                // 同时从内存和持久化存储中删除声纹
                deleteSpeakerFromBothMemoryAndStorage(speakerName, parentDialog)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 从内存和持久化存储中删除声纹
     */
    private fun deleteSpeakerFromBothMemoryAndStorage(speakerName: String, parentDialog: AlertDialog) {
        Thread {
            try {
                var memorySuccess = false
                var storageSuccess = false

                // 1. 先从内存中删除（如果SpeakerRecognition已初始化）
                try {
                    if (isSpeakerRecognitionInitialized()) {
                        memorySuccess = SpeakerRecognition.manager.remove(speakerName)
                        Log.i(TAG, "从内存删除声纹 '$speakerName': $memorySuccess")
                    } else {
                        Log.w(TAG, "SpeakerRecognition未初始化，跳过内存删除")
                        memorySuccess = true // 如果未初始化，认为删除成功
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "从内存删除声纹失败: $speakerName", e)
                    memorySuccess = false
                }

                // 2. 从持久化存储中删除
                storageSuccess = speakerDataManager?.removeSpeaker(speakerName) ?: false
                Log.i(TAG, "从存储删除声纹 '$speakerName': $storageSuccess")

                val overallSuccess = memorySuccess && storageSuccess

                runOnUiThread {
                    if (overallSuccess) {
                        showToast("声纹 \"$speakerName\" 已删除")
                        updateSpeakerCount()
                        parentDialog.dismiss()
                        // 重新显示声纹管理对话框
                        showSpeakerManagementDialog()
                    } else {
                        val errorMsg = when {
                            !memorySuccess && !storageSuccess -> "删除声纹失败（内存和存储都失败）"
                            !memorySuccess -> "删除声纹失败（内存删除失败）"
                            !storageSuccess -> "删除声纹失败（存储删除失败）"
                            else -> "删除声纹失败"
                        }
                        showToast(errorMsg)
                        Log.e(TAG, "删除声纹失败: $speakerName, 内存: $memorySuccess, 存储: $storageSuccess")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    showToast("删除声纹异常: ${e.message}")
                    Log.e(TAG, "删除声纹异常: $speakerName", e)
                }
            }
        }.start()
    }

    private fun showSpeakerRegistrationDialog() {
        // 检查麦克风权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.RECORD_AUDIO),
                PERMISSION_REQUEST_CODE
            )
            return
        }

        val dialog = AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("🎙️ 注册新声纹")
            .setMessage("请输入说话人姓名，然后录制3-5次音频样本以提高识别准确性")
            .create()

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        val nameInput = EditText(this).apply {
            hint = "请输入说话人姓名"
            setSingleLine(true)
        }

        val recordCountText = TextView(this).apply {
            text = "录音次数: 0/5"
            textSize = 14f
            setPadding(0, 10, 0, 10)
        }

        val recordButton = com.google.android.material.button.MaterialButton(this).apply {
            text = "开始录音"
            setBackgroundColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_blue))
            setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.white))
            cornerRadius = 12
        }

        val addButton = com.google.android.material.button.MaterialButton(this).apply {
            text = "注册声纹"
            isEnabled = false
            setBackgroundColor(ContextCompat.getColor(this@SettingsActivity, R.color.apple_green))
            setTextColor(ContextCompat.getColor(this@SettingsActivity, R.color.white))
            cornerRadius = 12
        }

        val statusText = TextView(this).apply {
            text = "请输入姓名后点击录音，建议录制3-5次不同的音频样本"
            textSize = 12f
            setPadding(0, 10, 0, 0)
        }

        layout.addView(nameInput)
        layout.addView(recordCountText)
        layout.addView(recordButton)
        layout.addView(addButton)
        layout.addView(statusText)

        dialog.setView(layout)

        var isRecordingForSpeaker = false
        var speakerAudioSamples = mutableListOf<FloatArray>()
        var currentRecordingData = mutableListOf<Float>()

        recordButton.setOnClickListener {
            val speakerName = nameInput.text.toString().trim()

            if (speakerName.isEmpty()) {
                showToast("请输入说话人姓名")
                return@setOnClickListener
            }

            if (speakerDataManager?.containsSpeaker(speakerName) == true) {
                showToast("说话人 '$speakerName' 已存在")
                return@setOnClickListener
            }

            if (!isRecordingForSpeaker) {
                // 开始录音
                startSpeakerRecording(recordButton, statusText, currentRecordingData)
                isRecordingForSpeaker = true
            } else {
                // 停止录音并保存样本
                stopSpeakerRecording(recordButton, statusText, currentRecordingData, speakerAudioSamples, recordCountText, addButton)
                isRecordingForSpeaker = false
            }
        }

        addButton.setOnClickListener {
            val speakerName = nameInput.text.toString().trim()
            if (speakerName.isNotEmpty() && speakerAudioSamples.isNotEmpty()) {
                registerSpeakerWithMultipleSamples(speakerName, speakerAudioSamples, dialog)
            }
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "取消") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun showLLMConfigDialog(provider: LLMProvider) {
        val input = EditText(this).apply {
            hint = "请输入 ${provider.displayName} API 密钥"
            inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        }

        AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("配置 ${provider.displayName}")
            .setMessage("请输入您的 ${provider.displayName} API 密钥")
            .setView(input)
            .setPositiveButton("保存") { _, _ ->
                val apiKey = input.text.toString().trim()
                if (apiKey.isNotBlank()) {
                    LLMApiKeyManager.saveApiKey(this, provider, apiKey)
                    LLMApiKeyManager.setCurrentProvider(this, provider)
                    tvCurrentProvider.text = provider.displayName
                    showToast("${provider.displayName} 配置成功")
                } else {
                    showToast("API 密钥不能为空")
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showExportFormatDialog() {
        val formats = arrayOf("TXT", "Markdown")
        var selectedFormat = getExportFormatIndex()

        AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("选择导出格式")
            .setSingleChoiceItems(formats, selectedFormat) { _, which ->
                selectedFormat = which
            }
            .setPositiveButton("确定") { _, _ ->
                setExportFormat(formats[selectedFormat])
                tvExportFormat.text = formats[selectedFormat]
                showToast("导出格式已设置为 ${formats[selectedFormat]}")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun initMicrophone(): Boolean {
        return try {
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT
            )

            audioRecord = AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT,
                bufferSize
            )

            audioRecord?.state == AudioRecord.STATE_INITIALIZED
        } catch (e: Exception) {
            Log.e(TAG, "麦克风初始化失败", e)
            false
        }
    }

    private fun startSpeakerRecording(
        recordButton: com.google.android.material.button.MaterialButton,
        statusText: TextView,
        audioData: MutableList<Float>
    ) {
        if (!initMicrophone()) {
            showToast("麦克风初始化失败")
            return
        }

        try {
            audioRecord?.startRecording()
            recordButton.text = "停止录音"
            recordButton.setBackgroundColor(ContextCompat.getColor(this, R.color.apple_red))
            statusText.text = "正在录音... 请说话（建议录音3-5秒，说一句完整的话）"
            audioData.clear()

            isSpeakerRecording = true

            speakerRecordingThread = Thread {
                val buffer = ShortArray(BUFFER_SIZE)
                while (isSpeakerRecording) {
                    try {
                        val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                        if (ret > 0) {
                            synchronized(audioData) {
                                for (i in 0 until ret) {
                                    audioData.add(buffer[i] / 32768.0f)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "声纹录音失败", e)
                        break
                    }
                }
            }
            speakerRecordingThread?.start()

        } catch (e: Exception) {
            Log.e(TAG, "开始声纹录音失败", e)
            showToast("录音失败: ${e.message}")
        }
    }

    private fun stopSpeakerRecording(
        recordButton: com.google.android.material.button.MaterialButton,
        statusText: TextView,
        audioData: MutableList<Float>,
        speakerAudioSamples: MutableList<FloatArray>,
        recordCountText: TextView,
        addButton: com.google.android.material.button.MaterialButton
    ) {
        try {
            isSpeakerRecording = false
            speakerRecordingThread?.join(1000)
            speakerRecordingThread = null

            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            recordButton.text = "开始录音"
            recordButton.setBackgroundColor(ContextCompat.getColor(this, R.color.apple_blue))

            if (audioData.isEmpty()) {
                statusText.text = "录音失败，请重试"
                return
            }

            val audioDataCopy = synchronized(audioData) {
                audioData.toFloatArray()
            }

            speakerAudioSamples.add(audioDataCopy)

            val recordCount = speakerAudioSamples.size
            recordCountText.text = "录音次数: $recordCount/5"
            statusText.text = "已录制 $recordCount 个样本。${if (recordCount >= 3) "可以注册了，" else "建议再录制 ${3 - recordCount} 个样本，"}或继续录制更多样本"

            addButton.isEnabled = recordCount >= 1

        } catch (e: Exception) {
            Log.e(TAG, "停止声纹录音失败", e)
            showToast("停止录音失败: ${e.message}")
        }
    }

    private fun registerSpeakerWithMultipleSamples(
        speakerName: String,
        audioSamples: List<FloatArray>,
        dialog: AlertDialog
    ) {
        Thread {
            // 同时添加到内存和持久化存储
            addSpeakerToBothMemoryAndStorage(speakerName, audioSamples, dialog)
        }.start()
    }

    /**
     * 将声纹添加到内存和持久化存储
     */
    private fun addSpeakerToBothMemoryAndStorage(
        speakerName: String,
        audioSamples: List<FloatArray>,
        dialog: AlertDialog
    ) {
        try {
            var memorySuccess = false
            var storageSuccess = false

            // 1. 先添加到内存中（如果SpeakerRecognition已初始化）
            try {
                if (isSpeakerRecognitionInitialized()) {
                    memorySuccess = addSpeakerToMemory(speakerName, audioSamples)
                    Log.i(TAG, "添加声纹到内存 '$speakerName': $memorySuccess")
                } else {
                    Log.w(TAG, "SpeakerRecognition未初始化，跳过内存添加")
                    memorySuccess = true // 如果未初始化，认为添加成功（稍后会在引擎初始化时恢复）
                }
            } catch (e: Exception) {
                Log.e(TAG, "添加声纹到内存失败: $speakerName", e)
                memorySuccess = false
            }

            // 2. 保存到持久化存储
            storageSuccess = speakerDataManager?.saveSpeaker(speakerName, audioSamples) ?: false
            Log.i(TAG, "保存声纹到存储 '$speakerName': $storageSuccess")

            val overallSuccess = memorySuccess && storageSuccess

            runOnUiThread {
                if (overallSuccess) {
                    showToast("声纹 '$speakerName' 注册成功（使用了 ${audioSamples.size} 个样本）")
                    updateSpeakerCount()
                    dialog.dismiss()
                } else {
                    val errorMsg = when {
                        !memorySuccess && !storageSuccess -> "声纹注册失败（内存和存储都失败）"
                        !memorySuccess -> "声纹注册失败（内存添加失败）"
                        !storageSuccess -> "声纹注册失败（存储保存失败）"
                        else -> "声纹注册失败"
                    }
                    showToast(errorMsg)
                    Log.e(TAG, "注册声纹失败: $speakerName, 内存: $memorySuccess, 存储: $storageSuccess")
                }
            }
        } catch (e: Exception) {
            runOnUiThread {
                showToast("声纹注册异常: ${e.message}")
                Log.e(TAG, "注册声纹异常: $speakerName", e)
            }
        }
    }

    /**
     * 检查SpeakerRecognition是否已初始化
     */
    private fun isSpeakerRecognitionInitialized(): Boolean {
        return try {
            SpeakerRecognition._extractor != null && SpeakerRecognition._manager != null
        } catch (e: Exception) {
            Log.e(TAG, "检查SpeakerRecognition初始化状态失败", e)
            false
        }
    }

    /**
     * 将声纹添加到内存中的SpeakerEmbeddingManager
     */
    private fun addSpeakerToMemory(speakerName: String, audioSamples: List<FloatArray>): Boolean {
        return try {
            val embeddingList = mutableListOf<FloatArray>()

            // 为每个音频样本提取特征
            for (audioSample in audioSamples) {
                val stream = SpeakerRecognition.extractor.createStream()
                stream.acceptWaveform(audioSample, 16000) // 使用16kHz采样率
                stream.inputFinished()

                if (SpeakerRecognition.extractor.isReady(stream)) {
                    val embedding = SpeakerRecognition.extractor.compute(stream)
                    embeddingList.add(embedding)
                }
                stream.release()
            }

            if (embeddingList.isNotEmpty()) {
                val success = SpeakerRecognition.manager.add(speakerName, embeddingList.toTypedArray())
                Log.d(TAG, "内存中添加说话人 '$speakerName' (${embeddingList.size}个特征): ${if (success) "成功" else "失败"}")
                return success
            } else {
                Log.w(TAG, "添加说话人到内存失败：无有效音频样本")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "内存中添加说话人失败", e)
            false
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showSpeakerRegistrationDialog()
            } else {
                showToast("需要麦克风权限才能注册声纹")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isSpeakerRecording) {
            isSpeakerRecording = false
            speakerRecordingThread?.interrupt()
        }
        audioRecord?.release()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    /**
     * 获取录音保存设置
     */
    private fun getSaveRecordingSetting(): Boolean {
        val sharedPrefs = getSharedPreferences("app_settings", MODE_PRIVATE)
        return sharedPrefs.getBoolean("save_recording", true) // 默认为true
    }

    /**
     * 设置录音保存设置
     */
    private fun setSaveRecordingSetting(enabled: Boolean) {
        val sharedPrefs = getSharedPreferences("app_settings", MODE_PRIVATE)
        sharedPrefs.edit().putBoolean("save_recording", enabled).apply()
    }

    /**
     * 获取导出格式设置
     */
    private fun getExportFormat(): String {
        val sharedPrefs = getSharedPreferences("app_settings", MODE_PRIVATE)
        return sharedPrefs.getString("export_format", "Markdown") ?: "Markdown"
    }

    /**
     * 设置导出格式设置
     */
    private fun setExportFormat(format: String) {
        val sharedPrefs = getSharedPreferences("app_settings", MODE_PRIVATE)
        sharedPrefs.edit().putString("export_format", format).apply()
    }

    /**
     * 获取导出格式索引（用于对话框选择）
     */
    private fun getExportFormatIndex(): Int {
        val format = getExportFormat()
        return when (format) {
            "TXT" -> 0
            "Markdown" -> 1
            else -> 1 // 默认为Markdown
        }
    }

    /**
     * 处理悬浮窗功能开关
     */
    private fun handleFloatingWindowToggle(enabled: Boolean) {
        if (enabled) {
            // 检查悬浮窗权限
            if (FloatingWindowPermissionManager.hasOverlayPermission(this)) {
                // 有权限，直接启用
                FloatingWindowSettings.setFloatingWindowEnabled(this, true)
                showToast("悬浮窗功能已开启")
                Log.d(TAG, "悬浮窗功能已开启")
            } else {
                // 没有权限，申请权限
                FloatingWindowPermissionManager.requestOverlayPermission(this, object : FloatingWindowPermissionManager.PermissionCallback {
                    override fun onPermissionGranted() {
                        FloatingWindowSettings.setFloatingWindowEnabled(this@SettingsActivity, true)
                        showToast("悬浮窗功能已开启")
                        Log.d(TAG, "悬浮窗权限申请成功，功能已开启")
                    }

                    override fun onPermissionDenied(reason: String) {
                        // 权限被拒绝，重置开关状态
                        switchFloatingWindow.isChecked = false
                        FloatingWindowSettings.setFloatingWindowEnabled(this@SettingsActivity, false)
                        showToast("悬浮窗权限被拒绝")
                        Log.w(TAG, "悬浮窗权限被拒绝: $reason")
                    }
                })
            }
        } else {
            // 关闭悬浮窗功能
            FloatingWindowSettings.setFloatingWindowEnabled(this, false)
            showToast("悬浮窗功能已关闭")
            Log.d(TAG, "悬浮窗功能已关闭")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: android.content.Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // 处理悬浮窗权限申请结果
        FloatingWindowPermissionManager.handlePermissionResult(this, requestCode, object : FloatingWindowPermissionManager.PermissionCallback {
            override fun onPermissionGranted() {
                switchFloatingWindow.isChecked = true
                FloatingWindowSettings.setFloatingWindowEnabled(this@SettingsActivity, true)
                showToast("悬浮窗功能已开启")
            }

            override fun onPermissionDenied(reason: String) {
                switchFloatingWindow.isChecked = false
                FloatingWindowSettings.setFloatingWindowEnabled(this@SettingsActivity, false)
                FloatingWindowPermissionManager.showPermissionDeniedDialog(this@SettingsActivity) {
                    handleFloatingWindowToggle(true)
                }
            }
        })
    }

}
