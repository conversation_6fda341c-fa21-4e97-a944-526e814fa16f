package com.vectora.vocalmind.server

import android.content.Context
import android.util.Log
import com.vectora.vocalmind.ChatMessage
import com.vectora.vocalmind.LLMProvider
import com.vectora.vocalmind.LLMResult
import com.vectora.vocalmind.SummaryType

/**
 * 服务器LLM提供商
 * 通过服务器API调用LLM服务
 */
class ServerLLMProvider private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ServerLLMProvider"
        
        @Volatile
        private var INSTANCE: ServerLLMProvider? = null
        
        fun getInstance(context: Context): ServerLLMProvider {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerLLMProvider(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val apiService = ServerApiService.getInstance(context)
    
    /**
     * 检查服务器LLM是否可用
     */
    suspend fun isAvailable(): Boolean {
        return try {
            if (!UserAuthManager.isLoggedIn(context)) {
                Log.d(TAG, "用户未登录，服务器LLM不可用")
                return false
            }
            
            val result = apiService.checkServerConnection()
            val isAvailable = result.isSuccess()
            Log.d(TAG, "服务器LLM可用性检查: $isAvailable")
            isAvailable
        } catch (e: Exception) {
            Log.e(TAG, "检查服务器LLM可用性失败", e)
            false
        }
    }

    /**
     * 使用服务器LLM进行ASR内容优化
     */
    suspend fun optimizeAsrContent(originalContent: String): LLMResult {
        Log.d(TAG, "开始服务器LLM ASR优化，内容长度: ${originalContent.length}")
        
        try {
            if (!UserAuthManager.isLoggedIn(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "用户未登录，请先登录服务器账户"
                )
            }
            
            // 构建ASR优化提示词
            val prompt = buildAsrOptimizationPrompt(originalContent)
            val messages = listOf(
                LLMMessage("user", prompt)
            )
            
            // 使用默认提供商（可以配置）
            val result = apiService.chatWithLLM("deepseek", messages)
            
            return when (result) {
                is ApiResult.Success -> {
                    Log.d(TAG, "服务器LLM ASR优化成功")
                    LLMResult(
                        success = true,
                        content = result.data.content,
                        error = null,
                        tokensUsed = result.data.usage?.tokensUsed ?: result.data.usage?.tokens,
                        cost = result.data.usage?.cost
                    )
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "服务器LLM ASR优化失败: ${result.message}")
                    LLMResult(
                        success = false,
                        content = "",
                        error = result.message
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务器LLM ASR优化异常", e)
            return LLMResult(
                success = false,
                content = "",
                error = "服务器LLM ASR优化异常: ${e.message}"
            )
        }
    }

    private fun buildAsrOptimizationPrompt(originalContent: String): String {
        // 使用LLMManager中的统一实现
        return com.vectora.vocalmind.LLMManager.buildAsrOptimizationPrompt(originalContent)
    }

    /**
     * 选择最佳内容：优先使用ASR优化内容，如果没有则使用原始内容
     */
    private fun selectBestContent(originalContent: String, optimizedContent: String): String {
        return if (optimizedContent.isNotBlank()) {
            Log.d(TAG, "使用ASR优化内容进行服务器LLM调用")
            optimizedContent
        } else {
            Log.d(TAG, "ASR优化内容为空，使用原始转录内容进行服务器LLM调用")
            originalContent
        }
    }

    /**
     * 使用服务器LLM生成会议标题
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateMeetingTitle(originalContent: String, optimizedContent: String): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        Log.d(TAG, "开始服务器LLM会议标题生成")

        try {
            if (!UserAuthManager.isLoggedIn(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "用户未登录，请先登录服务器账户"
                )
            }

            // 构建会议标题生成提示词
            val prompt = buildMeetingTitlePrompt(meetingContent)
            val messages = listOf(
                LLMMessage("user", prompt)
            )

            // 使用默认提供商
            val result = apiService.chatWithLLM("deepseek", messages)

            return when (result) {
                is ApiResult.Success -> {
                    Log.d(TAG, "服务器LLM会议标题生成成功")
                    LLMResult(
                        success = true,
                        content = result.data.content,
                        error = null,
                        tokensUsed = result.data.usage?.tokensUsed ?: result.data.usage?.tokens,
                        cost = result.data.usage?.cost
                    )
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "服务器LLM会议标题生成失败: ${result.message}")
                    LLMResult(
                        success = false,
                        content = "",
                        error = result.message
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务器LLM会议标题生成异常", e)
            return LLMResult(
                success = false,
                content = "",
                error = "服务器LLM会议标题生成异常: ${e.message}"
            )
        }
    }

    /**
     * 构建会议标题生成提示词
     */
    private fun buildMeetingTitlePrompt(meetingContent: String): String {
        // 使用LLMManager中的统一实现
        return com.vectora.vocalmind.LLMManager.buildMeetingTitlePrompt(meetingContent)
    }

    /**
     * 使用服务器LLM生成TODO待办事项
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateTodos(originalContent: String, optimizedContent: String, meetingTitle: String = ""): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        Log.d(TAG, "开始服务器LLM TODO生成")

        try {
            if (!UserAuthManager.isLoggedIn(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "用户未登录，请先登录服务器账户"
                )
            }

            // 构建TODO生成提示词
            val prompt = buildTodoGenerationPrompt(meetingContent, meetingTitle)
            val messages = listOf(
                LLMMessage("user", prompt)
            )

            // 使用默认提供商
            val result = apiService.chatWithLLM("deepseek", messages)

            return when (result) {
                is ApiResult.Success -> {
                    Log.d(TAG, "服务器LLM TODO生成成功")
                    LLMResult(
                        success = true,
                        content = result.data.content,
                        error = null,
                        tokensUsed = result.data.usage?.tokensUsed ?: result.data.usage?.tokens,
                        cost = result.data.usage?.cost
                    )
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "服务器LLM TODO生成失败: ${result.message}")
                    LLMResult(
                        success = false,
                        content = "",
                        error = result.message
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务器LLM TODO生成异常", e)
            return LLMResult(
                success = false,
                content = "",
                error = "服务器LLM TODO生成异常: ${e.message}"
            )
        }
    }

    /**
     * 生成会议标签
     */
    suspend fun generateTags(meetingContent: String): LLMResult {
        Log.d(TAG, "开始服务器LLM标签生成")

        try {
            if (!UserAuthManager.isLoggedIn(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "用户未登录，请先登录服务器账户"
                )
            }

            // 构建标签生成提示词
            val prompt = buildTagGenerationPrompt(meetingContent)
            val messages = listOf(
                LLMMessage("user", prompt)
            )

            // 使用默认提供商
            val result = apiService.chatWithLLM("deepseek", messages)

            return when (result) {
                is ApiResult.Success -> {
                    Log.d(TAG, "服务器LLM标签生成成功")
                    LLMResult(
                        success = true,
                        content = result.data.content,
                        error = null,
                        tokensUsed = result.data.usage?.tokensUsed ?: result.data.usage?.tokens,
                        cost = result.data.usage?.cost
                    )
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "服务器LLM标签生成失败: ${result.message}")
                    LLMResult(
                        success = false,
                        content = "",
                        error = result.message
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务器LLM标签生成异常", e)
            return LLMResult(
                success = false,
                content = "",
                error = "服务器LLM标签生成异常: ${e.message}"
            )
        }
    }

    /**
     * 同时生成会议标题和标签
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateTitleAndTags(originalContent: String, optimizedContent: String): LLMResult {
        // 选择最佳内容
        val meetingContent = selectBestContent(originalContent, optimizedContent)
        Log.d(TAG, "开始服务器LLM标题和标签生成")

        try {
            if (!UserAuthManager.isLoggedIn(context)) {
                return LLMResult(
                    success = false,
                    content = "",
                    error = "用户未登录，请先登录服务器账户"
                )
            }

            // 构建标题和标签生成提示词
            val prompt = buildTitleAndTagsPrompt(meetingContent)
            val messages = listOf(
                LLMMessage("user", prompt)
            )

            // 使用默认提供商
            val result = apiService.chatWithLLM("deepseek", messages)

            return when (result) {
                is ApiResult.Success -> {
                    Log.d(TAG, "服务器LLM标题和标签生成成功")
                    LLMResult(
                        success = true,
                        content = result.data.content,
                        error = null,
                        tokensUsed = result.data.usage?.tokensUsed ?: result.data.usage?.tokens,
                        cost = result.data.usage?.cost
                    )
                }
                is ApiResult.Error -> {
                    Log.e(TAG, "服务器LLM标题和标签生成失败: ${result.message}")
                    LLMResult(
                        success = false,
                        content = "",
                        error = result.message
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "服务器LLM标题和标签生成异常", e)
            return LLMResult(
                success = false,
                content = "",
                error = "服务器LLM标题和标签生成异常: ${e.message}"
            )
        }
    }

    /**
     * 构建标签生成提示词
     */
    private fun buildTagGenerationPrompt(meetingContent: String): String {
        // 使用LLMManager中的提示词模板，确保一致性
        return com.vectora.vocalmind.LLMManager.buildTagGenerationPrompt(meetingContent)
    }

    /**
     * 构建标题和标签生成提示词
     */
    private fun buildTitleAndTagsPrompt(meetingContent: String): String {
        // 使用LLMManager中的提示词模板，确保一致性
        return com.vectora.vocalmind.LLMManager.buildTitleAndTagsPrompt(meetingContent)
    }

    /**
     * 构建TODO生成提示词
     */
    private fun buildTodoGenerationPrompt(meetingContent: String, meetingTitle: String): String {
        // 使用LLMManager中的详细提示词模板，确保一致性
        return com.vectora.vocalmind.LLMManager.buildTodoGenerationPrompt(meetingContent, meetingTitle)
    }
}
