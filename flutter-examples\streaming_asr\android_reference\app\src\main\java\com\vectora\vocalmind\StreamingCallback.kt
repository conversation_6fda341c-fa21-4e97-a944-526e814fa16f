package com.vectora.vocalmind

/**
 * 流式响应回调接口
 */
interface StreamingCallback {
    /**
     * 连接建立时调用
     */
    fun onConnectionOpened()
    
    /**
     * 接收到流式数据时调用
     * @param chunk 接收到的文本片段
     */
    fun onDataReceived(chunk: String)
    
    /**
     * 流式响应完成时调用
     * @param fullContent 完整的响应内容
     */
    fun onCompleted(fullContent: String)
    
    /**
     * 发生错误时调用
     * @param error 错误信息
     */
    fun onError(error: String)
    
    /**
     * 连接关闭时调用
     */
    fun onConnectionClosed()
}