package com.vectora.vocalmind

import android.graphics.Color
import android.graphics.Paint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import java.util.*

/**
 * 支持分组和折叠功能的TODO适配器
 * 将已完成的项目显示在底部的可折叠区域
 */
class GroupedTodoAdapter(
    private val onItemClick: (TodoItem) -> Unit,
    private val onItemToggle: (TodoItem) -> Unit,
    private val onItemDelete: (TodoItem) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    
    companion object {
        private const val VIEW_TYPE_INCOMPLETE = 0
        private const val VIEW_TYPE_COMPLETED_HEADER = 1
        private const val VIEW_TYPE_COMPLETED = 2
    }
    
    private var incompleteTodos = mutableListOf<TodoItem>()
    private var completedTodos = mutableListOf<TodoItem>()
    private var isCompletedExpanded = false
    
    // 用于显示的数据项
    private val displayItems = mutableListOf<DisplayItem>()
    
    sealed class DisplayItem {
        data class TodoItemData(val todo: TodoItem, val isCompleted: Boolean) : DisplayItem()
        data class GroupHeader(val title: String, val count: Int, val isExpanded: Boolean) : DisplayItem()
    }
    
    fun updateTodos(newTodos: List<TodoItem>) {
        // 分离已完成和未完成的项目
        incompleteTodos.clear()
        completedTodos.clear()
        
        newTodos.forEach { todo ->
            if (todo.isCompleted) {
                completedTodos.add(todo)
            } else {
                incompleteTodos.add(todo)
            }
        }
        
        // 排序：未完成的按优先级和创建时间排序，已完成的按完成时间排序
        incompleteTodos.sortWith(compareBy<TodoItem> { 
            when (it.priority) {
                TodoItem.Priority.URGENT -> 0
                TodoItem.Priority.HIGH -> 1
                TodoItem.Priority.MEDIUM -> 2
                TodoItem.Priority.LOW -> 3
            }
        }.thenBy { it.createdAt })
        
        completedTodos.sortByDescending { it.completedAt ?: it.createdAt }
        
        updateDisplayItems()
        notifyDataSetChanged()
    }
    
    private fun updateDisplayItems() {
        displayItems.clear()
        
        // 添加未完成的项目
        incompleteTodos.forEach { todo ->
            displayItems.add(DisplayItem.TodoItemData(todo, false))
        }
        
        // 如果有已完成的项目，添加分组头部
        if (completedTodos.isNotEmpty()) {
            displayItems.add(DisplayItem.GroupHeader("已完成", completedTodos.size, isCompletedExpanded))
            
            // 如果展开状态，添加已完成的项目
            if (isCompletedExpanded) {
                completedTodos.forEach { todo ->
                    displayItems.add(DisplayItem.TodoItemData(todo, true))
                }
            }
        }
    }
    
    fun toggleCompletedSection() {
        isCompletedExpanded = !isCompletedExpanded
        updateDisplayItems()
        notifyDataSetChanged()
    }

    /**
     * 获取所有todos（用于拖拽排序后的回调）
     */
    fun getAllTodos(): List<TodoItem> {
        return incompleteTodos + completedTodos
    }

    /**
     * 获取显示项目（用于拖拽排序时访问）
     */
    fun getDisplayItems(): List<DisplayItem> {
        return displayItems.toList()
    }

    /**
     * 移动项目（支持拖拽排序）
     * 只允许在未完成的项目之间拖拽
     */
    fun moveItem(fromPosition: Int, toPosition: Int): Boolean {
        // 检查是否都是未完成的项目
        val fromItem = displayItems.getOrNull(fromPosition) as? DisplayItem.TodoItemData ?: return false
        val toItem = displayItems.getOrNull(toPosition) as? DisplayItem.TodoItemData ?: return false

        // 只允许在未完成的项目之间拖拽
        if (fromItem.isCompleted || toItem.isCompleted) {
            return false
        }

        // 在未完成列表中找到对应的索引
        val fromTodo = fromItem.todo
        val toTodo = toItem.todo

        val fromIndex = incompleteTodos.indexOf(fromTodo)
        val toIndex = incompleteTodos.indexOf(toTodo)

        if (fromIndex == -1 || toIndex == -1) return false

        // 移动项目
        if (fromIndex < toIndex) {
            for (i in fromIndex until toIndex) {
                Collections.swap(incompleteTodos, i, i + 1)
            }
        } else {
            for (i in fromIndex downTo toIndex + 1) {
                Collections.swap(incompleteTodos, i, i - 1)
            }
        }

        // 更新显示项目
        updateDisplayItems()
        notifyItemMoved(fromPosition, toPosition)

        return true
    }
    
    override fun getItemViewType(position: Int): Int {
        return when (displayItems[position]) {
            is DisplayItem.TodoItemData -> {
                if (displayItems[position].let { it as DisplayItem.TodoItemData }.isCompleted) {
                    VIEW_TYPE_COMPLETED
                } else {
                    VIEW_TYPE_INCOMPLETE
                }
            }
            is DisplayItem.GroupHeader -> VIEW_TYPE_COMPLETED_HEADER
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_COMPLETED_HEADER -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_todo_group_header, parent, false)
                GroupHeaderViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_todo, parent, false)
                TodoViewHolder(view)
            }
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is GroupHeaderViewHolder -> {
                val header = displayItems[position] as DisplayItem.GroupHeader
                holder.bind(header)
            }
            is TodoViewHolder -> {
                val todoData = displayItems[position] as DisplayItem.TodoItemData
                holder.bind(todoData.todo)
            }
        }
    }
    
    override fun getItemCount(): Int = displayItems.size
    
    // 分组头部ViewHolder
    inner class GroupHeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivExpandCollapse: ImageView = itemView.findViewById(R.id.iv_expand_collapse)
        private val tvGroupTitle: TextView = itemView.findViewById(R.id.tv_group_title)
        private val tvCount: TextView = itemView.findViewById(R.id.tv_count)
        
        fun bind(header: DisplayItem.GroupHeader) {
            tvGroupTitle.text = header.title
            tvCount.text = header.count.toString()
            
            // 设置展开/折叠图标
            if (header.isExpanded) {
                ivExpandCollapse.rotation = 180f
            } else {
                ivExpandCollapse.rotation = 0f
            }
            
            itemView.setOnClickListener {
                toggleCompletedSection()
            }
        }
    }
    
    // TODO项ViewHolder（复用原有逻辑）
    inner class TodoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: CardView = itemView.findViewById(R.id.card_todo)
        private val checkBox: CheckBox = itemView.findViewById(R.id.cb_completed)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_title)
        private val tvDescription: TextView = itemView.findViewById(R.id.tv_description)
        private val tvCategory: TextView = itemView.findViewById(R.id.tv_category)
        private val tvPriority: TextView = itemView.findViewById(R.id.tv_priority)
        private val tvDueDate: TextView = itemView.findViewById(R.id.tv_due_date)
        private val tvCreatedAt: TextView = itemView.findViewById(R.id.tv_created_at)
        private val btnDelete: ImageButton = itemView.findViewById(R.id.btn_delete)
        private val layoutDueDate: LinearLayout = itemView.findViewById(R.id.layout_due_date)
        private val ivDragHandle: ImageView = itemView.findViewById(R.id.iv_drag_handle)
        
        fun bind(todoItem: TodoItem) {
            // 设置基本信息
            tvTitle.text = todoItem.title
            tvDescription.text = todoItem.description
            tvCategory.text = todoItem.category
            tvCreatedAt.text = todoItem.getFormattedCreatedAt()
            
            // 设置完成状态
            checkBox.isChecked = todoItem.isCompleted
            updateCompletionStyle(todoItem.isCompleted)
            
            // 已完成的项目隐藏拖拽手柄
            ivDragHandle.visibility = if (todoItem.isCompleted) View.GONE else View.VISIBLE
            
            // 设置优先级
            tvPriority.text = todoItem.priority.displayName
            tvPriority.setBackgroundColor(Color.parseColor(todoItem.priority.color))
            tvPriority.setTextColor(Color.WHITE)
            
            // 设置截止时间
            if (todoItem.dueDate != null) {
                layoutDueDate.visibility = View.VISIBLE
                tvDueDate.text = todoItem.getFormattedDueDate()
                
                // 根据截止时间状态设置颜色
                when {
                    todoItem.isOverdue() -> {
                        tvDueDate.setTextColor(Color.parseColor("#dc3545"))
                        tvDueDate.text = "⚠️ ${todoItem.getFormattedDueDate()}"
                    }
                    todoItem.isDueSoon() -> {
                        tvDueDate.setTextColor(Color.parseColor("#fd7e14"))
                        tvDueDate.text = "🔔 ${todoItem.getFormattedDueDate()}"
                    }
                    else -> {
                        tvDueDate.setTextColor(Color.parseColor("#6c757d"))
                        tvDueDate.text = "📅 ${todoItem.getFormattedDueDate()}"
                    }
                }
            } else {
                layoutDueDate.visibility = View.GONE
            }
            
            // 设置描述可见性
            if (todoItem.description.isNotEmpty()) {
                tvDescription.visibility = View.VISIBLE
            } else {
                tvDescription.visibility = View.GONE
            }
            
            // 设置点击事件
            cardView.setOnClickListener {
                onItemClick(todoItem)
            }
            
            checkBox.setOnClickListener {
                onItemToggle(todoItem)
            }
            
            btnDelete.setOnClickListener {
                onItemDelete(todoItem)
            }
            
            // 设置卡片样式
            updateCardStyle(todoItem)
        }
        
        private fun updateCompletionStyle(isCompleted: Boolean) {
            if (isCompleted) {
                tvTitle.paintFlags = tvTitle.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                tvTitle.alpha = 0.6f
                tvDescription.alpha = 0.6f
                tvCategory.alpha = 0.6f
                tvCreatedAt.alpha = 0.6f
                cardView.alpha = 0.8f
            } else {
                tvTitle.paintFlags = tvTitle.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                tvTitle.alpha = 1.0f
                tvDescription.alpha = 1.0f
                tvCategory.alpha = 1.0f
                tvCreatedAt.alpha = 1.0f
                cardView.alpha = 1.0f
            }
        }
        
        private fun updateCardStyle(todoItem: TodoItem) {
            // 设置卡片背景色
            cardView.setCardBackgroundColor(Color.WHITE)

            // 设置卡片阴影
            cardView.cardElevation = if (todoItem.isCompleted) 1f else 4f
        }
    }
}

/**
 * 分组TODO拖拽排序回调类
 * 只允许在未完成的项目之间进行拖拽排序
 */
class GroupedTodoItemTouchHelperCallback(
    private val adapter: GroupedTodoAdapter,
    private val onItemMoved: (List<TodoItem>) -> Unit
) : ItemTouchHelper.Callback() {

    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        // 只有未完成的项目才能拖拽
        if (viewHolder is GroupedTodoAdapter.TodoViewHolder) {
            val position = viewHolder.adapterPosition
            val displayItem = adapter.getDisplayItems().getOrNull(position) as? GroupedTodoAdapter.DisplayItem.TodoItemData

            if (displayItem != null && !displayItem.isCompleted) {
                val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
                return makeMovementFlags(dragFlags, 0)
            }
        }

        return makeMovementFlags(0, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val fromPosition = viewHolder.adapterPosition
        val toPosition = target.adapterPosition

        return adapter.moveItem(fromPosition, toPosition)
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // 不支持滑动删除
    }

    override fun isLongPressDragEnabled(): Boolean = true

    override fun isItemViewSwipeEnabled(): Boolean = false

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)

        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
            viewHolder?.itemView?.alpha = 0.8f
            viewHolder?.itemView?.scaleX = 1.05f
            viewHolder?.itemView?.scaleY = 1.05f
        }
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)

        viewHolder.itemView.alpha = 1.0f
        viewHolder.itemView.scaleX = 1.0f
        viewHolder.itemView.scaleY = 1.0f

        // 通知排序变化
        onItemMoved(adapter.getAllTodos())
    }
}
