<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background_apple"
    android:padding="0dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="20dp"
        android:background="@color/apple_system_background">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="提示"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/apple_label"
            android:gravity="center" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close_apple"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/apple_gray_5" />

    <!-- 内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="300dp"
        android:background="@color/apple_system_background">

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="30dp"
            android:textSize="15sp"
            android:textColor="@color/apple_secondary_label"
            android:lineSpacingExtra="4dp"
            android:text="消息内容" />

    </ScrollView>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/apple_gray_5" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/apple_system_background">

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btn_negative"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="取消"
            android:textSize="16sp"
            android:textColor="@color/apple_secondary_label"
            android:background="?android:attr/selectableItemBackground"
            style="?android:attr/borderlessButtonStyle" />

        <!-- 分割线 -->
        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/apple_gray_5" />

        <!-- 确定按钮 -->
        <Button
            android:id="@+id/btn_positive"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="确定"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/apple_blue"
            android:background="?android:attr/selectableItemBackground"
            style="?android:attr/borderlessButtonStyle" />

    </LinearLayout>

</LinearLayout>