package com.vectora.vocalmind

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.util.Log
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 录音文件管理器
 * 负责录音文件的保存和管理，采用WAV格式
 */
class AudioRecordingManager(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioRecordingManager"
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val RECORDINGS_DIR = "meeting_recordings"
    }
    
    private var currentRecordingFile: File? = null
    private var audioOutputStream: FileOutputStream? = null
    private var isRecording = false
    
    /**
     * 开始录音到文件
     */
    fun startRecording(meetingId: String): String? {
        return try {
            // 创建录音目录
            val recordingsDir = File(context.filesDir, RECORDINGS_DIR)
            if (!recordingsDir.exists()) {
                recordingsDir.mkdirs()
            }
            
            // 生成录音文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "meeting_${meetingId}_${timestamp}.wav"
            currentRecordingFile = File(recordingsDir, fileName)
            
            // 创建文件输出流
            audioOutputStream = FileOutputStream(currentRecordingFile!!)
            
            // 写入WAV文件头（先写入占位符，录音结束后更新）
            writeWavHeader(audioOutputStream!!, 0)
            
            isRecording = true
            Log.i(TAG, "开始录音到文件: ${currentRecordingFile!!.absolutePath}")
            Log.d(TAG, "录音目录: ${recordingsDir.absolutePath}")
            Log.d(TAG, "录音文件名: $fileName")
            
            currentRecordingFile!!.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            null
        }
    }
    
    /**
     * 写入音频数据
     */
    fun writeAudioData(audioData: ShortArray, length: Int) {
        if (!isRecording || audioOutputStream == null) return
        
        try {
            // 将Short数组转换为字节数组（16位PCM）
            val byteBuffer = ByteArray(length * 2)
            for (i in 0 until length) {
                val sample = audioData[i]
                byteBuffer[i * 2] = (sample.toInt() and 0xFF).toByte()
                byteBuffer[i * 2 + 1] = ((sample.toInt() shr 8) and 0xFF).toByte()
            }
            
            audioOutputStream!!.write(byteBuffer)
        } catch (e: Exception) {
            Log.e(TAG, "写入音频数据失败", e)
        }
    }
    
    /**
     * 停止录音
     */
    fun stopRecording(): String? {
        if (!isRecording) return null
        
        return try {
            isRecording = false
            
            // 关闭输出流
            audioOutputStream?.close()
            audioOutputStream = null
            
            val recordingFile = currentRecordingFile
            if (recordingFile != null && recordingFile.exists()) {
                // 更新WAV文件头
                updateWavHeader(recordingFile)
                Log.i(TAG, "录音完成: ${recordingFile.absolutePath}, 文件大小: ${recordingFile.length()} bytes")
                recordingFile.absolutePath
            } else {
                Log.w(TAG, "录音文件不存在")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
            null
        } finally {
            currentRecordingFile = null
        }
    }
    
    /**
     * 删除录音文件
     */
    fun deleteRecording(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            if (file.exists()) {
                val deleted = file.delete()
                Log.i(TAG, "删除录音文件: $filePath, 结果: $deleted")
                deleted
            } else {
                Log.w(TAG, "录音文件不存在: $filePath")
                true // 文件不存在也算删除成功
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除录音文件失败: $filePath", e)
            false
        }
    }
    
    /**
     * 获取录音文件大小（MB）
     */
    fun getRecordingSize(filePath: String): Double {
        return try {
            val file = File(filePath)
            if (file.exists()) {
                file.length() / (1024.0 * 1024.0)
            } else {
                0.0
            }
        } catch (e: Exception) {
            0.0
        }
    }
    
    /**
     * 清理所有录音文件
     */
    fun clearAllRecordings(): Boolean {
        return try {
            val recordingsDir = File(context.filesDir, RECORDINGS_DIR)
            if (recordingsDir.exists()) {
                val files = recordingsDir.listFiles()
                files?.forEach { file ->
                    if (file.isFile && file.name.endsWith(".wav")) {
                        file.delete()
                    }
                }
                Log.i(TAG, "清理了 ${files?.size ?: 0} 个录音文件")
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "清理录音文件失败", e)
            false
        }
    }
    
    /**
     * 写入WAV文件头
     */
    private fun writeWavHeader(out: FileOutputStream, dataSize: Int) {
        val header = ByteArray(44)
        val totalDataLen = dataSize + 36
        val byteRate = SAMPLE_RATE * 2 // 16位单声道
        
        // RIFF header
        header[0] = 'R'.code.toByte()
        header[1] = 'I'.code.toByte()
        header[2] = 'F'.code.toByte()
        header[3] = 'F'.code.toByte()
        header[4] = (totalDataLen and 0xff).toByte()
        header[5] = ((totalDataLen shr 8) and 0xff).toByte()
        header[6] = ((totalDataLen shr 16) and 0xff).toByte()
        header[7] = ((totalDataLen shr 24) and 0xff).toByte()
        
        // WAVE header
        header[8] = 'W'.code.toByte()
        header[9] = 'A'.code.toByte()
        header[10] = 'V'.code.toByte()
        header[11] = 'E'.code.toByte()
        
        // fmt chunk
        header[12] = 'f'.code.toByte()
        header[13] = 'm'.code.toByte()
        header[14] = 't'.code.toByte()
        header[15] = ' '.code.toByte()
        header[16] = 16 // fmt chunk size
        header[17] = 0
        header[18] = 0
        header[19] = 0
        header[20] = 1 // PCM format
        header[21] = 0
        header[22] = 1 // mono
        header[23] = 0
        header[24] = (SAMPLE_RATE and 0xff).toByte()
        header[25] = ((SAMPLE_RATE shr 8) and 0xff).toByte()
        header[26] = ((SAMPLE_RATE shr 16) and 0xff).toByte()
        header[27] = ((SAMPLE_RATE shr 24) and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte()
        header[29] = ((byteRate shr 8) and 0xff).toByte()
        header[30] = ((byteRate shr 16) and 0xff).toByte()
        header[31] = ((byteRate shr 24) and 0xff).toByte()
        header[32] = 2 // block align
        header[33] = 0
        header[34] = 16 // bits per sample
        header[35] = 0
        
        // data chunk
        header[36] = 'd'.code.toByte()
        header[37] = 'a'.code.toByte()
        header[38] = 't'.code.toByte()
        header[39] = 'a'.code.toByte()
        header[40] = (dataSize and 0xff).toByte()
        header[41] = ((dataSize shr 8) and 0xff).toByte()
        header[42] = ((dataSize shr 16) and 0xff).toByte()
        header[43] = ((dataSize shr 24) and 0xff).toByte()
        
        out.write(header)
    }
    
    /**
     * 更新WAV文件头（录音结束后调用）
     */
    private fun updateWavHeader(file: File) {
        try {
            val dataSize = (file.length() - 44).toInt()
            val totalDataLen = dataSize + 36
            
            RandomAccessFile(file, "rw").use { raf ->
                // 更新文件总长度
                raf.seek(4)
                raf.write(totalDataLen and 0xff)
                raf.write((totalDataLen shr 8) and 0xff)
                raf.write((totalDataLen shr 16) and 0xff)
                raf.write((totalDataLen shr 24) and 0xff)
                
                // 更新数据长度
                raf.seek(40)
                raf.write(dataSize and 0xff)
                raf.write((dataSize shr 8) and 0xff)
                raf.write((dataSize shr 16) and 0xff)
                raf.write((dataSize shr 24) and 0xff)
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新WAV文件头失败", e)
        }
    }
}
