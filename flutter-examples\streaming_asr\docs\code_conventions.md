# 代码规范和项目结构

本文档旨在为 `streaming_asr` 项目提供一套标准的开发规范和架构指南。遵循这些准则有助于保持代码库的整洁、可维护和可扩展。

## 核心原则

*   **分层架构 (Layered Architecture)**: 将代码按职责分为不同的层次 (UI, 业务逻辑, 服务), 实现关注点分离。
*   **功能驱动 (Feature-First)**: 代码按功能模块组织, 而不是按类型组织。所有与特定功能 (如 ASR) 相关的文件都放在同一个目录下。
*   **响应式状态管理 (Reactive State Management)**: 使用 `Riverpod` 作为状态管理库, 实现单向数据流和响应式UI更新。

## 1. 目录结构

项目 `lib` 目录遵循以下结构：

```
lib/
└── src/
    ├── core/                # 核心/共享代码
    │   ├── providers.dart   # 全局或核心服务的Provider
    │   └── utils.dart       # 共享工具函数
    │
    ├── features/            # 功能模块目录
    │   └── asr/             # ASR (语音识别) 功能
    │       ├── application/ # 应用层: 业务逻辑
    │       │   └── asr_provider.dart
    │       ├── domain/      # 领域层: 数据模型/状态
    │       │   └── asr_state.dart
    │       ├── presentation/  # 表现层: UI Widgets
    │       │   └── asr_screen.dart
    │       └── service/     # 服务层: 封装外部依赖
    │           └── asr_service.dart
    │
    └── main.dart            # 应用主入口
```

## 2. 分层架构详解

我们的架构遵循清晰的依赖规则: **表现层 -> 应用层 -> 服务层**。上层可以调用下层, 但下层永远不能直接调用上层。

```mermaid
graph TD
    A["表现层 (Presentation)"] --> B["应用层 (Application)"];
    B --> C["服务层 (Service)"];
    B --> D["领域层 (Domain)"];
    A --> D;
```

*   **表现层 (Presentation Layer)**
    *   **职责**: 构建和展示UI, 捕获用户输入。
    *   **内容**: Flutter Widgets, 通常是 `ConsumerWidget` 或 `ConsumerStatefulWidget`。
    *   **规则**: UI层应保持“愚笨”, 它只负责展示状态和发送用户事件, 不包含任何业务逻辑。它通过 `ref.watch` 监听状态变化, 通过 `ref.read(...).notifier` 调用应用层的方法。

*   **应用层 (Application Layer)**
    *   **职责**: 编排业务逻辑, 响应UI事件, 管理状态。
    *   **内容**: Riverpod 的 `Notifier` 类。
    *   **规则**: 这是业务逻辑的核心。它调用服务层来获取数据或执行操作, 然后处理这些数据并更新状态 (`state`)。它定义了“做什么”。

*   **领域层 (Domain Layer)**
    *   **职责**: 定义应用的核心数据结构和状态。
    *   **内容**: 不可变的 (immutable) 状态类和数据模型。
    *   **规则**: 这些类应该是纯数据容器, 不包含业务逻辑。

*   **服务层 (Service Layer)**
    *   **职责**: 封装与外部世界的交互细节。
    *   **内容**: 封装了第三方库 (如 `sherpa_onnx`)、API请求 (`http`/`dio`) 或本地数据库 (`sqflite`) 的类。
    *   **规则**: 它为应用层提供简单的接口, 并隐藏了底层实现的复杂性。它定义了“如何做”。

## 3. 状态管理 (State Management)

我们使用 **Riverpod** 进行状态管理, 遵循单向数据流 (Unidirectional Data Flow) 模式。

1.  **事件 (Event)**: 用户在UI上进行操作 (如点击按钮)。
2.  **调用 (Call)**: UI (`ConsumerWidget`) 通过 `ref.read(provider.notifier).method()` 调用 `Notifier` 中的方法。
3.  **逻辑 (Logic)**: `Notifier` (应用层) 执行业务逻辑, 可能会调用 `Service`。
4.  **更新 (Update)**: `Notifier` 创建一个新的状态对象 (`state = state.copyWith(...)`) 来更新其状态。
5.  **重建 (Rebuild)**: Riverpod 自动检测到状态变化, 并只重建那些正在 `watch` 该状态的UI组件。

这种模式使得状态的流动变得可预测, 易于调试。

## 4. 如何添加新功能

当需要添加一个新功能时 (例如, “用户设置”), 请遵循以下步骤:

1.  在 `lib/src/features/` 下创建一个新的目录, 例如 `settings`。
2.  如果需要, 在 `settings/domain/` 中定义你的状态类 `settings_state.dart`。
3.  如果需要与外部服务 (如 `SharedPreferences`) 交互, 在 `settings/service/` 中创建 `settings_service.dart`。
4.  在 `settings/application/` 中创建 `settings_provider.dart`, 编写 `Notifier` 来管理业务逻辑。
5.  在 `settings/presentation/` 中创建 `settings_screen.dart` 作为UI。
6.  最后, 将 `SettingsScreen` 集成到应用的导航流程中 (例如, 在 `main.dart` 的 `BottomNavigationBar` 中添加一个新标签)。
