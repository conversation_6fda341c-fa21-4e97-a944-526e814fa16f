<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <!-- 悬浮按钮主体 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_floating_button"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        app:cardCornerRadius="28dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="#2196F3">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 录音图标 -->
            <ImageView
                android:id="@+id/iv_mic_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@android:drawable/ic_btn_speak_now"
                android:tint="@android:color/white"
                android:contentDescription="录音按钮" />

            <!-- 脉动动画背景 -->
            <View
                android:id="@+id/view_pulse_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/floating_button_pulse"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <!-- 下拉菜单（长按显示） -->
    <LinearLayout
        android:id="@+id/ll_action_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="70dp"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 打开主应用按钮 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cv_open_app"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="28dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#4CAF50">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@android:drawable/ic_menu_view"
                android:tint="@android:color/white"
                android:contentDescription="打开应用" />

        </androidx.cardview.widget.CardView>

        <!-- 关闭悬浮窗按钮 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cv_close_floating"
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:cardCornerRadius="28dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#F44336">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:tint="@android:color/white"
                android:contentDescription="关闭悬浮窗" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</FrameLayout>
