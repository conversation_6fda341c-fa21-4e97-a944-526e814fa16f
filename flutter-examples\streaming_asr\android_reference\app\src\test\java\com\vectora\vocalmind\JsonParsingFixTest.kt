package com.vectora.vocalmind

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vectora.vocalmind.server.*
import org.junit.Test

/**
 * JSON解析修复测试
 * 专门测试泛型类型的JSON解析修复
 */
class JsonParsingFixTest {

    @Test
    fun testLoginDataJsonParsing() {
        val gson = Gson()
        
        // 模拟服务器返回的登录响应JSON
        val loginJson = """
        {
            "success": true,
            "data": {
                "access_token": "test_token_123",
                "token_type": "Bearer",
                "expires_in": 86400,
                "user": {
                    "email": "<EMAIL>",
                    "name": "Test User",
                    "id": "test-id-123",
                    "role": "user",
                    "is_active": true,
                    "is_verified": true,
                    "total_llm_calls": 0,
                    "monthly_llm_calls": 0,
                    "created_at": "2025-07-03T11:55:17",
                    "updated_at": "2025-07-05T14:00:41",
                    "last_login_at": "2025-07-05T14:00:42"
                }
            },
            "message": "登录成功"
        }
        """.trimIndent()
        
        // 使用TypeToken正确解析泛型类型
        val type = object : TypeToken<ApiResponse<LoginData>>() {}.type
        val response = gson.fromJson<ApiResponse<LoginData>>(loginJson, type)
        
        // 验证解析结果
        assert(response.success)
        assert(response.data != null)
        assert(response.data!!.accessToken == "test_token_123")
        assert(response.data!!.tokenType == "Bearer")
        assert(response.data!!.expiresIn == 86400L)
        assert(response.data!!.user.email == "<EMAIL>")
        assert(response.data!!.user.name == "Test User")
        assert(response.data!!.user.role == "user")
        assert(response.message == "登录成功")
        
        println("✓ LoginData JSON解析测试通过")
    }
    
    @Test
    fun testLLMChatDataJsonParsing() {
        val gson = Gson()
        
        // 模拟服务器返回的LLM聊天响应JSON
        val chatJson = """
        {
            "success": true,
            "data": {
                "content": "这是AI的回复内容",
                "usage": {
                    "tokens_used": 150,
                    "cost": 0.003
                }
            },
            "message": "聊天完成"
        }
        """.trimIndent()
        
        // 使用TypeToken正确解析泛型类型
        val type = object : TypeToken<ApiResponse<LLMChatData>>() {}.type
        val response = gson.fromJson<ApiResponse<LLMChatData>>(chatJson, type)
        
        // 验证解析结果
        assert(response.success)
        assert(response.data != null)
        assert(response.data!!.content == "这是AI的回复内容")
        assert(response.data!!.usage != null)
        assert(response.data!!.usage!!.tokensUsed == 150)
        assert(response.data!!.usage!!.cost == 0.003)
        assert(response.message == "聊天完成")
        
        println("✓ LLMChatData JSON解析测试通过")
    }
    
    @Test
    fun testUserJsonParsing() {
        val gson = Gson()
        
        // 模拟服务器返回的用户信息响应JSON
        val userJson = """
        {
            "success": true,
            "data": {
                "email": "<EMAIL>",
                "name": "Example User",
                "id": "user-id-456",
                "role": "admin",
                "is_active": true,
                "is_verified": true,
                "total_llm_calls": 100,
                "monthly_llm_calls": 50,
                "created_at": "2025-01-01T00:00:00",
                "updated_at": "2025-07-05T12:00:00",
                "last_login_at": "2025-07-05T14:00:00"
            },
            "message": "获取用户信息成功"
        }
        """.trimIndent()
        
        // 使用TypeToken正确解析泛型类型
        val type = object : TypeToken<ApiResponse<User>>() {}.type
        val response = gson.fromJson<ApiResponse<User>>(userJson, type)
        
        // 验证解析结果
        assert(response.success)
        assert(response.data != null)
        assert(response.data!!.email == "<EMAIL>")
        assert(response.data!!.name == "Example User")
        assert(response.data!!.id == "user-id-456")
        assert(response.data!!.role == "admin")
        assert(response.data!!.isActive)
        assert(response.data!!.isVerified)
        assert(response.data!!.totalLlmCalls == 100)
        assert(response.data!!.monthlyLlmCalls == 50)
        assert(response.message == "获取用户信息成功")
        
        println("✓ User JSON解析测试通过")
    }
}
