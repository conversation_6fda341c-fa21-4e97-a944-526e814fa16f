<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_streaming_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="AI总结"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/apple_label"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- 副标题/状态 -->
    <TextView
        android:id="@+id/tv_streaming_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="正在连接..."
        android:textSize="14sp"
        android:textColor="@color/apple_secondary_label"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 进度指示器 -->
    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        style="?android:attr/progressBarStyle" />

    <!-- 流式内容显示区域 -->
    <ScrollView
        android:id="@+id/scroll_streaming_content"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@drawable/rounded_background_secondary"
        android:padding="12dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false">

        <TextView
            android:id="@+id/tv_streaming_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="14sp"
            android:textColor="@color/apple_label"
            android:lineSpacingExtra="2dp"
            />

    </ScrollView>

    <!-- 提示文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 内容正在实时生成，请耐心等待..."
        android:textSize="12sp"
        android:textColor="@color/apple_tertiary_label"
        android:gravity="center"
        android:layout_marginTop="12dp" />

</LinearLayout>
