package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * TODO管理器
 * 负责TODO项的CRUD操作和数据持久化
 */
class TodoManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "TodoManager"
        private const val PREFS_NAME = "todo_manager_prefs"
        private const val KEY_TODOS = "todos"
        private const val KEY_NEXT_ORDER = "next_order"
        
        @Volatile
        private var INSTANCE: TodoManager? = null
        
        fun getInstance(context: Context): TodoManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TodoManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val todoCache = ConcurrentHashMap<String, TodoItem>()
    private var nextOrder = 0
    
    init {
        loadTodosFromPrefs()
    }
    
    /**
     * 从SharedPreferences加载TODO项
     */
    private fun loadTodosFromPrefs() {
        try {
            val todosJson = prefs.getString(KEY_TODOS, "[]")
            nextOrder = prefs.getInt(KEY_NEXT_ORDER, 0)
            
            if (!todosJson.isNullOrEmpty()) {
                val jsonArray = JSONArray(todosJson)
                todoCache.clear()
                
                for (i in 0 until jsonArray.length()) {
                    val todoJson = jsonArray.getJSONObject(i).toString()
                    val todoItem = TodoItem.fromJson(todoJson)
                    if (todoItem != null) {
                        todoCache[todoItem.id] = todoItem
                    }
                }
                
                Log.d(TAG, "加载了 ${todoCache.size} 个TODO项")
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载TODO项失败", e)
        }
    }
    
    /**
     * 保存TODO项到SharedPreferences
     */
    private fun saveTodosToPrefs() {
        try {
            val jsonArray = JSONArray()
            todoCache.values.forEach { todoItem ->
                jsonArray.put(JSONObject(todoItem.toJson()))
            }
            
            prefs.edit()
                .putString(KEY_TODOS, jsonArray.toString())
                .putInt(KEY_NEXT_ORDER, nextOrder)
                .apply()
                
            Log.d(TAG, "保存了 ${todoCache.size} 个TODO项")
        } catch (e: Exception) {
            Log.e(TAG, "保存TODO项失败", e)
        }
    }
    
    /**
     * 添加TODO项
     */
    fun addTodo(todoItem: TodoItem): Boolean {
        return try {
            val itemWithOrder = todoItem.copy(order = nextOrder++)
            todoCache[itemWithOrder.id] = itemWithOrder
            saveTodosToPrefs()
            Log.d(TAG, "添加TODO项成功: ${itemWithOrder.title}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "添加TODO项失败", e)
            false
        }
    }
    
    /**
     * 批量添加TODO项
     */
    fun addTodos(todoItems: List<TodoItem>): Boolean {
        return try {
            todoItems.forEach { todoItem ->
                val itemWithOrder = todoItem.copy(order = nextOrder++)
                todoCache[itemWithOrder.id] = itemWithOrder
            }
            saveTodosToPrefs()
            Log.d(TAG, "批量添加TODO项成功: ${todoItems.size} 个")
            true
        } catch (e: Exception) {
            Log.e(TAG, "批量添加TODO项失败", e)
            false
        }
    }
    
    /**
     * 更新TODO项
     */
    fun updateTodo(todoItem: TodoItem): Boolean {
        return try {
            if (todoCache.containsKey(todoItem.id)) {
                todoCache[todoItem.id] = todoItem
                saveTodosToPrefs()
                Log.d(TAG, "更新TODO项成功: ${todoItem.title}")
                true
            } else {
                Log.w(TAG, "TODO项不存在: ${todoItem.id}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新TODO项失败", e)
            false
        }
    }
    
    /**
     * 删除TODO项
     */
    fun deleteTodo(todoId: String): Boolean {
        return try {
            val removed = todoCache.remove(todoId)
            if (removed != null) {
                saveTodosToPrefs()
                Log.d(TAG, "删除TODO项成功: ${removed.title}")
                true
            } else {
                Log.w(TAG, "TODO项不存在: $todoId")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除TODO项失败", e)
            false
        }
    }
    
    /**
     * 获取单个TODO项
     */
    fun getTodo(todoId: String): TodoItem? {
        return todoCache[todoId]
    }

    /**
     * 根据ID获取TODO项（别名方法）
     */
    fun getTodoById(todoId: String): TodoItem? {
        return getTodo(todoId)
    }
    
    /**
     * 获取所有TODO项
     */
    fun getAllTodos(): List<TodoItem> {
        return todoCache.values.sortedBy { it.order }
    }
    
    /**
     * 获取未完成的TODO项
     */
    fun getIncompleteTodos(): List<TodoItem> {
        return todoCache.values.filter { !it.isCompleted }.sortedBy { it.order }
    }
    
    /**
     * 获取已完成的TODO项
     */
    fun getCompletedTodos(): List<TodoItem> {
        return todoCache.values.filter { it.isCompleted }.sortedByDescending { it.completedAt ?: 0 }
    }
    
    /**
     * 根据优先级获取TODO项
     */
    fun getTodosByPriority(priority: TodoItem.Priority): List<TodoItem> {
        return todoCache.values.filter { it.priority == priority }.sortedBy { it.order }
    }
    
    /**
     * 根据分类获取TODO项
     */
    fun getTodosByCategory(category: String): List<TodoItem> {
        return todoCache.values.filter { it.category == category }.sortedBy { it.order }
    }
    
    /**
     * 根据会议记录ID获取TODO项
     */
    fun getTodosByMeetingRecord(meetingRecordId: String): List<TodoItem> {
        return todoCache.values.filter { it.meetingRecordId == meetingRecordId }.sortedBy { it.order }
    }
    
    /**
     * 搜索TODO项
     */
    fun searchTodos(query: String): List<TodoItem> {
        if (query.isBlank()) return getAllTodos()
        
        val lowerQuery = query.lowercase()
        return todoCache.values.filter { 
            it.title.lowercase().contains(lowerQuery) || 
            it.description.lowercase().contains(lowerQuery) ||
            it.category.lowercase().contains(lowerQuery)
        }.sortedBy { it.order }
    }
    
    /**
     * 获取过期的TODO项
     */
    fun getOverdueTodos(): List<TodoItem> {
        return todoCache.values.filter { it.isOverdue() }.sortedBy { it.dueDate }
    }
    
    /**
     * 获取即将到期的TODO项
     */
    fun getDueSoonTodos(): List<TodoItem> {
        return todoCache.values.filter { it.isDueSoon() }.sortedBy { it.dueDate }
    }
    
    /**
     * 标记TODO项为完成
     */
    fun markTodoAsCompleted(todoId: String): Boolean {
        val todo = todoCache[todoId] ?: return false
        return updateTodo(todo.markAsCompleted())
    }
    
    /**
     * 标记TODO项为未完成
     */
    fun markTodoAsIncomplete(todoId: String): Boolean {
        val todo = todoCache[todoId] ?: return false
        return updateTodo(todo.markAsIncomplete())
    }
    
    /**
     * 更新TODO项的排序
     */
    fun updateTodoOrder(todoItems: List<TodoItem>): Boolean {
        return try {
            todoItems.forEachIndexed { index, todoItem ->
                val updatedItem = todoItem.copy(order = index)
                todoCache[updatedItem.id] = updatedItem
            }
            saveTodosToPrefs()
            Log.d(TAG, "更新TODO排序成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新TODO排序失败", e)
            false
        }
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): TodoStatistics {
        val allTodos = todoCache.values
        return TodoStatistics(
            total = allTodos.size,
            completed = allTodos.count { it.isCompleted },
            incomplete = allTodos.count { !it.isCompleted },
            overdue = allTodos.count { it.isOverdue() },
            dueSoon = allTodos.count { it.isDueSoon() },
            highPriority = allTodos.count { it.priority == TodoItem.Priority.HIGH || it.priority == TodoItem.Priority.URGENT }
        )
    }
    
    /**
     * 清空所有TODO项
     */
    fun clearAllTodos(): Boolean {
        return try {
            todoCache.clear()
            nextOrder = 0
            saveTodosToPrefs()
            Log.d(TAG, "清空所有TODO项成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清空所有TODO项失败", e)
            false
        }
    }
    
    /**
     * TODO统计信息数据类
     */
    data class TodoStatistics(
        val total: Int,
        val completed: Int,
        val incomplete: Int,
        val overdue: Int,
        val dueSoon: Int,
        val highPriority: Int
    ) {
        val completionRate: Float
            get() = if (total > 0) completed.toFloat() / total.toFloat() else 0f
    }
}
