package com.vectora.vocalmind.server

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.lang.reflect.Type
import java.util.concurrent.TimeUnit

/**
 * 服务器API客户端
 * 处理与服务器的HTTP通信
 */
class ServerApiClient private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ServerApiClient"
        private val JSON_MEDIA_TYPE = "application/json; charset=utf-8".toMediaType()
        
        @Volatile
        private var INSTANCE: ServerApiClient? = null
        
        fun getInstance(context: Context): ServerApiClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerApiClient(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val gson = Gson()
    private var okHttpClient: OkHttpClient? = null
    
    /**
     * 获取配置好的OkHttpClient
     */
    private fun getHttpClient(): OkHttpClient {
        if (okHttpClient == null) {
            okHttpClient = OkHttpClient.Builder()
                .connectTimeout(ServerConfigManager.getConnectionTimeout(context), TimeUnit.MILLISECONDS)
                .readTimeout(ServerConfigManager.getReadTimeout(context), TimeUnit.MILLISECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .addInterceptor { chain ->
                    val original = chain.request()
                    val requestBuilder = original.newBuilder()
                        .header("Content-Type", "application/json")
                        .header("Accept", "application/json")
                        .header("User-Agent", "VocalMind-Android/1.0")
                    
                    // 添加认证头（如果已登录）
                    val authHeader = UserAuthManager.getAuthorizationHeader(context)
                    if (authHeader != null) {
                        requestBuilder.header("Authorization", authHeader)
                    }
                    
                    val request = requestBuilder.build()
                    Log.d(TAG, "HTTP请求: ${request.method} ${request.url}")
                    
                    val response = chain.proceed(request)
                    Log.d(TAG, "HTTP响应: ${response.code} ${response.message}")
                    
                    response
                }
                .build()
        }
        return okHttpClient!!
    }
    
    /**
     * 执行GET请求
     */
    suspend fun <T> get(endpoint: String, responseClass: Class<T>): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val request = Request.Builder()
                .url(url)
                .get()
                .build()

            executeRequest(request, responseClass)
        } catch (e: Exception) {
            Log.e(TAG, "GET请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }

    /**
     * 执行GET请求（支持泛型类型）
     */
    suspend fun <T> get(endpoint: String, responseType: Type): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val request = Request.Builder()
                .url(url)
                .get()
                .build()

            executeRequest(request, responseType)
        } catch (e: Exception) {
            Log.e(TAG, "GET请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }
    
    /**
     * 执行POST请求
     */
    suspend fun <T> post(endpoint: String, body: Any?, responseClass: Class<T>): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val jsonBody = if (body != null) gson.toJson(body) else "{}"
            val requestBody = jsonBody.toRequestBody(JSON_MEDIA_TYPE)

            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .build()

            executeRequest(request, responseClass)
        } catch (e: Exception) {
            Log.e(TAG, "POST请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }

    /**
     * 执行POST请求（支持泛型类型）
     */
    suspend fun <T> post(endpoint: String, body: Any?, responseType: Type): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val jsonBody = if (body != null) gson.toJson(body) else "{}"
            val requestBody = jsonBody.toRequestBody(JSON_MEDIA_TYPE)

            val request = Request.Builder()
                .url(url)
                .post(requestBody)
                .build()

            executeRequest(request, responseType)
        } catch (e: Exception) {
            Log.e(TAG, "POST请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }
    
    /**
     * 执行PUT请求
     */
    suspend fun <T> put(endpoint: String, body: Any?, responseClass: Class<T>): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val jsonBody = if (body != null) gson.toJson(body) else "{}"
            val requestBody = jsonBody.toRequestBody(JSON_MEDIA_TYPE)
            
            val request = Request.Builder()
                .url(url)
                .put(requestBody)
                .build()
            
            executeRequest(request, responseClass)
        } catch (e: Exception) {
            Log.e(TAG, "PUT请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }
    
    /**
     * 执行DELETE请求
     */
    suspend fun <T> delete(endpoint: String, responseClass: Class<T>): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val request = Request.Builder()
                .url(url)
                .delete()
                .build()

            executeRequest(request, responseClass)
        } catch (e: Exception) {
            Log.e(TAG, "DELETE请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }

    /**
     * 执行DELETE请求（支持泛型类型）
     */
    suspend fun <T> delete(endpoint: String, responseType: Type): ApiResult<T> = withContext(Dispatchers.IO) {
        try {
            val url = "${ServerConfigManager.getApiBaseUrl(context)}$endpoint"
            val request = Request.Builder()
                .url(url)
                .delete()
                .build()

            executeRequest(request, responseType)
        } catch (e: Exception) {
            Log.e(TAG, "DELETE请求失败: $endpoint", e)
            ApiResult.Error("网络请求失败: ${e.message}")
        }
    }
    
    /**
     * 执行HTTP请求并处理响应
     */
    private fun <T> executeRequest(request: Request, responseClass: Class<T>): ApiResult<T> {
        return try {
            val response = getHttpClient().newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "响应状态码: ${response.code}")
            Log.d(TAG, "响应内容: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val result = gson.fromJson(responseBody, responseClass)
                    ApiResult.Success(result)
                } catch (e: JsonSyntaxException) {
                    Log.e(TAG, "JSON解析失败", e)
                    ApiResult.Error("响应数据格式错误")
                }
            } else {
                // 尝试解析错误响应
                val errorMessage = try {
                    if (responseBody != null) {
                        val errorResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                        errorResponse.error?.message ?: errorResponse.message ?: "请求失败"
                    } else {
                        "服务器响应为空"
                    }
                } catch (e: Exception) {
                    "HTTP ${response.code}: ${response.message}"
                }

                ApiResult.Error(errorMessage)
            }
        } catch (e: IOException) {
            Log.e(TAG, "网络连接失败", e)
            ApiResult.Error("网络连接失败，请检查网络设置")
        } catch (e: Exception) {
            Log.e(TAG, "请求执行失败", e)
            ApiResult.Error("请求失败: ${e.message}")
        }
    }

    /**
     * 执行HTTP请求并处理响应（支持泛型类型）
     */
    private fun <T> executeRequest(request: Request, responseType: Type): ApiResult<T> {
        return try {
            val response = getHttpClient().newCall(request).execute()
            val responseBody = response.body?.string()

            Log.d(TAG, "响应状态码: ${response.code}")
            Log.d(TAG, "响应内容: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val result = gson.fromJson<T>(responseBody, responseType)
                    ApiResult.Success(result)
                } catch (e: JsonSyntaxException) {
                    Log.e(TAG, "JSON解析失败", e)
                    ApiResult.Error("响应数据格式错误")
                }
            } else {
                // 尝试解析错误响应
                val errorMessage = try {
                    if (responseBody != null) {
                        val errorResponse = gson.fromJson(responseBody, ApiResponse::class.java)
                        errorResponse.error?.message ?: errorResponse.message ?: "请求失败"
                    } else {
                        "服务器响应为空"
                    }
                } catch (e: Exception) {
                    "HTTP ${response.code}: ${response.message}"
                }

                ApiResult.Error(errorMessage)
            }
        } catch (e: IOException) {
            Log.e(TAG, "网络连接失败", e)
            ApiResult.Error("网络连接失败，请检查网络设置")
        } catch (e: Exception) {
            Log.e(TAG, "请求执行失败", e)
            ApiResult.Error("请求失败: ${e.message}")
        }
    }
    
    /**
     * 检查服务器连接
     */
    suspend fun checkServerConnection(): ApiResult<HealthResponse> {
        return try {
            val serverUrl = ServerConfigManager.getServerUrl(context)
            val url = "$serverUrl/health"
            Log.d(TAG, "Testing connection to: $url")
            
            val request = Request.Builder()
                .url(url)
                .get()
                .build()
            
            val result = withContext(Dispatchers.IO) {
                executeRequest(request, HealthResponse::class.java)
            }
            Log.d(TAG, "Connection test result: $result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "服务器连接检查失败", e)
            when (e) {
                is java.net.UnknownHostException -> ApiResult.Error("无法解析服务器地址: ${e.message}")
                is java.net.ConnectException -> ApiResult.Error("无法连接到服务器: ${e.message}")
                is java.net.SocketTimeoutException -> ApiResult.Error("连接超时: ${e.message}")
                is javax.net.ssl.SSLException -> ApiResult.Error("SSL连接错误: ${e.message}")
                else -> ApiResult.Error("无法连接到服务器: ${e.message}")
            }
        }
    }
    
    /**
     * 重置HTTP客户端（配置更改时调用）
     */
    fun resetHttpClient() {
        okHttpClient = null
        Log.d(TAG, "HTTP客户端已重置")
    }
}

/**
 * API请求结果封装
 */
sealed class ApiResult<out T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error(val message: String) : ApiResult<Nothing>()
    
    fun isSuccess(): Boolean = this is Success
    fun isError(): Boolean = this is Error
    
    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }
    
    fun getErrorMessage(): String? = when (this) {
        is Success -> null
        is Error -> message
    }
}
