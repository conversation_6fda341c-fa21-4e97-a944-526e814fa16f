<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background_apple"
    android:padding="0dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="20dp"
        android:background="@color/apple_system_background">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="处理中"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/apple_label"
            android:gravity="center" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close_apple"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/apple_gray_5" />

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="30dp"
        android:gravity="center"
        android:background="@color/apple_system_background">

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginBottom="20dp"
            android:indeterminateTint="@color/apple_blue" />

        <!-- 消息文本 -->
        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:textColor="@color/apple_secondary_label"
            android:lineSpacingExtra="4dp"
            android:gravity="center"
            android:text="正在处理，请稍候..." />

    </LinearLayout>

    <!-- 取消按钮区域 -->
    <LinearLayout
        android:id="@+id/cancel_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/apple_gray_5" />

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="取消"
            android:textSize="16sp"
            android:textColor="@color/apple_red"
            android:background="?android:attr/selectableItemBackground"
            style="?android:attr/borderlessButtonStyle" />

    </LinearLayout>

</LinearLayout>