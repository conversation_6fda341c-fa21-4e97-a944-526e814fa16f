package com.vectora.vocalmind

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import io.noties.markwon.Markwon
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
import io.noties.markwon.ext.tables.TablePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin

/**
 * 聊天消息适配器
 */
class ChatMessageAdapter : RecyclerView.Adapter<ChatMessageAdapter.MessageViewHolder>() {

    companion object {
        private const val TAG = "ChatMessageAdapter"
    }

    private val messages = mutableListOf<ChatMessage>()
    private lateinit var markwon: Markwon

    init {
        // 启用稳定ID以优化RecyclerView性能，减少不必要的重新绑定
        setHasStableIds(true)
    }

    /**
     * 初始化Markdown渲染器
     */
    fun initMarkwon(context: android.content.Context) {
        markwon = Markwon.builder(context)
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(TablePlugin.create(context))
            .usePlugin(TaskListPlugin.create(context))
            .build()
    }

    /**
     * 设置TextView的Markdown内容
     */
    fun setMarkdownText(textView: TextView, markdownText: String?) {
        val safeText = markdownText ?: ""
        if (safeText.trim().isEmpty()) {
            textView.text = safeText
            return
        }

        try {
            if (::markwon.isInitialized) {
                markwon.setMarkdown(textView, safeText)
            } else {
                Log.w(TAG, "Markwon未初始化，使用原始文本")
                textView.text = safeText
            }
        } catch (e: Exception) {
            Log.w(TAG, "Markdown渲染失败，使用原始文本: ${e.message}")
            textView.text = safeText
        }
    }

    fun addMessage(message: ChatMessage) {
        messages.add(message)
        notifyItemInserted(messages.size - 1)
    }

    fun addMessages(newMessages: List<ChatMessage>) {
        val startPosition = messages.size
        messages.addAll(newMessages)
        notifyItemRangeInserted(startPosition, newMessages.size)
    }

    fun clearMessages() {
        val size = messages.size
        messages.clear()
        notifyItemRangeRemoved(0, size)
    }

    fun getMessages(): List<ChatMessage> = messages.toList()

    /**
     * 更新指定位置消息的内容（用于流式更新，避免闪烁）
     */
    fun updateMessageContent(position: Int, newContent: String) {
        if (position >= 0 && position < messages.size) {
            messages[position].content = newContent
            // 直接更新TextView，避免使用notifyItemChanged导致闪烁
            // 注意：这个方法需要在主线程中调用
        }
    }

    /**
     * 获取指定位置的ViewHolder（用于直接更新UI）
     */
    fun getViewHolderAt(recyclerView: RecyclerView, position: Int): MessageViewHolder? {
        return recyclerView.findViewHolderForAdapterPosition(position) as? MessageViewHolder
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_chat_message, parent, false)
        return MessageViewHolder(view)
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        holder.bind(messages[position], this)
    }

    override fun getItemCount(): Int = messages.size
    
    override fun getItemId(position: Int): Long {
        // 使用消息ID的哈希值作为稳定ID
        return messages[position].id.hashCode().toLong()
    }

    class MessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val layoutUserMessage: LinearLayout = itemView.findViewById(R.id.layout_user_message)
        private val layoutAiMessage: LinearLayout = itemView.findViewById(R.id.layout_ai_message)
        private val tvUserMessage: TextView = itemView.findViewById(R.id.tv_user_message)
        private val tvAiMessage: TextView = itemView.findViewById(R.id.tv_ai_message)
        private val tvTimestamp: TextView = itemView.findViewById(R.id.tv_timestamp)

        fun bind(message: ChatMessage, adapter: ChatMessageAdapter) {
            if (message.isFromUser) {
                // 显示用户消息
                layoutUserMessage.visibility = View.VISIBLE
                layoutAiMessage.visibility = View.GONE
                tvUserMessage.text = message.content
            } else {
                // 显示AI消息
                layoutUserMessage.visibility = View.GONE
                layoutAiMessage.visibility = View.VISIBLE
                adapter.setMarkdownText(tvAiMessage, message.content)
            }

            // 显示时间戳（可选）
            if (adapterPosition == 0 || shouldShowTimestamp(message, adapterPosition)) {
                tvTimestamp.visibility = View.VISIBLE
                tvTimestamp.text = message.getFormattedTimestamp()
            } else {
                tvTimestamp.visibility = View.GONE
            }
        }

        /**
         * 直接更新AI消息内容（用于流式更新，避免闪烁）
         */
        fun updateAiMessageContent(content: String, adapter: ChatMessageAdapter) {
            adapter.setMarkdownText(tvAiMessage, content)
        }

        private fun shouldShowTimestamp(message: ChatMessage, position: Int): Boolean {
            // 如果是第一条消息，显示时间戳
            if (position == 0) return true
            
            // 如果与前一条消息间隔超过5分钟，显示时间戳
            val adapter = itemView.parent as? RecyclerView
            val previousMessage = (adapter?.adapter as? ChatMessageAdapter)?.messages?.getOrNull(position - 1)
            return if (previousMessage != null) {
                message.timestamp - previousMessage.timestamp > 5 * 60 * 1000 // 5分钟
            } else {
                true
            }
        }
    }
}