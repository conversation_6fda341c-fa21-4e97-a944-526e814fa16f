package com.vectora.vocalmind

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * AI驱动的TODO生成器
 * 使用LLM分析会议内容并生成相关的待办事项
 */
class TodoGenerator(private val context: Context) {
    
    companion object {
        private const val TAG = "TodoGenerator"
    }
    
    /**
     * 从会议内容生成TODO项
     * 优先使用ASR优化内容，如果没有则使用原始转录内容
     */
    suspend fun generateTodosFromMeeting(
        originalContent: String,
        optimizedContent: String,
        meetingRecordId: String,
        meetingTitle: String = ""
    ): List<TodoItem> {
        return withContext(Dispatchers.IO) {
            try {
                // 使用LLMManager的generateTodos方法，该方法会自动处理服务器模式和隐私模式的切换
                val result = LLMManager.generateTodos(context, originalContent, optimizedContent, meetingTitle)

                if (result.success) {
                    parseTodoItemsFromResponse(result.content, meetingRecordId)
                } else {
                    Log.e(TAG, "LLM调用失败: ${result.error}")
                    emptyList()
                }

            } catch (e: Exception) {
                Log.e(TAG, "生成TODO失败", e)
                emptyList()
            }
        }
    }
    

    
    /**
     * 解析LLM返回的JSON格式TODO项
     */
    private fun parseTodoItemsFromResponse(response: String, meetingRecordId: String): List<TodoItem> {
        return try {
            // 清理响应内容，移除可能的markdown格式
            val cleanResponse = response.trim()
                .removePrefix("```json")
                .removePrefix("```")
                .removeSuffix("```")
                .trim()
            
            val jsonArray = JSONArray(cleanResponse)
            val todoItems = mutableListOf<TodoItem>()
            
            for (i in 0 until jsonArray.length()) {
                val jsonObj = jsonArray.getJSONObject(i)
                
                val title = jsonObj.optString("title", "").trim()
                if (title.isEmpty()) continue
                
                val description = jsonObj.optString("description", "").trim()
                val priorityStr = jsonObj.optString("priority", "中").trim()
                val category = jsonObj.optString("category", "工作").trim()
                val dueDateStr = jsonObj.optString("dueDate", "").trim()
                
                // 解析优先级
                val priority = parsePriority(priorityStr)
                
                // 解析截止时间
                val dueDate = parseDueDate(dueDateStr)
                
                // 验证分类
                val validCategory = validateCategory(category)
                
                val todoItem = TodoItem(
                    title = title,
                    description = description,
                    priority = priority,
                    category = validCategory,
                    dueDate = dueDate,
                    meetingRecordId = meetingRecordId
                )
                
                todoItems.add(todoItem)
                Log.d(TAG, "解析TODO项: $title (优先级: ${priority.displayName}, 分类: $validCategory)")
            }
            
            Log.i(TAG, "成功解析 ${todoItems.size} 个TODO项")
            todoItems
            
        } catch (e: Exception) {
            Log.e(TAG, "解析LLM响应失败", e)
            Log.e(TAG, "原始响应: $response")
            
            // 尝试从文本中提取TODO项（备用方案）
            extractTodosFromText(response, meetingRecordId)
        }
    }
    
    /**
     * 解析优先级
     */
    private fun parsePriority(priorityStr: String): TodoItem.Priority {
        return when (priorityStr.lowercase()) {
            "紧急", "urgent", "critical" -> TodoItem.Priority.URGENT
            "高", "high", "important" -> TodoItem.Priority.HIGH
            "中", "medium", "normal" -> TodoItem.Priority.MEDIUM
            "低", "low" -> TodoItem.Priority.LOW
            else -> TodoItem.Priority.MEDIUM
        }
    }
    
    /**
     * 解析截止时间
     */
    private fun parseDueDate(dueDateStr: String): Long? {
        if (dueDateStr.isEmpty()) return null

        val formats = arrayOf(
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd",
            "MM-dd HH:mm",
            "MM-dd"
        )

        for (format in formats) {
            try {
                val formatter = SimpleDateFormat(format, Locale.getDefault())
                val date = formatter.parse(dueDateStr)
                if (date != null) {
                    val calendar = Calendar.getInstance()
                    calendar.time = date

                    // 如果只有月日，补充当前年份
                    if (format.startsWith("MM-dd")) {
                        calendar.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR))

                        // 如果日期已经过了，设置为明年
                        if (calendar.timeInMillis < System.currentTimeMillis()) {
                            calendar.add(Calendar.YEAR, 1)
                        }
                    }

                    // 确保日期不会是过去的时间（除非明确指定了年份）
                    if (!format.startsWith("yyyy") && calendar.timeInMillis < System.currentTimeMillis()) {
                        // 对于没有年份的日期，如果是过去的时间，可能是指明年
                        calendar.add(Calendar.YEAR, 1)
                    }

                    return calendar.timeInMillis
                }
            } catch (e: Exception) {
                // 继续尝试下一个格式
            }
        }

        return null
    }
    
    /**
     * 验证分类
     */
    private fun validateCategory(category: String): String {
        val validCategories = setOf(
            "工作", "会议", "项目", "沟通", "文档", "审核", "其他",
            "work", "meeting", "project", "communication", "document", "review", "other"
        )
        
        return if (validCategories.contains(category.lowercase())) {
            // 转换为中文分类
            when (category.lowercase()) {
                "work" -> "工作"
                "meeting" -> "会议"
                "project" -> "项目"
                "communication" -> "沟通"
                "document" -> "文档"
                "review" -> "审核"
                "other" -> "其他"
                else -> category
            }
        } else {
            "工作" // 默认分类
        }
    }
    
    /**
     * 从文本中提取TODO项（备用方案）
     */
    private fun extractTodosFromText(text: String, meetingRecordId: String): List<TodoItem> {
        val todoItems = mutableListOf<TodoItem>()
        
        try {
            // 查找可能的TODO项关键词
            val todoKeywords = listOf(
                "需要", "要求", "安排", "准备", "完成", "联系", "确认", "跟进", 
                "处理", "解决", "制定", "编写", "整理", "审核", "检查"
            )
            
            val lines = text.split("\n")
            for (line in lines) {
                val trimmedLine = line.trim()
                if (trimmedLine.length > 10 && todoKeywords.any { trimmedLine.contains(it) }) {
                    val todoItem = TodoItem(
                        title = trimmedLine.take(50), // 限制标题长度
                        description = trimmedLine,
                        priority = TodoItem.Priority.MEDIUM,
                        category = "工作",
                        meetingRecordId = meetingRecordId
                    )
                    todoItems.add(todoItem)
                    
                    if (todoItems.size >= 5) break // 最多提取5个
                }
            }
            
            Log.i(TAG, "备用方案提取了 ${todoItems.size} 个TODO项")
            
        } catch (e: Exception) {
            Log.e(TAG, "备用方案提取失败", e)
        }
        
        return todoItems
    }

}
