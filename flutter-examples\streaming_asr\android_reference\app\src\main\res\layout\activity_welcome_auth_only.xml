<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/welcome_background"
    tools:context=".WelcomeActivity">

    <!-- 顶部Logo区域 -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingTop="80dp"
        android:paddingBottom="60dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- App Logo -->
        <ImageView
            android:id="@+id/iv_app_logo"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/ic_app_logo"
            android:layout_marginBottom="24dp"
            android:contentDescription="App Logo" />

        <!-- App Name -->
        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="VocalMind"
            android:textSize="32sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text"
            android:layout_marginBottom="12dp" />

        <!-- Welcome Message -->
        <TextView
            android:id="@+id/tv_welcome_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="欢迎回来！"
            android:textSize="18sp"
            android:textColor="@color/secondary_text"
            android:layout_marginBottom="8dp" />

        <!-- Server Mode Info -->
        <TextView
            android:id="@+id/tv_server_mode_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前为服务器模式，需要登录以继续使用"
            android:textSize="14sp"
            android:textColor="@color/secondary_text"
            android:gravity="center"
            android:paddingHorizontal="32dp"
            android:alpha="0.8" />

    </LinearLayout>

    <!-- 认证按钮区域 -->
    <LinearLayout
        android:id="@+id/layout_auth_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="32dp"
        app:layout_constraintTop_toBottomOf="@id/layout_header"
        app:layout_constraintBottom_toTopOf="@id/layout_mode_switch"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 登录按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="登录"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:backgroundTint="@color/primary_color"
            app:cornerRadius="28dp"
            app:elevation="4dp"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.Button" />

        <!-- 注册按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_register"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="注册"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:backgroundTint="@color/white"
            app:cornerRadius="28dp"
            app:strokeColor="@color/primary_color"
            app:strokeWidth="2dp"
            android:layout_marginBottom="24dp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

    </LinearLayout>

    <!-- 模式切换区域 -->
    <LinearLayout
        android:id="@+id/layout_mode_switch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingHorizontal="32dp"
        android:paddingBottom="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 分割线 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"
                android:background="@color/divider_color_new" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="或"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:paddingHorizontal="16dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1"
                android:background="@color/divider_color_new" />

        </LinearLayout>

        <!-- 切换到隐私模式按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_switch_to_privacy"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="切换到隐私模式"
            android:textSize="14sp"
            android:textColor="@color/secondary_text"
            android:backgroundTint="@color/transparent"
            app:cornerRadius="24dp"
            app:strokeColor="@color/divider_color_new"
            app:strokeWidth="1dp"
            android:layout_marginBottom="12dp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <!-- 隐私模式说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="隐私模式：数据不经过服务器，直接调用第三方API"
            android:textSize="12sp"
            android:textColor="@color/secondary_text"
            android:gravity="center"
            android:alpha="0.7" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
