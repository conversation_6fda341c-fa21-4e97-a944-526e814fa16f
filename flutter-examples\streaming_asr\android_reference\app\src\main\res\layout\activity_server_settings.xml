<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="服务器设置"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 服务器模式开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="使用服务器模式"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_server_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 服务器配置区域 -->
        <LinearLayout
            android:id="@+id/layout_server_config"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 服务器URL -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="服务器地址"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/edit_server_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="http://127.0.0.1:8000"
                android:inputType="textUri"
                android:layout_marginBottom="8dp" />

            <!-- 连接测试 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <Button
                    android:id="@+id/btn_test_connection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="测试连接"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:id="@+id/text_connection_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="未测试"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 用户认证区域 -->
        <LinearLayout
            android:id="@+id/layout_user_auth"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#CCCCCC"
                android:layout_marginVertical="16dp" />

            <!-- 用户认证标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="用户认证"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- 当前用户信息 -->
            <TextView
                android:id="@+id/text_user_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="未登录"
                android:textSize="14sp"
                android:background="#E8F5E8"
                android:padding="12dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone" />

            <!-- 邮箱 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="邮箱"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/edit_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入邮箱"
                android:inputType="textEmailAddress"
                android:layout_marginBottom="12dp" />

            <!-- 密码 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="密码"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/edit_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入密码"
                android:inputType="textPassword"
                android:layout_marginBottom="12dp" />

            <!-- 姓名（注册时需要） -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="姓名（注册时需要）"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/edit_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入姓名"
                android:inputType="textPersonName"
                android:layout_marginBottom="16dp" />

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btn_login"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="登录"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_register"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="注册"
                    android:layout_marginStart="8dp" />

                <Button
                    android:id="@+id/btn_logout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="登出"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- 说明文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="说明：\n• 隐私模式：直接调用第三方LLM API，数据不经过服务器\n• 服务器模式：通过自建服务器调用LLM API，便于统一管理\n• 服务器模式可以提供统一的API管理和使用量统计"
            android:textSize="12sp"
            android:textColor="#666666"
            android:background="#F5F5F5"
            android:padding="12dp"
            android:layout_marginTop="24dp" />

    </LinearLayout>

</ScrollView>
