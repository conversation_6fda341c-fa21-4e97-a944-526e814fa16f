package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Patterns
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.vectora.vocalmind.server.ServerApiService
import com.vectora.vocalmind.server.UserAuthManager
import kotlinx.coroutines.launch

/**
 * 登录页面 - SaaS标准登录界面
 * 提供简洁专业的用户登录体验
 */
class LoginActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "LoginActivity"
    }

    // UI组件
    private lateinit var tilEmail: TextInputLayout
    private lateinit var etEmail: TextInputEditText
    private lateinit var tilPassword: TextInputLayout
    private lateinit var etPassword: TextInputEditText
    private lateinit var btnLogin: MaterialButton
    private lateinit var tvForgotPassword: TextView
    private lateinit var tvRegister: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvError: TextView

    private lateinit var apiService: ServerApiService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        initViews()
        initServices()
        setupEventListeners()
        setupFormValidation()
    }

    private fun initViews() {
        tilEmail = findViewById(R.id.til_email)
        etEmail = findViewById(R.id.et_email)
        tilPassword = findViewById(R.id.til_password)
        etPassword = findViewById(R.id.et_password)
        btnLogin = findViewById(R.id.btn_login)
        tvForgotPassword = findViewById(R.id.tv_forgot_password)
        tvRegister = findViewById(R.id.tv_register)
        progressBar = findViewById(R.id.progress_bar)
        tvError = findViewById(R.id.tv_error)
    }

    private fun initServices() {
        apiService = ServerApiService.getInstance(this)
    }

    private fun setupEventListeners() {
        btnLogin.setOnClickListener { performLogin() }
        
        tvForgotPassword.setOnClickListener {
            startActivity(Intent(this, ForgotPasswordActivity::class.java))
        }
        
        tvRegister.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
            finish()
        }

        // 返回按钮
        findViewById<ImageView>(R.id.iv_back).setOnClickListener {
            onBackPressed()
        }
    }

    private fun setupFormValidation() {
        val textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                validateForm()
                clearError()
            }
        }

        etEmail.addTextChangedListener(textWatcher)
        etPassword.addTextChangedListener(textWatcher)
    }

    private fun validateForm(): Boolean {
        val email = etEmail.text.toString().trim()
        val password = etPassword.text.toString().trim()

        var isValid = true

        // 验证邮箱
        if (email.isEmpty()) {
            tilEmail.error = "请输入邮箱"
            isValid = false
        } else if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            tilEmail.error = "请输入有效的邮箱地址"
            isValid = false
        } else {
            tilEmail.error = null
        }

        // 验证密码
        if (password.isEmpty()) {
            tilPassword.error = "请输入密码"
            isValid = false
        } else if (password.length < 6) {
            tilPassword.error = "密码至少6位"
            isValid = false
        } else {
            tilPassword.error = null
        }

        btnLogin.isEnabled = isValid
        return isValid
    }

    private fun clearError() {
        tvError.visibility = View.GONE
    }

    private fun showError(message: String) {
        tvError.text = message
        tvError.visibility = View.VISIBLE
    }

    private fun performLogin() {
        if (!validateForm()) return

        val email = etEmail.text.toString().trim()
        val password = etPassword.text.toString().trim()

        setLoading(true)
        clearError()

        lifecycleScope.launch {
            try {
                val result = apiService.login(email, password)

                runOnUiThread {
                    setLoading(false)
                    
                    if (result.isSuccess()) {
                        // 登录成功，跳转到主页面
                        showToast("登录成功")
                        navigateToMain()
                    } else {
                        showError(result.getErrorMessage() ?: "登录失败")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    setLoading(false)
                    showError("网络错误，请检查网络连接")
                }
            }
        }
    }

    private fun setLoading(loading: Boolean) {
        progressBar.visibility = if (loading) View.VISIBLE else View.GONE
        btnLogin.isEnabled = !loading
        etEmail.isEnabled = !loading
        etPassword.isEnabled = !loading
    }

    private fun navigateToMain() {
        val intent = Intent(this, SingleModelActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onBackPressed() {
        startActivity(Intent(this, WelcomeActivity::class.java))
        finish()
    }
}
