<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@color/apple_system_background"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_meeting_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="会议标题"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_meeting_datetime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12/25 14:30"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginStart="12dp" />

        </LinearLayout>

        <!-- Content Preview -->
        <TextView
            android:id="@+id/tv_meeting_preview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="会议内容预览..."
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
            android:textColor="@color/apple_secondary_label"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:layout_marginBottom="16dp" />

        <!-- Tags Row -->
        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/flexbox_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:flexWrap="nowrap"
            app:alignItems="center"
            app:justifyContent="flex_start" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
