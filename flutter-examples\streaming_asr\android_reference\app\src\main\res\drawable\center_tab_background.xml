<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外层阴影 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
        <shape android:shape="oval">
            <solid android:color="#30000000" />
        </shape>
    </item>
    
    <!-- 中层渐变 -->
    <item android:top="0dp" android:left="0dp" android:right="0dp" android:bottom="4dp">
        <shape android:shape="oval">
            <gradient
                android:startColor="#007AFF"
                android:endColor="#0051D5"
                android:angle="135" />
        </shape>
    </item>
    
    <!-- 内层高光 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
        <shape android:shape="oval">
            <gradient
                android:startColor="#40FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="45" />
        </shape>
    </item>
</layer-list>
