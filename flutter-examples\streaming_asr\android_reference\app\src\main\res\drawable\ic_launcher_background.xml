<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    <!-- VocalMind AI Background with gradient -->
    <path
        android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="108"
                android:endY="108"
                android:type="linear">
                <item
                    android:color="#E8F4FD"
                    android:offset="0.0" />
                <item
                    android:color="#FFFFFF"
                    android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>

    <!-- Subtle pattern elements -->
    <path
        android:fillColor="#F0F8FF"
        android:pathData="M20,20L88,20L88,88L20,88Z"
        android:fillAlpha="0.3" />

    <!-- Corner accent elements -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M0,0L15,0L0,15Z"
        android:fillAlpha="0.1" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M93,0L108,0L108,15Z"
        android:fillAlpha="0.1" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M0,93L0,108L15,108Z"
        android:fillAlpha="0.1" />
    <path
        android:fillColor="#4A90E2"
        android:pathData="M93,108L108,108L108,93Z"
        android:fillAlpha="0.1" />
</vector>
