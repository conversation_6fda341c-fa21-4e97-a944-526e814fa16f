<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/login_background"
    android:fillViewport="true"
    tools:context=".LoginActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="600dp">

        <!-- 顶部导航栏 -->
        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="登录"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text"
                android:gravity="center" />

            <View
                android:layout_width="24dp"
                android:layout_height="24dp" />

        </LinearLayout>

        <!-- 主要内容区域 -->
        <LinearLayout
            android:id="@+id/layout_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:padding="32dp"
            app:layout_constraintTop_toBottomOf="@id/layout_header"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Logo和标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="48dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_app_logo"
                    android:layout_marginBottom="16dp"
                    android:contentDescription="App Logo" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="欢迎回来"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="登录您的账户继续使用"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

            </LinearLayout>

            <!-- 登录表单 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <!-- 邮箱输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="邮箱地址"
                        android:inputType="textEmailAddress"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:boxStrokeColor="@color/primary_color"
                    app:hintTextColor="@color/primary_color"
                    app:passwordToggleEnabled="true"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="密码"
                        android:inputType="textPassword"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 忘记密码 -->
                <TextView
                    android:id="@+id/tv_forgot_password"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:text="忘记密码？"
                    android:textSize="14sp"
                    android:textColor="@color/primary_color"
                    android:padding="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true" />

            </LinearLayout>

            <!-- 错误提示 -->
            <TextView
                android:id="@+id/tv_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="错误信息"
                android:textSize="14sp"
                android:textColor="@color/error_color"
                android:background="@drawable/error_background"
                android:padding="12dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                android:drawableStart="@drawable/ic_error"
                android:drawablePadding="8dp"
                android:gravity="center_vertical" />

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="登录"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:backgroundTint="@color/primary_color"
                app:cornerRadius="28dp"
                android:layout_marginBottom="24dp"
                android:enabled="false"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginBottom="24dp"
                android:visibility="gone"
                android:indeterminateTint="@color/primary_color" />

            <!-- 分割线 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="24dp">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1"
                    android:background="@color/divider_color_new" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="或"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text"
                    android:paddingHorizontal="16dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1"
                    android:background="@color/divider_color_new" />

            </LinearLayout>

            <!-- 注册链接 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="还没有账户？"
                    android:textSize="14sp"
                    android:textColor="@color/secondary_text" />

                <TextView
                    android:id="@+id/tv_register"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="立即注册"
                    android:textSize="14sp"
                    android:textColor="@color/primary_color"
                    android:textStyle="bold"
                    android:padding="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
