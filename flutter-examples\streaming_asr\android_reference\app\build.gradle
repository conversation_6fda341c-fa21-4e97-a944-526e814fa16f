plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.vectora.vocalmind'
    compileSdk 34

    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        applicationId "com.vectora.vocalmind"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 从 gradle.properties 读取 API 密钥
        def geminiApiKey = project.findProperty("GEMINI_API_KEY") ?: ""
        buildConfigField "String", "GEMINI_API_KEY", "\"${geminiApiKey}\""
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    // 协程支持
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'

    // Lifecycle支持 (用于lifecycleScope)
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'

    // JSON序列化支持 (用于会议记录数据持久化)
    implementation 'com.google.code.gson:gson:2.10.1'

    // RecyclerView支持 (用于会议记录列表)
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // CardView支持 (用于会议记录卡片布局)
    implementation 'androidx.cardview:cardview:1.0.0'

    // ViewPager2支持 (用于Tab切换)
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // Fragment支持
    implementation 'androidx.fragment:fragment-ktx:1.5.4'

    // HTTP客户端支持 (用于API调用)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // 安全存储支持 (用于加密存储敏感信息)
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // Markdown渲染支持 (用于显示格式化的会议内容)
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:ext-strikethrough:4.6.2'
    implementation 'io.noties.markwon:ext-tables:4.6.2'
    implementation 'io.noties.markwon:ext-tasklist:4.6.2'

    // neumorphism 风格UI
     implementation 'com.github.fornewid:neumorphism:0.3.2'


    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.11.0'
    testImplementation 'org.mockito:mockito-inline:4.11.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
}