package com.vectora.vocalmind

import android.animation.ValueAnimator
import android.app.Dialog
import android.content.Context
import android.util.DisplayMetrics
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.VelocityTracker
import android.view.View
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.webkit.WebView
import android.widget.TextView
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 增强的全屏弹窗管理器
 * 支持自动拖动、全方向拖动和手指缩放功能
 */
class FullscreenDialogManager(private val context: Context, private val dialog: Dialog) {
    
    companion object {
        private const val MIN_FLING_VELOCITY = 150f
        private const val MAX_FLING_VELOCITY = 8000f
        private const val EDGE_SNAP_THRESHOLD = 100 // 边缘吸附阈值
        private const val ANIMATION_DURATION = 300L
        private const val MIN_SCALE = 0.5f
        private const val MAX_SCALE = 3.0f
    }
    
    private val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private val displayMetrics = DisplayMetrics()
    
    private var velocityTracker: VelocityTracker? = null
    private var gestureDetector: GestureDetector
    private var scaleGestureDetector: ScaleGestureDetector
    
    private var isDragging = false
    private var isScaling = false
    private var lastX = 0f
    private var lastY = 0f
    private var currentScale = 1.0f
    
    private var webView: WebView? = null
    private var dragHandle: View? = null
    private var zoomIndicator: TextView? = null
    private var operationHint: TextView? = null
    
    init {
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        gestureDetector = GestureDetector(context, GestureListener())
        scaleGestureDetector = ScaleGestureDetector(context, ScaleListener())
    }
    
    /**
     * 设置增强的触摸功能
     */
    fun setupEnhancedTouch(webView: WebView, dragHandle: View) {
        this.webView = webView
        this.dragHandle = dragHandle
        this.zoomIndicator = dialog.findViewById(R.id.tvZoomIndicator)
        this.operationHint = dialog.findViewById(R.id.tvOperationHint)
        
        // 为整个弹窗设置触摸监听器，支持全方向拖动
        val rootView = dialog.findViewById<View>(android.R.id.content)
        rootView?.setOnTouchListener { _, event ->
            handleTouch(event)
        }
        
        // 为 WebView 启用缩放功能
        setupWebViewZoom(webView)
        
        // 设置操作提示自动隐藏
        setupHintAutoHide()
    }
    
    /**
     * 处理触摸事件
     */
    private fun handleTouch(event: MotionEvent): Boolean {
        // 处理缩放手势
        scaleGestureDetector.onTouchEvent(event)
        
        // 如果正在缩放，不处理拖动
        if (isScaling) {
            return true
        }
        
        // 处理拖动手势
        gestureDetector.onTouchEvent(event)
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                initVelocityTracker(event)
                isDragging = true
                lastX = event.rawX
                lastY = event.rawY
                return true
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (isDragging && !isScaling) {
                    velocityTracker?.addMovement(event)
                    
                    val deltaX = event.rawX - lastX
                    val deltaY = event.rawY - lastY
                    
                    moveDialog(deltaX, deltaY)
                    
                    lastX = event.rawX
                    lastY = event.rawY
                }
                return true
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    handleFling()
                    isDragging = false
                }
                recycleVelocityTracker()
                return true
            }
        }
        
        return false
    }
    
    /**
     * 移动弹窗
     */
    private fun moveDialog(deltaX: Float, deltaY: Float) {
        val window = dialog.window ?: return
        val params = window.attributes
        
        val newX = params.x + deltaX.toInt()
        val newY = params.y + deltaY.toInt()
        
        // 边界检测
        val maxX = displayMetrics.widthPixels - window.decorView.width
        val maxY = displayMetrics.heightPixels - window.decorView.height
        
        params.x = max(-window.decorView.width / 2, min(newX, maxX + window.decorView.width / 2))
        params.y = max(-window.decorView.height / 2, min(newY, maxY + window.decorView.height / 2))
        
        window.attributes = params
    }
    
    /**
     * 处理惯性滑动
     */
    private fun handleFling() {
        velocityTracker?.let { tracker ->
            tracker.computeCurrentVelocity(1000, MAX_FLING_VELOCITY)
            
            val velocityX = tracker.xVelocity
            val velocityY = tracker.yVelocity
            
            // 只有当速度足够大时才执行惯性滑动
            if (abs(velocityX) > MIN_FLING_VELOCITY || abs(velocityY) > MIN_FLING_VELOCITY) {
                startFlingAnimation(velocityX, velocityY)
            } else {
                // 检查是否需要边缘吸附
                checkEdgeSnap()
            }
        }
    }
    
    /**
     * 开始惯性滑动动画
     */
    private fun startFlingAnimation(velocityX: Float, velocityY: Float) {
        val window = dialog.window ?: return
        val params = window.attributes
        
        val startX = params.x.toFloat()
        val startY = params.y.toFloat()
        
        // 计算最终位置
        val friction = 0.95f
        val endX = startX + velocityX * friction / 3
        val endY = startY + velocityY * friction / 3
        
        // 边界限制
        val maxX = displayMetrics.widthPixels - window.decorView.width
        val maxY = displayMetrics.heightPixels - window.decorView.height
        
        val finalX = max(-window.decorView.width / 2f, min(endX, maxX + window.decorView.width / 2f))
        val finalY = max(-window.decorView.height / 2f, min(endY, maxY + window.decorView.height / 2f))
        
        // 创建动画
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.duration = ANIMATION_DURATION
        animator.interpolator = DecelerateInterpolator()
        
        animator.addUpdateListener { animation ->
            val fraction = animation.animatedValue as Float
            
            val currentX = startX + (finalX - startX) * fraction
            val currentY = startY + (finalY - startY) * fraction
            
            params.x = currentX.toInt()
            params.y = currentY.toInt()
            window.attributes = params
        }
        
        animator.start()
    }
    
    /**
     * 检查边缘吸附
     */
    private fun checkEdgeSnap() {
        val window = dialog.window ?: return
        val params = window.attributes
        
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        
        var targetX = params.x
        var targetY = params.y
        var needsAnimation = false
        
        // 左右边缘吸附
        if (params.x < EDGE_SNAP_THRESHOLD) {
            targetX = 0
            needsAnimation = true
        } else if (params.x > screenWidth - window.decorView.width - EDGE_SNAP_THRESHOLD) {
            targetX = screenWidth - window.decorView.width
            needsAnimation = true
        }
        
        // 上下边缘吸附
        if (params.y < EDGE_SNAP_THRESHOLD) {
            targetY = 0
            needsAnimation = true
        } else if (params.y > screenHeight - window.decorView.height - EDGE_SNAP_THRESHOLD) {
            targetY = screenHeight - window.decorView.height
            needsAnimation = true
        }
        
        if (needsAnimation) {
            animateToPosition(targetX, targetY)
        }
    }
    
    /**
     * 动画移动到指定位置
     */
    private fun animateToPosition(targetX: Int, targetY: Int) {
        val window = dialog.window ?: return
        val params = window.attributes
        
        val startX = params.x
        val startY = params.y
        
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.duration = ANIMATION_DURATION
        animator.interpolator = DecelerateInterpolator()
        
        animator.addUpdateListener { animation ->
            val fraction = animation.animatedValue as Float
            
            params.x = (startX + (targetX - startX) * fraction).toInt()
            params.y = (startY + (targetY - startY) * fraction).toInt()
            window.attributes = params
        }
        
        animator.start()
    }
    
    /**
     * 设置 WebView 缩放功能
     */
    private fun setupWebViewZoom(webView: WebView) {
        webView.settings.apply {
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false // 隐藏缩放控件
            useWideViewPort = true
            loadWithOverviewMode = true
        }
        
        // 设置初始缩放
        webView.setInitialScale((currentScale * 100).toInt())
    }
    
    /**
     * 初始化速度追踪器
     */
    private fun initVelocityTracker(event: MotionEvent) {
        velocityTracker?.recycle()
        velocityTracker = VelocityTracker.obtain()
        velocityTracker?.addMovement(event)
    }
    
    /**
     * 回收速度追踪器
     */
    private fun recycleVelocityTracker() {
        velocityTracker?.recycle()
        velocityTracker = null
    }
    
    /**
     * 手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        
        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击重置缩放
            resetZoom()
            return true
        }
        
        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            // 惯性滑动在 handleFling 中处理
            return true
        }
    }
    
    /**
     * 缩放手势监听器
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            return true
        }
        
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaleFactor = detector.scaleFactor
            val newScale = currentScale * scaleFactor
            
            // 限制缩放范围
            currentScale = max(MIN_SCALE, min(newScale, MAX_SCALE))
            
            // 应用缩放到 WebView
            webView?.setInitialScale((currentScale * 100).toInt())
            webView?.reload()
            
            // 显示缩放指示器
            showZoomIndicator()
            
            return true
        }
        
        override fun onScaleEnd(detector: ScaleGestureDetector) {
            isScaling = false
        }
    }
    
    /**
     * 重置缩放
     */
    private fun resetZoom() {
        currentScale = 1.0f
        webView?.setInitialScale(100)
        webView?.reload()
        showZoomIndicator()
    }
    
    /**
     * 显示缩放指示器
     */
    private fun showZoomIndicator() {
        zoomIndicator?.let { indicator ->
            indicator.text = "${(currentScale * 100).toInt()}%"
            indicator.visibility = View.VISIBLE
            indicator.alpha = 1.0f
            
            // 2秒后自动隐藏
            indicator.animate()
                .alpha(0f)
                .setDuration(2000)
                .withEndAction {
                    indicator.visibility = View.GONE
                }
                .start()
        }
    }
    
    /**
     * 设置操作提示自动隐藏
     */
    private fun setupHintAutoHide() {
        operationHint?.let { hint ->
            // 3秒后自动隐藏操作提示
            hint.animate()
                .alpha(0f)
                .setDuration(3000)
                .withEndAction {
                    hint.visibility = View.GONE
                }
                .start()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        recycleVelocityTracker()
    }
}