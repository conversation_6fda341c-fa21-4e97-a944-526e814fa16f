# VocalMind 项目功能模块与流程分析

## 项目概述

VocalMind 是一个基于 Android 平台的智能语音识别与会议记录应用，集成了先进的 ASR（自动语音识别）技术和 LLM（大语言模型）能力，为用户提供高效的语音转文字、智能优化、会议总结等功能。

## 主要功能模块

### 1. 语音识别模块 (ASR Engine)

**核心组件：**
- `SingleModelASREngine.kt` - 单模型ASR引擎，基于FunASR架构
- `OnlineRecognizer.kt` - 在线识别器
- `OnlineStream.kt` - 音频流处理
- `AudioRecordingManager.kt` - 录音文件管理
- `AudioRecordingService.kt` - 录音后台服务

**主要功能：**
- 实时语音识别
- 说话人识别与分离
- 音频质量控制
- 端点检测
- WAV格式录音文件保存

### 2. 会议记录管理模块

**核心组件：**
- `MeetingRecord.kt` - 会议记录数据模型
- `MeetingRecordManager.kt` - 会议记录管理器
- `MeetingRecordsActivity.kt` - 会议记录列表页面
- `MeetingDetailActivity.kt` - 会议详情页面
- `MeetingRecordsAdapter.kt` - 列表适配器

**主要功能：**
- 会议记录的增删改查
- 本地数据持久化存储
- 搜索与过滤功能
- 音频文件关联管理

### 3. LLM集成模块

**核心组件：**
- `LLMManager.kt` - 统一LLM管理器
- `GeminiConfig.kt` - Gemini API配置
- `DeepSeekConfig.kt` - DeepSeek API配置
- `LLMApiKeyManager.kt` - API密钥管理
- `StreamingLLMManager.kt` - 流式LLM处理

**主要功能：**
- 多LLM提供商支持（Gemini、DeepSeek）
- ASR内容智能优化
- 会议内容智能总结
- Mermaid流程图生成
- 流式响应处理

### 4. AI聊天模块

**核心组件：**
- `AiChatActivity.kt` - AI聊天界面
- `ChatManager.kt` - 聊天记录管理
- `ChatMessage.kt` - 聊天消息模型
- `ChatMessageAdapter.kt` - 聊天消息适配器

**主要功能：**
- 基于会议内容的智能问答
- 聊天历史记录保存
- 上下文感知对话
- 实时消息流处理

### 5. 悬浮窗模块

**核心组件：**
- `FloatingWindowService.kt` - 悬浮窗服务
- `FloatingWindowView.kt` - 悬浮窗视图
- `FloatingWindowPermissionManager.kt` - 权限管理
- `FloatingWindowSettings.kt` - 悬浮窗设置

**主要功能：**
- 系统级悬浮窗显示
- 录音状态实时同步
- 快捷操作支持
- 权限动态管理

### 6. 设置与配置模块

**核心组件：**
- `SettingsActivity.kt` - 设置页面
- `FeatureConfig.kt` - 功能配置
- `ApiKeyManager.kt` - API密钥管理
- `HomophoneReplacerConfig.kt` - 同音字替换配置

**主要功能：**
- LLM提供商配置
- API密钥管理
- 功能开关控制
- 用户偏好设置

## 应用执行流程

### 主要业务流程图

```mermaid
flowchart TD
    A[应用启动] --> B[SingleModelActivity]
    B --> C{权限检查}
    C -->|权限不足| D[请求录音权限]
    C -->|权限充足| E[初始化ASR引擎]
    D --> E
    E --> F[显示主界面]
    
    F --> G{用户操作}
    G -->|点击录音| H[开始录音]
    G -->|查看记录| I[MeetingRecordsActivity]
    G -->|打开设置| J[SettingsActivity]
    G -->|启用悬浮窗| K[FloatingWindowService]
    
    H --> L[AudioRecordingService]
    L --> M[实时ASR识别]
    M --> N[显示识别结果]
    N --> O{录音结束}
    O -->|是| P[保存会议记录]
    O -->|否| M
    
    P --> Q[LLM内容优化]
    Q --> R[生成会议总结]
    R --> S[保存到本地存储]
    
    I --> T[显示会议列表]
    T --> U{用户选择}
    U -->|查看详情| V[MeetingDetailActivity]
    U -->|AI聊天| W[AiChatActivity]
    U -->|删除记录| X[删除确认]
    
    V --> Y[显示会议详情]
    Y --> Z[支持编辑和导出]
    
    W --> AA[ChatManager]
    AA --> BB[LLM问答处理]
    BB --> CC[显示聊天结果]
```

### ASR识别流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 主界面
    participant Service as AudioRecordingService
    participant ASR as SingleModelASREngine
    participant Manager as MeetingRecordManager
    
    User->>UI: 点击录音按钮
    UI->>Service: 启动录音服务
    Service->>ASR: 初始化识别引擎
    ASR->>Service: 引擎就绪
    
    loop 录音过程
        Service->>ASR: 发送音频数据
        ASR->>ASR: 实时识别处理
        ASR->>UI: 返回识别结果
        UI->>User: 显示实时文字
    end
    
    User->>UI: 停止录音
    UI->>Service: 停止录音服务
    Service->>ASR: 获取最终结果
    ASR->>Manager: 保存会议记录
    Manager->>UI: 保存完成
    UI->>User: 显示保存结果
```

### LLM处理流程图

```mermaid
flowchart TD
    A[ASR识别完成] --> B[获取原始文本]
    B --> C[LLMManager]
    C --> D{检查API配置}
    D -->|未配置| E[返回错误信息]
    D -->|已配置| F[选择LLM提供商]
    
    F --> G{提供商类型}
    G -->|Gemini| H[GeminiConfig]
    G -->|DeepSeek| I[DeepSeekConfig]
    
    H --> J[构建优化Prompt]
    I --> J
    J --> K[调用LLM API]
    K --> L{API响应}
    L -->|成功| M[解析优化结果]
    L -->|失败| N[错误处理]
    
    M --> O[生成会议总结]
    O --> P[生成Mermaid图表]
    P --> Q[更新会议记录]
    Q --> R[保存到本地]
    
    N --> S[显示错误信息]
    E --> S
```

### 悬浮窗服务流程图

```mermaid
stateDiagram-v2
    [*] --> 权限检查
    权限检查 --> 权限申请 : 无权限
    权限检查 --> 服务启动 : 有权限
    权限申请 --> 服务启动 : 权限获取
    权限申请 --> [*] : 权限拒绝
    
    服务启动 --> 悬浮窗显示
    悬浮窗显示 --> 录音监听
    录音监听 --> 状态同步
    状态同步 --> 录音监听
    
    录音监听 --> 录音开始 : 用户点击
    录音开始 --> 录音中
    录音中 --> 录音结束 : 用户停止
    录音结束 --> 录音监听
    
    悬浮窗显示 --> 服务停止 : 用户关闭
    服务停止 --> [*]
```

### 数据存储架构图

```mermaid
erDiagram
    MeetingRecord {
        string id PK
        string title
        long timestamp
        string originalContent
        string optimizedContent
        string summaryContent
        string mermaidContent
        int wordCount
        long duration
        int speakerCount
        string audioFilePath
    }
    
    ChatMessage {
        string id PK
        string meetingRecordId FK
        string content
        boolean isUser
        long timestamp
        boolean isStreaming
    }
    
    SpeakerData {
        string speakerId PK
        string name
        float[] embedding
        long lastUsed
        int usageCount
    }
    
    AudioFile {
        string filePath PK
        string meetingId FK
        long fileSize
        long duration
        string format
    }
    
    MeetingRecord ||--o{ ChatMessage : "has"
    MeetingRecord ||--|| AudioFile : "contains"
    MeetingRecord }o--o{ SpeakerData : "involves"
```

## 技术特点

### 1. 架构设计
- **单一职责原则**：每个模块职责明确，便于维护
- **依赖注入**：使用单例模式管理核心组件
- **异步处理**：大量使用协程处理耗时操作
- **事件驱动**：基于回调和监听器的事件处理机制

### 2. 性能优化
- **内存管理**：及时释放音频缓冲区和大对象
- **线程优化**：合理使用后台线程处理计算密集任务
- **缓存策略**：智能缓存ASR结果和LLM响应
- **资源复用**：复用网络连接和音频组件

### 3. 用户体验
- **Apple风格UI**：简洁优雅的界面设计
- **实时反馈**：即时显示识别结果和处理状态
- **错误处理**：友好的错误提示和恢复机制
- **离线支持**：本地数据存储，支持离线查看

### 4. 扩展性
- **插件化LLM**：支持多种LLM提供商
- **模块化设计**：便于添加新功能模块
- **配置化管理**：通过配置文件控制功能开关
- **API抽象**：统一的接口设计便于扩展

## 部署与配置

### 环境要求
- Android SDK 21+
- Kotlin 1.8+
- 录音权限
- 网络权限（LLM功能）
- 悬浮窗权限（可选）

### 配置步骤
1. 在 `gradle.properties` 中配置 LLM API 密钥
2. 确保设备支持录音功能
3. 授予必要的系统权限
4. 根据需要配置悬浮窗功能

### 注意事项
- API密钥安全存储，避免泄露
- 音频文件占用存储空间，需定期清理
- 网络请求需要处理超时和重试
- 不同Android版本的权限适配

---

*本文档基于项目源码分析生成，详细的API文档和使用说明请参考各模块的代码注释。*