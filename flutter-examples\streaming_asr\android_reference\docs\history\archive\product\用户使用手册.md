# VocalMind 用户使用手册

## 📱 应用简介

**VocalMind** 是一款智能语音识别与会议记录应用，集成了先进的 ASR（自动语音识别）和 LLM（大语言模型）技术，为您提供高效、准确的语音转文字服务和智能会议管理功能。

### 🌟 核心特性
- **实时语音识别**：高精度语音转文字，支持多种语言
- **说话人识别**：自动识别不同说话人，便于会议记录
- **智能内容优化**：AI 自动优化识别结果，提升可读性
- **会议智能总结**：一键生成会议要点和行动项
- **悬浮窗录音**：支持全局悬浮窗，随时随地录音
- **AI 智能问答**：基于会议内容的智能对话

---

## 🚀 快速开始

### 首次使用

#### 1. 安装应用
- 从应用商店下载安装 VocalMind
- 或者从官方网站下载 APK 文件进行安装

#### 2. 权限设置
应用首次启动时，需要授予以下权限：

- **🎤 麦克风权限**：用于录音和语音识别
- **📁 存储权限**：用于保存会议记录和音频文件
- **🔊 悬浮窗权限**：用于显示全局悬浮窗（可选）
- **📱 通知权限**：用于显示录音状态通知

> **提示**：所有权限都可以在系统设置中随时修改

#### 3. 基础配置
进入应用后，建议先进行以下配置：

1. **语言设置**：选择主要使用的语言
2. **音频质量**：根据需要选择录音质量
3. **LLM 配置**：配置 AI 服务（可选，用于智能优化功能）

---

## 📝 主要功能使用

### 语音识别录音

#### 开始录音
1. 打开应用主界面
2. 点击中央的 **"开始录音"** 按钮
3. 按钮变为红色，开始实时语音识别
4. 说话内容会实时显示在屏幕上

#### 停止录音
1. 再次点击录音按钮（现在显示为 "停止录音"）
2. 系统会自动保存录音结果
3. 可选择是否进行 AI 优化处理

#### 实时功能
- **实时转写**：说话内容立即显示
- **说话人标识**：不同颜色区分不同说话人
- **音量指示**：实时显示当前音量水平
- **时长显示**：显示当前录音时长

### 会议记录管理

#### 查看会议记录
1. 点击底部导航栏的 **"会议记录"** 标签
2. 浏览所有已保存的会议记录
3. 记录按时间倒序排列，最新的在顶部

#### 会议记录详情
点击任意会议记录，可以查看：
- **原始内容**：语音识别的原始结果
- **优化内容**：AI 优化后的文本
- **会议总结**：智能生成的会议要点
- **流程图**：Mermaid 格式的会议流程图
- **基本信息**：录音时长、字数、说话人数量等

#### 编辑会议记录
1. 在会议记录详情页面，点击 **"编辑"** 按钮
2. 可以修改：
   - 会议标题
   - 会议内容
   - 添加标签
   - 设置分类

#### 分享会议记录
1. 在会议记录详情页面，点击 **"分享"** 按钮
2. 选择分享方式：
   - 纯文本分享
   - Markdown 格式分享
   - PDF 导出（需要额外插件）

#### 删除会议记录
1. 长按会议记录项
2. 选择 **"删除"** 选项
3. 确认删除操作

> **注意**：删除操作不可恢复，请谨慎操作

### AI 智能功能

#### 内容优化
1. 在会议记录详情页面，点击 **"AI 优化"** 按钮
2. AI 会自动：
   - 修正语音识别错误
   - 添加标点符号
   - 优化语句结构
   - 统一术语表达

#### 会议总结
1. 点击 **"生成总结"** 按钮
2. 选择总结类型：
   - **简要总结**：提取关键要点
   - **详细总结**：分段详细总结
   - **行动项**：提取待办事项
   - **决策总结**：总结关键决策

#### 流程图生成
1. 点击 **"生成流程图"** 按钮
2. AI 会分析会议内容，生成 Mermaid 格式的流程图
3. 可以复制流程图代码，在支持的工具中查看

### AI 聊天功能

#### 开始聊天
1. 在会议记录详情页面，点击 **"AI 聊天"** 按钮
2. 进入聊天界面，可以询问关于会议内容的问题

#### 聊天技巧
- **具体提问**："会议中提到了哪些关键决策？"
- **总结请求**："请总结张三的发言要点"
- **行动项查询**："有哪些需要跟进的事项？"
- **时间查询**："会议讨论了多长时间的预算问题？"

### 悬浮窗功能

#### 启用悬浮窗
1. 进入 **"设置"** → **"悬浮窗设置"
2. 开启 **"启用悬浮窗"** 开关
3. 授予悬浮窗权限（如果尚未授予）

#### 使用悬浮窗
1. 启用后，屏幕上会显示一个小的悬浮按钮
2. 点击悬浮按钮开始录音
3. 录音过程中，悬浮窗会显示：
   - 录音状态指示
   - 当前录音时长
   - 实时识别文本（可选）

#### 悬浮窗设置
- **位置调整**：拖拽悬浮窗到合适位置
- **透明度**：调整悬浮窗透明度
- **大小**：选择悬浮窗大小
- **显示内容**：选择显示哪些信息

---

## ⚙️ 设置与配置

### 基本设置

#### 语音识别设置
1. 进入 **"设置"** → **"语音识别"
2. 可配置：
   - **识别语言**：选择主要使用的语言
   - **识别精度**：平衡速度和准确性
   - **说话人识别**：开启/关闭说话人识别
   - **端点检测**：自动检测说话开始和结束

#### 录音设置
1. 进入 **"设置"** → **"录音设置"
2. 可配置：
   - **音频质量**：选择录音质量（影响文件大小）
   - **采样率**：16kHz（推荐）或 8kHz
   - **音频格式**：WAV 或 MP3
   - **自动保存**：是否自动保存录音文件

#### 存储设置
1. 进入 **"设置"** → **"存储设置"
2. 可配置：
   - **存储位置**：选择文件保存位置
   - **自动清理**：设置自动清理旧文件的规则
   - **备份设置**：配置数据备份选项

### AI 服务配置

#### LLM 提供商设置
1. 进入 **"设置"** → **"AI 服务"
2. 选择 LLM 提供商：
   - **Google Gemini**：Google 的大语言模型
   - **DeepSeek**：国产优秀的大语言模型
   - **自定义**：配置其他兼容的 API

#### API 密钥配置
1. 选择提供商后，点击 **"配置 API 密钥"
2. 输入从服务提供商获取的 API 密钥
3. 点击 **"测试连接"** 验证配置是否正确

> **安全提示**：API 密钥仅存储在本地设备，不会上传到任何服务器

#### AI 功能开关
可以单独控制各项 AI 功能：
- **内容优化**：是否启用 AI 内容优化
- **自动总结**：是否自动生成会议总结
- **流程图生成**：是否启用流程图生成
- **智能聊天**：是否启用 AI 聊天功能

### 界面设置

#### 主题设置
1. 进入 **"设置"** → **"界面设置"
2. 选择主题：
   - **跟随系统**：根据系统设置自动切换
   - **浅色主题**：始终使用浅色主题
   - **深色主题**：始终使用深色主题

#### 字体设置
- **字体大小**：调整界面字体大小
- **字体样式**：选择字体样式（如果支持）

#### 显示设置
- **显示说话人颜色**：是否用颜色区分说话人
- **显示时间戳**：是否显示详细时间信息
- **实时滚动**：识别文本是否自动滚动

---

## 💡 使用技巧

### 提高识别准确性

#### 环境优化
- **安静环境**：选择相对安静的环境进行录音
- **距离适中**：保持与设备 30-50cm 的距离
- **避免遮挡**：不要遮挡设备的麦克风
- **减少回音**：避免在空旷的房间录音

#### 说话技巧
- **语速适中**：不要说得太快或太慢
- **发音清晰**：尽量发音标准，避免方言
- **适当停顿**：句子之间适当停顿
- **音量稳定**：保持稳定的说话音量

### 会议记录最佳实践

#### 会议前准备
1. **测试设备**：确保麦克风工作正常
2. **检查存储**：确保有足够的存储空间
3. **网络连接**：如果使用 AI 功能，确保网络稳定
4. **权限确认**：确保所有必要权限已授予

#### 会议中操作
1. **及时开始**：会议开始前启动录音
2. **设备位置**：将设备放在会议桌中央
3. **避免干扰**：避免移动设备或产生噪音
4. **分段录音**：长会议可以分段录音，避免单个文件过大

#### 会议后处理
1. **及时保存**：录音结束后及时保存
2. **添加标题**：为会议记录添加有意义的标题
3. **AI 优化**：使用 AI 功能优化内容
4. **分享备份**：及时分享给相关人员并备份

### 数据管理建议

#### 定期整理
- **分类管理**：为会议记录添加分类标签
- **定期清理**：删除不需要的旧记录
- **重要标记**：为重要会议添加标记

#### 备份策略
- **本地备份**：定期导出重要会议记录
- **云端备份**：使用云存储服务备份数据
- **多设备同步**：在多个设备间同步数据

---

## 🔧 故障排除

### 常见问题

#### 录音问题

**问题：无法开始录音**
- 检查麦克风权限是否已授予
- 确认其他应用没有占用麦克风
- 重启应用或设备

**问题：录音质量差**
- 检查麦克风是否被遮挡
- 调整录音质量设置
- 更换录音环境

**问题：录音中断**
- 检查存储空间是否充足
- 确认应用没有被系统杀死
- 检查电池优化设置

#### 识别问题

**问题：识别准确率低**
- 确认选择了正确的语言
- 改善录音环境和说话方式
- 尝试重新训练或更新模型

**问题：识别速度慢**
- 检查设备性能和可用内存
- 降低识别精度设置
- 关闭其他占用资源的应用

**问题：无法识别特定词汇**
- 添加自定义词汇到词典
- 使用更清晰的发音
- 考虑使用专业术语模型

#### AI 功能问题

**问题：AI 功能无法使用**
- 检查网络连接是否正常
- 确认 API 密钥配置正确
- 检查 API 服务是否可用

**问题：AI 响应慢**
- 检查网络速度
- 尝试更换 LLM 提供商
- 减少输入文本长度

**问题：AI 结果不准确**
- 提供更多上下文信息
- 调整 AI 参数设置
- 尝试不同的提示词

#### 悬浮窗问题

**问题：悬浮窗不显示**
- 检查悬浮窗权限是否已授予
- 确认悬浮窗功能已启用
- 重启应用

**问题：悬浮窗位置异常**
- 手动拖拽到合适位置
- 重置悬浮窗设置
- 检查屏幕分辨率设置

### 性能优化

#### 内存优化
- 定期清理缓存数据
- 关闭不必要的功能
- 限制同时处理的文件数量

#### 电池优化
- 在设置中关闭电池优化（针对本应用）
- 使用省电模式时注意功能限制
- 长时间录音时连接充电器

#### 存储优化
- 定期清理旧的录音文件
- 使用压缩格式保存文件
- 将文件移动到外部存储

---

## 🔒 隐私与安全

### 数据保护

#### 本地存储
- 所有会议记录默认存储在本地设备
- 数据使用加密存储，保护隐私安全
- 用户完全控制数据的访问和删除

#### 网络传输
- AI 功能需要网络连接，但仅传输必要的文本内容
- 所有网络传输使用 HTTPS 加密
- 不会上传音频文件到云端

#### API 密钥安全
- API 密钥仅存储在本地设备
- 使用安全的存储机制保护密钥
- 用户可以随时删除或更换密钥

### 权限说明

#### 必需权限
- **麦克风**：用于录音和语音识别
- **存储**：用于保存会议记录和音频文件

#### 可选权限
- **悬浮窗**：用于显示全局悬浮窗
- **通知**：用于显示录音状态通知
- **网络**：用于 AI 功能（可选）

#### 权限管理
- 用户可以在系统设置中随时修改权限
- 应用会根据权限状态调整功能可用性
- 不会强制要求不必要的权限

### 数据导出与删除

#### 数据导出
- 支持导出所有会议记录为标准格式
- 可以选择性导出特定记录
- 导出的数据可以在其他设备上导入

#### 数据删除
- 用户可以随时删除任何会议记录
- 提供批量删除功能
- 卸载应用时会清除所有本地数据

---

## 📞 技术支持

### 获取帮助

#### 应用内帮助
- 点击 **"设置"** → **"帮助与反馈"
- 查看常见问题解答
- 观看功能演示视频

#### 在线支持
- **官方网站**：[网站地址]
- **用户手册**：[在线文档地址]
- **视频教程**：[视频教程地址]

#### 联系我们
- **邮箱支持**：<EMAIL>
- **QQ 群**：[QQ群号]
- **微信群**：[微信群二维码]

### 反馈与建议

#### 问题反馈
1. 详细描述遇到的问题
2. 提供设备型号和系统版本
3. 如果可能，提供错误截图
4. 说明问题的重现步骤

#### 功能建议
- 描述希望添加的功能
- 说明功能的使用场景
- 提供具体的实现建议

#### 反馈渠道
- 应用内反馈功能
- 应用商店评价
- 官方邮箱或社群

---

## 📋 附录

### 快捷键说明

| 操作 | 快捷方式 | 说明 |
|------|----------|------|
| 开始/停止录音 | 音量键长按 | 需要在设置中启用 |
| 暂停录音 | 双击屏幕 | 悬浮窗模式下可用 |
| 快速保存 | 摇晃设备 | 需要在设置中启用 |

### 支持的文件格式

#### 音频格式
- **WAV**：无损音频格式，质量最高
- **MP3**：压缩音频格式，文件较小
- **AAC**：高效压缩格式，平衡质量和大小

#### 导出格式
- **TXT**：纯文本格式
- **Markdown**：支持格式化的文本
- **JSON**：结构化数据格式
- **PDF**：便于分享的文档格式（需要插件）

### 系统要求

#### 最低要求
- **Android 版本**：Android 5.0 (API 21) 及以上
- **内存**：2GB RAM
- **存储空间**：500MB 可用空间
- **处理器**：ARM64 或 x86_64 架构

#### 推荐配置
- **Android 版本**：Android 8.0 (API 26) 及以上
- **内存**：4GB RAM 或更多
- **存储空间**：2GB 可用空间
- **网络**：稳定的 Wi-Fi 或 4G 连接（AI 功能）

### 更新日志

#### 版本 2.0.0
- 新增 AI 聊天功能
- 优化语音识别准确性
- 改进用户界面设计
- 修复已知问题

#### 版本 1.5.0
- 新增悬浮窗功能
- 支持多种 LLM 提供商
- 添加会议流程图生成
- 性能优化和稳定性改进

#### 版本 1.0.0
- 首次发布
- 基础语音识别功能
- 会议记录管理
- AI 内容优化

---

**感谢您使用 VocalMind！**

如果您在使用过程中遇到任何问题或有任何建议，请随时联系我们。我们致力于为您提供最好的语音识别和会议记录体验。

*本手册最后更新时间：2024年12月*