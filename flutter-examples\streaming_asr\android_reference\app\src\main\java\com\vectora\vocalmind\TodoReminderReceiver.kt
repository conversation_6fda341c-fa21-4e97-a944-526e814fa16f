package com.vectora.vocalmind

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.PowerManager
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * TODO提醒广播接收器
 * 接收AlarmManager触发的提醒事件，显示通知并播放响铃/振动
 */
class TodoReminderReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "TodoReminderReceiver"
        const val ACTION_TODO_REMINDER = "com.vectora.vocalmind.TODO_REMINDER"
        const val EXTRA_TODO_ID = "todo_id"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到提醒广播: ${intent.action}")

        when (intent.action) {
            ACTION_TODO_REMINDER -> {
                val todoId = intent.getStringExtra(EXTRA_TODO_ID)
                if (todoId != null) {
                    handleTodoReminder(context, todoId)
                } else {
                    Log.w(TAG, "TODO ID为空，无法处理提醒")
                }
            }
            TodoNotificationHelper.ACTION_STOP_REMINDER -> {
                Log.d(TAG, "收到停止响铃广播")
                TodoNotificationHelper.stopCurrentRingtone()
            }
        }
    }
    
    /**
     * 处理TODO提醒
     */
    private fun handleTodoReminder(context: Context, todoId: String) {
        Log.d(TAG, "处理TODO提醒: $todoId")

        // 获取 WakeLock 确保设备保持唤醒
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        val wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            "TodoReminder:$todoId"
        )

        try {
            // 获取 WakeLock，最多持续 30 秒
            wakeLock.acquire(30000)

            // 使用协程处理异步操作
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val todoManager = TodoManager.getInstance(context)
                    val todoItem = todoManager.getTodoById(todoId)

                    if (todoItem != null) {
                        // 检查TODO是否已完成
                        if (todoItem.isCompleted) {
                            Log.d(TAG, "TODO已完成，跳过提醒: ${todoItem.title}")
                            return@launch
                        }

                        // 检查提醒时间是否有效
                        if (todoItem.reminderTime == null) {
                            Log.w(TAG, "TODO没有设置提醒时间: ${todoItem.title}")
                            return@launch
                        }

                        // 显示提醒通知
                        val notificationHelper = TodoNotificationHelper(context)
                        notificationHelper.showReminderNotification(todoItem)

                        Log.i(TAG, "已显示TODO提醒通知: ${todoItem.title}")

                    } else {
                        Log.w(TAG, "找不到指定的TODO项: $todoId")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "处理TODO提醒失败", e)
                } finally {
                    // 确保释放 WakeLock
                    if (wakeLock.isHeld) {
                        wakeLock.release()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取WakeLock失败", e)
            // 如果获取 WakeLock 失败，仍然尝试显示通知
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val todoManager = TodoManager.getInstance(context)
                    val todoItem = todoManager.getTodoById(todoId)

                    if (todoItem != null && !todoItem.isCompleted && todoItem.reminderTime != null) {
                        val notificationHelper = TodoNotificationHelper(context)
                        notificationHelper.showReminderNotification(todoItem)
                    }
                } catch (ex: Exception) {
                    Log.e(TAG, "备用提醒处理失败", ex)
                }
            }
        }
    }
}
