package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 会议记录管理器
 * 负责会议记录的持久化存储和管理
 * 采用苹果式的简洁设计理念
 */
class MeetingRecordManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "MeetingRecordManager"
        private const val PREFS_NAME = "meeting_records"
        private const val KEY_RECORDS = "records"
        private const val MAX_RECORDS = 100 // 最大保存记录数
        
        @Volatile
        private var INSTANCE: MeetingRecordManager? = null
        
        fun getInstance(context: Context): MeetingRecordManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MeetingRecordManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    private val audioRecordingManager = AudioRecordingManager(context)
    
    /**
     * 保存会议记录
     */
    fun saveMeetingRecord(record: MeetingRecord): Boolean {
        return try {
            val records = getAllRecords().toMutableList()
            
            // 检查是否已存在相同ID的记录，如果存在则更新
            val existingIndex = records.indexOfFirst { it.id == record.id }
            if (existingIndex >= 0) {
                records[existingIndex] = record
                Log.d(TAG, "更新会议记录: ${record.title}")
            } else {
                // 添加新记录到列表开头
                records.add(0, record)
                Log.d(TAG, "保存新会议记录: ${record.title}")
            }
            
            // 限制记录数量
            if (records.size > MAX_RECORDS) {
                records.subList(MAX_RECORDS, records.size).clear()
                Log.d(TAG, "清理旧记录，保持最大数量: $MAX_RECORDS")
            }
            
            // 保存到SharedPreferences
            val json = gson.toJson(records)
            prefs.edit().putString(KEY_RECORDS, json).apply()
            
            Log.i(TAG, "会议记录保存成功，当前总数: ${records.size}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存会议记录失败", e)
            false
        }
    }
    
    /**
     * 获取所有会议记录（按时间倒序）
     */
    fun getAllRecords(): List<MeetingRecord> {
        return try {
            val json = prefs.getString(KEY_RECORDS, null)
            if (json.isNullOrEmpty()) {
                emptyList()
            } else {
                val type = object : TypeToken<List<MeetingRecord>>() {}.type
                val records: List<MeetingRecord> = gson.fromJson(json, type)
                // 确保按时间倒序排列
                records.sortedByDescending { it.timestamp }
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取会议记录失败", e)
            emptyList()
        }
    }
    
    /**
     * 根据ID获取会议记录
     */
    fun getRecordById(id: String): MeetingRecord? {
        return getAllRecords().find { it.id == id }
    }
    
    /**
     * 删除会议记录
     */
    fun deleteRecord(id: String): Boolean {
        return try {
            val records = getAllRecords().toMutableList()
            val recordToDelete = records.find { it.id == id }
            val removed = records.removeAll { it.id == id }

            if (removed) {
                // 删除录音文件
                recordToDelete?.let { record ->
                    if (record.hasAudioFile()) {
                        audioRecordingManager.deleteRecording(record.audioFilePath!!)
                    }
                }

                val json = gson.toJson(records)
                prefs.edit().putString(KEY_RECORDS, json).apply()
                Log.i(TAG, "删除会议记录成功: $id")
            }

            removed
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录失败", e)
            false
        }
    }
    
    /**
     * 清空所有记录
     */
    fun clearAllRecords(): Boolean {
        return try {
            // 清空所有录音文件
            audioRecordingManager.clearAllRecordings()

            prefs.edit().remove(KEY_RECORDS).apply()
            Log.i(TAG, "清空所有会议记录")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清空记录失败", e)
            false
        }
    }
    
    /**
     * 获取记录总数
     */
    fun getRecordCount(): Int {
        return getAllRecords().size
    }
    
    /**
     * 创建会议记录（从当前录音会话）
     */
    fun createMeetingRecord(
        originalContent: String,
        optimizedContent: String = "",
        summaryContent: String = "",
        wordCount: Int = 0,
        duration: Long = 0,
        speakerCount: Int = 0,
        audioFilePath: String = ""
    ): MeetingRecord {
        // 生成智能标题
        val title = generateMeetingTitle(originalContent, summaryContent)

        val record = MeetingRecord(
            title = title,
            originalContent = originalContent,
            optimizedContent = optimizedContent,
            summaryContent = summaryContent,
            wordCount = wordCount,
            duration = duration,
            speakerCount = speakerCount,
            audioFilePath = audioFilePath
        )

        // 异步生成标题和标签
        generateTitleTagsAsync(record)

        return record
    }
    
    /**
     * 异步生成会议标签
     */
    private fun generateTagsAsync(record: MeetingRecord) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始为会议记录生成标签: ${record.id}")

                val result = LLMManager.generateTags(
                    context,
                    record.originalContent,
                    record.optimizedContent
                )

                if (result.success && result.content.isNotEmpty()) {
                    // 解析标签（逗号分隔）
                    val tags = result.content.split(",")
                        .map { it.trim() }
                        .filter { it.isNotEmpty() }
                        .take(3) // 最多3个标签

                    if (tags.isNotEmpty()) {
                        // 更新记录的标签
                        val updatedRecord = record.copy(tags = tags)
                        updateRecord(updatedRecord)

                        Log.d(TAG, "会议记录标签生成成功: ${tags.joinToString(", ")}")
                    }
                } else {
                    Log.w(TAG, "会议记录标签生成失败: ${result.error}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "会议记录标签生成异常", e)
            }
        }
    }

    /**
     * 异步生成会议标题和标签（统一方法，减少LLM调用次数）
     */
    private fun generateTitleTagsAsync(record: MeetingRecord) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始为会议记录生成标题和标签: ${record.id}")

                val result = LLMManager.generateTitleAndTags(
                    context,
                    record.originalContent,
                    record.optimizedContent
                )

                if (result.success && result.content.isNotEmpty()) {
                    try {
                        // 解析JSON结果
                        val jsonResult = org.json.JSONObject(result.content)
                        val title = jsonResult.optString("title", "")
                        val tagsArray = jsonResult.optJSONArray("tags")

                        var updatedRecord = record

                        // 更新标题
                        if (title.isNotBlank()) {
                            val cleanTitle = title.trim()
                                .removePrefix("标题：")
                                .removePrefix("会议标题：")
                                .removeSuffix("。")
                                .trim()

                            if (cleanTitle.isNotBlank()) {
                                updatedRecord = updatedRecord.copy(title = cleanTitle)
                                Log.d(TAG, "会议记录标题生成成功: $cleanTitle")
                            }
                        }

                        // 更新标签
                        if (tagsArray != null && tagsArray.length() > 0) {
                            val tags = mutableListOf<String>()
                            for (i in 0 until tagsArray.length()) {
                                val tag = tagsArray.optString(i, "").trim()
                                if (tag.isNotEmpty()) {
                                    tags.add(tag)
                                }
                            }

                            if (tags.isNotEmpty()) {
                                updatedRecord = updatedRecord.copy(tags = tags.take(3)) // 最多3个标签
                                Log.d(TAG, "会议记录标签生成成功: ${tags.joinToString(", ")}")
                            }
                        }

                        // 如果有更新，保存记录
                        if (updatedRecord != record) {
                            updateRecord(updatedRecord)
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "解析标题和标签JSON结果失败", e)
                        // 如果JSON解析失败，尝试作为纯文本处理
                        Log.d(TAG, "尝试作为纯文本处理标题和标签结果")
                    }
                } else {
                    Log.w(TAG, "会议记录标题和标签生成失败: ${result.error}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "会议记录标题和标签生成异常", e)
            }
        }
    }

    /**
     * 生成智能会议标题
     */
    private fun generateMeetingTitle(originalContent: String, summaryContent: String): String {
        // 优先从总结中提取关键词
        val contentForTitle = if (summaryContent.isNotEmpty()) summaryContent else originalContent
        
        if (contentForTitle.isEmpty()) {
            return "会议记录 ${SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(Date())}"
        }
        
        // 提取前20个字符作为标题，去除时间戳格式
        val cleanContent = contentForTitle
            .replace(Regex("\\[\\d{2}:\\d{2}:\\d{2}\\]"), "") // 移除时间戳
            .replace(Regex("-\\w+:"), "") // 移除说话人标识
            .trim()
        
        val title = if (cleanContent.length > 20) {
            cleanContent.take(20) + "..."
        } else {
            cleanContent
        }
        
        return if (title.isBlank()) {
            "会议记录 ${SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(Date())}"
        } else {
            title
        }
    }
    
    /**
     * 使用LLM生成智能会议标题
     * 异步方法，会在后台生成标题并更新记录
     */
    fun generateLLMTitleAsync(recordId: String, content: String) {
        // 在协程中检查LLM是否可用
        CoroutineScope(Dispatchers.IO).launch {
            if (!LLMManager.isCurrentLLMAvailable(context)) {
                Log.d(TAG, "LLM未配置，跳过智能标题生成")
                return@launch
            }

            performLLMTitleGeneration(recordId, content)
        }
    }

    private suspend fun performLLMTitleGeneration(recordId: String, content: String) {
        
        if (content.trim().isEmpty()) {
            Log.d(TAG, "内容为空，跳过智能标题生成")
            return
        }
        
        // 在协程中生成标题
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.generateMeetingTitle(context, content, "")
                if (result.success && result.content.isNotEmpty()) {
                    // 更新记录标题
                    updateRecordTitle(recordId, result.content)
                    Log.i(TAG, "LLM智能标题生成成功: ${result.content}")
                } else {
                    Log.w(TAG, "LLM标题生成失败: ${result.error}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "LLM标题生成异常", e)
            }
        }
    }
    
    /**
     * 更新会议记录
     */
    fun updateRecord(updatedRecord: MeetingRecord) {
        try {
            // 使用现有的saveMeetingRecord方法，它会自动处理更新逻辑
            val success = saveMeetingRecord(updatedRecord)
            if (success) {
                Log.d(TAG, "会议记录更新成功: ${updatedRecord.id}")
            } else {
                Log.w(TAG, "会议记录更新失败: ${updatedRecord.id}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录失败", e)
        }
    }

    /**
     * 更新记录标题
     */
    fun updateRecordTitle(recordId: String, newTitle: String) {
        try {
            val records = getAllRecords().toMutableList()
            val recordIndex = records.indexOfFirst { it.id == recordId }
            
            if (recordIndex >= 0) {
                val updatedRecord = records[recordIndex].copy(title = newTitle)
                records[recordIndex] = updatedRecord
                
                // 保存更新后的记录列表
                val json = gson.toJson(records)
                prefs.edit().putString(KEY_RECORDS, json).apply()
                
                Log.d(TAG, "记录标题更新成功: $newTitle")
            } else {
                Log.w(TAG, "未找到要更新的记录: $recordId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新记录标题失败", e)
        }
    }
    
    /**
     * 搜索会议记录
     */
    fun searchRecords(query: String): List<MeetingRecord> {
        if (query.isBlank()) return getAllRecords()
        
        return getAllRecords().filter { record ->
            record.title.contains(query, ignoreCase = true) ||
            record.originalContent.contains(query, ignoreCase = true) ||
            record.optimizedContent.contains(query, ignoreCase = true) ||
            record.summaryContent.contains(query, ignoreCase = true)
        }
    }
}
