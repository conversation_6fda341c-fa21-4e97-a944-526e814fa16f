package com.vectora.vocalmind

import java.text.SimpleDateFormat
import java.util.*

/**
 * 聊天消息数据类
 */
data class ChatMessage(
    val id: String = UUID.randomUUID().toString(),
    var content: String,
    val isFromUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val meetingRecordId: String
) {
    companion object {
        const val SENDER_USER = "user"
        const val SENDER_AI = "ai"
    }
    
    val sender: String
        get() = if (isFromUser) SENDER_USER else SENDER_AI
    /**
     * 获取格式化的时间戳
     */
    fun getFormattedTimestamp(): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }

    /**
     * 获取详细的时间戳
     */
    fun getDetailedTimestamp(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
}