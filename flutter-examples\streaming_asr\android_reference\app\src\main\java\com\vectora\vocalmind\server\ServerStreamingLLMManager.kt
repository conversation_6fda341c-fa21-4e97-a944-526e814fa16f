package com.vectora.vocalmind.server

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.vectora.vocalmind.ChatMessage
import com.vectora.vocalmind.LLMProvider
import com.vectora.vocalmind.StreamingCallback
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 服务器流式LLM管理器
 * 处理通过服务器的流式LLM响应
 */
class ServerStreamingLLMManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ServerStreamingLLMManager"
        private val JSON_MEDIA_TYPE = "application/json; charset=utf-8".toMediaType()
        
        @Volatile
        private var INSTANCE: ServerStreamingLLMManager? = null
        
        fun getInstance(context: Context): ServerStreamingLLMManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerStreamingLLMManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val gson = Gson()
    private var currentCall: Call? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 获取配置好的OkHttpClient
     */
    private fun getHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(ServerConfigManager.getConnectionTimeout(context), TimeUnit.MILLISECONDS)
            .readTimeout(ServerConfigManager.getReadTimeout(context), TimeUnit.MILLISECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .addInterceptor { chain ->
                val original = chain.request()
                val requestBuilder = original.newBuilder()
                    .header("Content-Type", "application/json")
                    .header("Accept", "text/event-stream")
                    .header("Cache-Control", "no-cache")
                    .header("User-Agent", "VocalMind-Android/1.0")
                
                // 添加认证头
                val authHeader = UserAuthManager.getAuthorizationHeader(context)
                if (authHeader != null) {
                    requestBuilder.header("Authorization", authHeader)
                }
                
                chain.proceed(requestBuilder.build())
            }
            .build()
    }
    
    /**
     * 发起流式聊天请求
     */
    fun streamChat(
        provider: LLMProvider,
        userMessage: String,
        meetingContent: String,
        chatHistory: List<ChatMessage> = emptyList(),
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始服务器流式聊天: provider=$provider")
        
        // 检查登录状态
        if (!UserAuthManager.isLoggedIn(context)) {
            callback.onError("用户未登录，请先登录服务器账户")
            return
        }
        
        scope.launch {
            try {
                // 构建请求
                val messages = buildMessageList(userMessage, meetingContent, chatHistory)
                val providerName = when (provider) {
                    LLMProvider.DEEPSEEK -> "deepseek"
                    LLMProvider.GEMINI -> "gemini"
                }
                
                val request = LLMChatRequest(providerName, messages, true)
                val requestBody = gson.toJson(request).toRequestBody(JSON_MEDIA_TYPE)
                
                val url = "${ServerConfigManager.getApiBaseUrl(context)}/llm/chat/stream"
                val httpRequest = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()
                
                // 执行请求
                currentCall = getHttpClient().newCall(httpRequest)
                currentCall?.enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "流式请求失败", e)
                        callback.onError("网络连接失败: ${e.message}")
                    }
                    
                    override fun onResponse(call: Call, response: Response) {
                        if (!response.isSuccessful) {
                            Log.e(TAG, "流式请求响应失败: ${response.code}")
                            callback.onError("服务器响应错误: ${response.code}")
                            return
                        }
                        
                        response.body?.let { responseBody ->
                            processStreamResponse(responseBody, callback)
                        } ?: run {
                            callback.onError("服务器响应为空")
                        }
                    }
                })
                
            } catch (e: Exception) {
                Log.e(TAG, "流式聊天异常", e)
                callback.onError("流式聊天异常: ${e.message}")
            }
        }
    }

    /**
     * 发起流式总结请求
     */
    fun streamSummary(
        meetingContent: String,
        summaryType: String,
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始服务器流式总结: summaryType=$summaryType")

        // 检查登录状态
        if (!UserAuthManager.isLoggedIn(context)) {
            callback.onError("用户未登录，请先登录服务器账户")
            return
        }

        scope.launch {
            try {
                // 构建总结提示词
                val prompt = buildMeetingSummaryPrompt(meetingContent, summaryType)
                val messages = listOf(
                    LLMMessage("user", prompt)
                )

                val request = LLMChatRequest("deepseek", messages, true) // 使用默认提供商
                val requestBody = gson.toJson(request).toRequestBody(JSON_MEDIA_TYPE)

                val url = "${ServerConfigManager.getApiBaseUrl(context)}/llm/chat/stream"
                val httpRequest = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // 执行请求
                currentCall = getHttpClient().newCall(httpRequest)
                currentCall?.enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "流式总结请求失败", e)
                        callback.onError("网络连接失败: ${e.message}")
                    }

                    override fun onResponse(call: Call, response: Response) {
                        if (!response.isSuccessful) {
                            Log.e(TAG, "流式总结响应失败: ${response.code}")
                            callback.onError("服务器响应错误: ${response.code}")
                            return
                        }

                        response.body?.let { responseBody ->
                            processStreamResponse(responseBody, callback)
                        } ?: run {
                            callback.onError("服务器响应为空")
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "流式总结异常", e)
                callback.onError("流式总结异常: ${e.message}")
            }
        }
    }

    /**
     * 发起流式内容优化请求
     */
    fun streamOptimizeContent(
        originalContent: String,
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始服务器流式内容优化")

        // 检查登录状态
        if (!UserAuthManager.isLoggedIn(context)) {
            callback.onError("用户未登录，请先登录服务器账户")
            return
        }

        scope.launch {
            try {
                // 构建优化提示词
                val prompt = buildOptimizePrompt(originalContent)
                val messages = listOf(
                    LLMMessage("user", prompt)
                )

                val request = LLMChatRequest("deepseek", messages, true) // 使用默认提供商
                val requestBody = gson.toJson(request).toRequestBody(JSON_MEDIA_TYPE)

                val url = "${ServerConfigManager.getApiBaseUrl(context)}/llm/chat/stream"
                val httpRequest = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // 执行请求
                currentCall = getHttpClient().newCall(httpRequest)
                currentCall?.enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "流式优化请求失败", e)
                        callback.onError("网络连接失败: ${e.message}")
                    }

                    override fun onResponse(call: Call, response: Response) {
                        if (!response.isSuccessful) {
                            Log.e(TAG, "流式优化响应失败: ${response.code}")
                            callback.onError("服务器响应错误: ${response.code}")
                            return
                        }

                        response.body?.let { responseBody ->
                            processStreamResponse(responseBody, callback)
                        } ?: run {
                            callback.onError("服务器响应为空")
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "流式优化异常", e)
                callback.onError("流式优化异常: ${e.message}")
            }
        }
    }

    /**
     * 发起流式Mermaid生成请求
     */
    fun streamMermaidGeneration(
        meetingContent: String,
        mermaidType: String,
        callback: StreamingCallback
    ) {
        Log.d(TAG, "开始服务器流式Mermaid生成: mermaidType=$mermaidType")

        // 检查登录状态
        if (!UserAuthManager.isLoggedIn(context)) {
            callback.onError("用户未登录，请先登录服务器账户")
            return
        }

        scope.launch {
            try {
                // 构建Mermaid生成提示词
                val prompt = buildMermaidPrompt(meetingContent, mermaidType)
                val messages = listOf(
                    LLMMessage("user", prompt)
                )

                val request = LLMChatRequest("deepseek", messages, true) // 使用默认提供商
                val requestBody = gson.toJson(request).toRequestBody(JSON_MEDIA_TYPE)

                val url = "${ServerConfigManager.getApiBaseUrl(context)}/llm/chat/stream"
                val httpRequest = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // 执行请求
                currentCall = getHttpClient().newCall(httpRequest)
                currentCall?.enqueue(object : Callback {
                    override fun onFailure(call: Call, e: IOException) {
                        Log.e(TAG, "流式Mermaid生成请求失败", e)
                        callback.onError("网络连接失败: ${e.message}")
                    }

                    override fun onResponse(call: Call, response: Response) {
                        if (!response.isSuccessful) {
                            Log.e(TAG, "流式Mermaid生成响应失败: ${response.code}")
                            callback.onError("服务器响应错误: ${response.code}")
                            return
                        }

                        response.body?.let { responseBody ->
                            processStreamResponse(responseBody, callback)
                        } ?: run {
                            callback.onError("服务器响应为空")
                        }
                    }
                })

            } catch (e: Exception) {
                Log.e(TAG, "流式Mermaid生成异常", e)
                callback.onError("流式Mermaid生成异常: ${e.message}")
            }
        }
    }

    /**
     * 构建会议总结提示词
     */
    private fun buildMeetingSummaryPrompt(meetingContent: String, summaryType: String): String {
        val promptTemplate = when (summaryType) {
            "brief" -> "请为以下会议内容生成简要总结，突出关键要点："
            "detailed" -> "请为以下会议内容生成详细总结，包括讨论要点、决策和行动项："
            "action_items" -> "请从以下会议内容中提取行动项和待办事项："
            "decisions" -> "请从以下会议内容中提取重要决策和结论："
            else -> "请为以下会议内容生成总结："
        }

        return """
        $promptTemplate

        会议内容：
        $meetingContent

        请使用清晰的格式组织总结内容。
        """.trimIndent()
    }

    /**
     * 构建内容优化提示词
     */
    private fun buildOptimizePrompt(originalContent: String): String {
        return """
        请对以下语音识别(ASR)内容进行优化和修正，要求：

        1. **语义准确性**：修正明显的识别错误，如同音字错误、语音识别常见错误
        2. **语言流畅性**：优化语句结构，使表达更加自然流畅
        3. **逻辑合理性**：整理语序，确保逻辑清晰、前后连贯
        4. **保持原意**：完全保持原始含义和重要信息，不添加、不删除关键内容
        5. **标点符号**：添加合适的标点符号，提高可读性
        6. **说话人分段**：将同一说话人的连续发言合并为一个段落

        原始内容：
        $originalContent

        请直接返回优化后的内容：
        """.trimIndent()
    }

    /**
     * 构建Mermaid生成提示词（使用LLMManager的详细模板）
     */
    private fun buildMermaidPrompt(meetingContent: String, mermaidType: String): String {
        // 使用LLMManager中的详细提示词模板，确保思维导图和流程图的正确区分
        return com.vectora.vocalmind.LLMManager.buildMermaidPrompt(meetingContent, mermaidType)
    }

    /**
     * 处理流式响应
     */
    private fun processStreamResponse(responseBody: ResponseBody, callback: StreamingCallback) {
        try {
            val source = responseBody.source()
            var totalContent = StringBuilder()
            
            callback.onConnectionOpened()

            while (!source.exhausted()) {
                val line = source.readUtf8Line()
                if (line == null) break

                Log.d(TAG, "收到SSE行: $line")

                // 处理SSE格式
                if (line.startsWith("data: ")) {
                    val data = line.substring(6) // 移除 "data: " 前缀

                    if (data.trim() == "[DONE]") {
                        Log.d(TAG, "流式响应完成")
                        break
                    }

                    try {
                        val chunk = gson.fromJson(data, StreamChunk::class.java)

                        when (chunk.type) {
                            "chunk" -> {
                                chunk.content?.let { content ->
                                    totalContent.append(content)
                                    callback.onDataReceived(content)
                                }
                            }
                            "done" -> {
                                Log.d(TAG, "收到完成信号")
                                // 使用量信息可以记录到日志，但StreamingCallback接口没有对应方法
                                chunk.usage?.let { usage ->
                                    Log.d(TAG, "使用量信息: tokens=${usage.tokensUsed ?: usage.tokens}, cost=${usage.cost}")
                                }
                                break
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "解析流式数据失败: $data", e)
                        // 继续处理下一行，不中断整个流
                    }
                }
            }

            callback.onCompleted(totalContent.toString())
            
        } catch (e: Exception) {
            Log.e(TAG, "处理流式响应异常", e)
            callback.onError("处理响应异常: ${e.message}")
        } finally {
            responseBody.close()
        }
    }
    
    /**
     * 取消当前流式请求
     */
    fun cancelCurrentRequest() {
        currentCall?.cancel()
        currentCall = null
        Log.d(TAG, "已取消当前流式请求")
    }
    
    /**
     * 检查是否有正在进行的请求
     */
    fun hasActiveRequest(): Boolean {
        return currentCall != null && !currentCall!!.isCanceled()
    }
    
    /**
     * 构建消息列表
     */
    private fun buildMessageList(
        userMessage: String,
        meetingContent: String,
        chatHistory: List<ChatMessage>
    ): List<LLMMessage> {
        val messages = mutableListOf<LLMMessage>()
        
        // 添加系统提示
        val systemPrompt = """
        你是一个专业的会议助手，基于以下会议内容回答用户问题：
        
        会议内容：
        $meetingContent
        
        请根据会议内容准确回答用户的问题，如果问题与会议内容无关，请礼貌地说明。
        """.trimIndent()
        
        messages.add(LLMMessage("user", systemPrompt))
        messages.add(LLMMessage("assistant", "我已经了解了会议内容，请问您有什么问题？"))
        
        // 添加聊天历史
        chatHistory.forEach { chatMessage ->
            when (chatMessage.isFromUser) {
                true -> messages.add(LLMMessage("user", chatMessage.content))
                false -> messages.add(LLMMessage("assistant", chatMessage.content))
            }
        }
        
        // 添加当前用户消息
        messages.add(LLMMessage("user", userMessage))
        
        return messages
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        cancelCurrentRequest()
        scope.cancel()
        Log.d(TAG, "资源已清理")
    }
}
