# VocalMind API接口文档

## 概述

本文档详细描述了VocalMind项目中各个模块的API接口设计，包括核心类的方法签名、参数说明、返回值类型以及使用示例。

## 核心API接口

### 1. ASR引擎接口

#### SingleModelASREngine

```kotlin
class SingleModelASREngine(
    private val assetManager: AssetManager,
    private val sampleRateInHz: Int = 16000,
    private val enableSpeakerIdentification: Boolean = true,
    private val speakerThreshold: Float = 0.3f,
    context: android.content.Context? = null
) {
    
    interface ASRListener {
        /**
         * 识别结果回调
         * @param result ASR识别结果
         */
        fun onRecognitionResult(result: ASRResult)
        
        /**
         * 错误回调
         * @param error 错误信息
         */
        fun onError(error: String)
        
        /**
         * 状态变化回调
         * @param status 当前状态
         */
        fun onStatusChanged(status: String)
    }
    
    /**
     * 初始化ASR引擎
     * @return 是否初始化成功
     */
    fun initialize(): Boolean
    
    /**
     * 开始语音识别
     * @param listener 识别结果监听器
     */
    fun startRecognition(listener: ASRListener)
    
    /**
     * 处理音频数据
     * @param audioData 音频数据数组
     * @param length 有效数据长度
     */
    fun processAudio(audioData: FloatArray, length: Int)
    
    /**
     * 停止语音识别
     * @return 最终识别结果
     */
    fun stopRecognition(): ASRResult
    
    /**
     * 重置识别状态
     */
    fun reset()
    
    /**
     * 释放资源
     */
    fun release()
}

/**
 * ASR识别结果数据类
 */
data class ASRResult(
    val text: String = "",                    // 识别文本
    val isPrediction: Boolean = false,         // 是否为预测结果
    val confidence: Float = 0.0f,              // 置信度
    val speakerId: String? = null,             // 说话人ID
    val speakerName: String? = null,           // 说话人姓名
    val timestamp: Long = System.currentTimeMillis(), // 时间戳
    val isEndpoint: Boolean = false,           // 是否为端点
    val audioQuality: Float = 1.0f             // 音频质量评分
)
```

#### 使用示例

```kotlin
// 初始化ASR引擎
val asrEngine = SingleModelASREngine(
    assetManager = assets,
    sampleRateInHz = 16000,
    enableSpeakerIdentification = true,
    context = this
)

// 设置监听器
val listener = object : SingleModelASREngine.ASRListener {
    override fun onRecognitionResult(result: ASRResult) {
        if (result.isPrediction) {
            // 处理预测结果
            updatePredictionText(result.text)
        } else {
            // 处理最终结果
            updateFinalText(result.text)
        }
    }
    
    override fun onError(error: String) {
        Log.e(TAG, "ASR Error: $error")
        showErrorMessage(error)
    }
    
    override fun onStatusChanged(status: String) {
        updateStatusDisplay(status)
    }
}

// 开始识别
if (asrEngine.initialize()) {
    asrEngine.startRecognition(listener)
} else {
    Log.e(TAG, "ASR引擎初始化失败")
}
```

### 2. LLM管理接口

#### LLMManager

```kotlin
object LLMManager {
    
    /**
     * 获取当前LLM配置
     * @param context 上下文
     * @return LLM配置对象
     */
    fun getCurrentLLMConfig(context: Context): LLMConfig
    
    /**
     * 检查当前LLM是否可用
     * @param context 上下文
     * @return 是否可用
     */
    fun isCurrentLLMAvailable(context: Context): Boolean
    
    /**
     * 优化ASR识别内容
     * @param context 上下文
     * @param originalContent 原始内容
     * @return LLM处理结果
     */
    suspend fun optimizeAsrContent(
        context: Context, 
        originalContent: String
    ): LLMResult
    
    /**
     * 生成会议总结
     * @param context 上下文
     * @param speechContent 语音内容
     * @param summaryType 总结类型
     * @return LLM处理结果
     */
    suspend fun generateSummary(
        context: Context,
        speechContent: String,
        summaryType: SummaryType = SummaryType.MEETING
    ): LLMResult
    
    /**
     * 生成Mermaid流程图
     * @param context 上下文
     * @param content 内容
     * @param diagramType 图表类型
     * @return LLM处理结果
     */
    suspend fun generateMermaidDiagram(
        context: Context,
        content: String,
        diagramType: MermaidType = MermaidType.FLOWCHART
    ): LLMResult
    
    /**
     * AI聊天对话
     * @param context 上下文
     * @param prompt 用户输入
     * @param conversationContext 对话上下文
     * @return LLM处理结果
     */
    suspend fun chatWithAI(
        context: Context,
        prompt: String,
        conversationContext: String = ""
    ): LLMResult
}

/**
 * LLM处理结果
 */
data class LLMResult(
    val success: Boolean,                      // 是否成功
    val content: String,                       // 返回内容
    val error: String? = null,                 // 错误信息
    val usage: LLMUsage? = null,              // 使用统计
    val processingTime: Long = 0L              // 处理时间(ms)
)

/**
 * LLM使用统计
 */
data class LLMUsage(
    val promptTokens: Int,                     // 输入token数
    val completionTokens: Int,                 // 输出token数
    val totalTokens: Int                       // 总token数
)
```

#### 使用示例

```kotlin
// 优化ASR内容
lifecycleScope.launch {
    val result = LLMManager.optimizeAsrContent(
        context = this@MainActivity,
        originalContent = "这是一段需要优化的ASR识别文本"
    )
    
    if (result.success) {
        // 使用优化后的内容
        updateOptimizedContent(result.content)
    } else {
        // 处理错误
        showError(result.error ?: "优化失败")
    }
}

// 生成会议总结
lifecycleScope.launch {
    val summaryResult = LLMManager.generateSummary(
        context = this@MainActivity,
        speechContent = meetingContent,
        summaryType = SummaryType.MEETING
    )
    
    if (summaryResult.success) {
        displaySummary(summaryResult.content)
    }
}
```

### 3. 会议记录管理接口

#### MeetingRecordManager

```kotlin
class MeetingRecordManager private constructor(private val context: Context) {
    
    companion object {
        fun getInstance(context: Context): MeetingRecordManager
    }
    
    /**
     * 保存会议记录
     * @param record 会议记录对象
     * @return 是否保存成功
     */
    fun saveMeetingRecord(record: MeetingRecord): Boolean
    
    /**
     * 获取所有会议记录
     * @return 会议记录列表（按时间倒序）
     */
    fun getAllRecords(): List<MeetingRecord>
    
    /**
     * 根据ID获取会议记录
     * @param id 记录ID
     * @return 会议记录对象，不存在返回null
     */
    fun getRecordById(id: String): MeetingRecord?
    
    /**
     * 删除会议记录
     * @param id 记录ID
     * @return 是否删除成功
     */
    fun deleteRecord(id: String): Boolean
    
    /**
     * 批量删除会议记录
     * @param ids 记录ID列表
     * @return 删除成功的数量
     */
    fun deleteRecords(ids: List<String>): Int
    
    /**
     * 搜索会议记录
     * @param query 搜索关键词
     * @return 匹配的会议记录列表
     */
    fun searchRecords(query: String): List<MeetingRecord>
    
    /**
     * 更新会议记录
     * @param record 更新后的会议记录
     * @return 是否更新成功
     */
    fun updateRecord(record: MeetingRecord): Boolean
    
    /**
     * 获取记录统计信息
     * @return 统计信息对象
     */
    fun getStatistics(): RecordStatistics
    
    /**
     * 清空所有记录
     * @return 是否清空成功
     */
    fun clearAllRecords(): Boolean
}

/**
 * 会议记录数据模型
 */
data class MeetingRecord(
    val id: String = UUID.randomUUID().toString(),
    val title: String = "未命名会议",
    val timestamp: Long = System.currentTimeMillis(),
    val originalContent: String = "",
    val optimizedContent: String = "",
    val summaryContent: String = "",
    var mermaidContent: String = "",
    val wordCount: Int = 0,
    val duration: Long = 0,
    val speakerCount: Int = 0,
    val audioFilePath: String? = ""
) {
    fun getFormattedDateTime(): String
    fun getShortDate(): String
    fun getFormattedDuration(): String
    fun getContentPreview(): String
    fun hasAudioFile(): Boolean
    fun getWordCountDisplay(): String
}

/**
 * 记录统计信息
 */
data class RecordStatistics(
    val totalRecords: Int,                     // 总记录数
    val totalDuration: Long,                   // 总时长(秒)
    val totalWords: Int,                       // 总字数
    val averageDuration: Long,                 // 平均时长
    val averageWords: Int,                     // 平均字数
    val oldestRecord: Long,                    // 最早记录时间
    val newestRecord: Long                     // 最新记录时间
)
```

### 4. 聊天管理接口

#### ChatManager

```kotlin
class ChatManager private constructor(private val context: Context) {
    
    companion object {
        fun getInstance(context: Context): ChatManager
    }
    
    /**
     * 保存聊天消息
     * @param message 聊天消息对象
     */
    fun saveChatMessage(message: ChatMessage)
    
    /**
     * 获取聊天历史
     * @param meetingRecordId 会议记录ID
     * @return 聊天消息列表
     */
    fun getChatHistory(meetingRecordId: String): List<ChatMessage>
    
    /**
     * 清空聊天历史
     * @param meetingRecordId 会议记录ID
     */
    fun clearChatHistory(meetingRecordId: String)
    
    /**
     * 删除指定消息
     * @param messageId 消息ID
     * @param meetingRecordId 会议记录ID
     * @return 是否删除成功
     */
    fun deleteMessage(messageId: String, meetingRecordId: String): Boolean
    
    /**
     * 获取聊天统计
     * @param meetingRecordId 会议记录ID
     * @return 聊天统计信息
     */
    fun getChatStatistics(meetingRecordId: String): ChatStatistics
}

/**
 * 聊天消息数据模型
 */
data class ChatMessage(
    val id: String = UUID.randomUUID().toString(),
    val meetingRecordId: String,
    val content: String,
    val isUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val isStreaming: Boolean = false
) {
    fun getFormattedTime(): String
    fun getDisplayContent(): String
}

/**
 * 聊天统计信息
 */
data class ChatStatistics(
    val totalMessages: Int,                    // 总消息数
    val userMessages: Int,                     // 用户消息数
    val aiMessages: Int,                       // AI消息数
    val firstMessageTime: Long,                // 首条消息时间
    val lastMessageTime: Long                  // 最后消息时间
)
```

### 5. 音频录制管理接口

#### AudioRecordingManager

```kotlin
class AudioRecordingManager(private val context: Context) {
    
    /**
     * 开始录音
     * @param meetingId 会议ID
     * @return 录音文件路径，失败返回null
     */
    fun startRecording(meetingId: String): String?
    
    /**
     * 写入音频数据
     * @param audioData 音频数据
     * @param length 数据长度
     */
    fun writeAudioData(audioData: ShortArray, length: Int)
    
    /**
     * 停止录音
     * @return 最终录音文件路径
     */
    fun stopRecording(): String?
    
    /**
     * 获取录音文件
     * @param filePath 文件路径
     * @return 文件对象，不存在返回null
     */
    fun getRecordingFile(filePath: String): File?
    
    /**
     * 删除录音文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    fun deleteRecordingFile(filePath: String): Boolean
    
    /**
     * 获取录音文件信息
     * @param filePath 文件路径
     * @return 文件信息对象
     */
    fun getRecordingInfo(filePath: String): AudioFileInfo?
    
    /**
     * 清理过期录音文件
     * @param daysOld 保留天数
     * @return 清理的文件数量
     */
    fun cleanupOldRecordings(daysOld: Int = 30): Int
    
    /**
     * 获取录音目录大小
     * @return 目录大小（字节）
     */
    fun getRecordingDirectorySize(): Long
}

/**
 * 音频文件信息
 */
data class AudioFileInfo(
    val filePath: String,                      // 文件路径
    val fileName: String,                      // 文件名
    val fileSize: Long,                        // 文件大小（字节）
    val duration: Long,                        // 时长（秒）
    val sampleRate: Int,                       // 采样率
    val channels: Int,                         // 声道数
    val bitRate: Int,                          // 比特率
    val format: String,                        // 格式
    val createdTime: Long                      // 创建时间
) {
    fun getFormattedSize(): String
    fun getFormattedDuration(): String
    fun getFormattedCreatedTime(): String
}
```

### 6. 悬浮窗服务接口

#### FloatingWindowService

```kotlin
class FloatingWindowService : Service(), FloatingWindowView.FloatingWindowCallback {
    
    inner class FloatingWindowBinder : Binder() {
        fun getService(): FloatingWindowService = this@FloatingWindowService
    }
    
    /**
     * 显示悬浮窗
     * @return 是否显示成功
     */
    fun showFloatingWindow(): Boolean
    
    /**
     * 隐藏悬浮窗
     */
    fun hideFloatingWindow()
    
    /**
     * 更新悬浮窗状态
     * @param isRecording 是否正在录音
     */
    fun updateRecordingState(isRecording: Boolean)
    
    /**
     * 设置悬浮窗位置
     * @param x X坐标
     * @param y Y坐标
     */
    fun setFloatingWindowPosition(x: Int, y: Int)
    
    /**
     * 获取悬浮窗是否显示
     * @return 是否显示
     */
    fun isFloatingWindowShowing(): Boolean
    
    // FloatingWindowCallback 实现
    override fun onStartRecording()
    override fun onStopRecording()
    override fun onCloseWindow()
    override fun onOpenMainActivity()
}

/**
 * 悬浮窗回调接口
 */
interface FloatingWindowCallback {
    /**
     * 开始录音回调
     */
    fun onStartRecording()
    
    /**
     * 停止录音回调
     */
    fun onStopRecording()
    
    /**
     * 关闭悬浮窗回调
     */
    fun onCloseWindow()
    
    /**
     * 打开主界面回调
     */
    fun onOpenMainActivity()
}
```

## 配置接口

### 1. LLM配置接口

```kotlin
abstract class LLMConfig {
    /**
     * 获取LLM提供商
     */
    abstract fun getProvider(): LLMProvider
    
    /**
     * 获取API URL
     */
    abstract fun getApiUrl(): String
    
    /**
     * 获取API密钥
     */
    abstract fun getApiKey(context: Context): String
    
    /**
     * 检查API密钥是否已配置
     */
    abstract fun isApiKeyConfigured(context: Context): Boolean
    
    /**
     * 构建请求体
     */
    abstract fun buildRequest(prompt: String): String
    
    /**
     * 解析响应
     */
    abstract fun parseResponse(response: String): LLMResult
    
    /**
     * 获取模型名称
     */
    abstract fun getModelName(): String
    
    /**
     * 获取最大token数
     */
    abstract fun getMaxTokens(): Int
}

/**
 * LLM提供商枚举
 */
enum class LLMProvider(val displayName: String, val configKey: String) {
    GEMINI("Google Gemini", "gemini"),
    DEEPSEEK("DeepSeek", "deepseek")
}
```

### 2. 功能配置接口

```kotlin
object FeatureConfig {
    /**
     * 检查功能是否启用
     */
    fun isFeatureEnabled(context: Context, feature: Feature): Boolean
    
    /**
     * 启用/禁用功能
     */
    fun setFeatureEnabled(context: Context, feature: Feature, enabled: Boolean)
    
    /**
     * 获取所有功能状态
     */
    fun getAllFeatureStates(context: Context): Map<Feature, Boolean>
    
    /**
     * 重置所有功能为默认状态
     */
    fun resetToDefaults(context: Context)
}

/**
 * 功能枚举
 */
enum class Feature(val displayName: String, val defaultEnabled: Boolean) {
    SPEAKER_IDENTIFICATION("说话人识别", true),
    AUTO_OPTIMIZATION("自动优化", true),
    AUTO_SUMMARY("自动总结", false),
    FLOATING_WINDOW("悬浮窗", false),
    MERMAID_GENERATION("流程图生成", false),
    AUDIO_RECORDING("录音保存", true)
}
```

## 错误码定义

```kotlin
object ErrorCodes {
    // ASR相关错误
    const val ASR_INIT_FAILED = 1001
    const val ASR_PERMISSION_DENIED = 1002
    const val ASR_AUDIO_RECORD_ERROR = 1003
    const val ASR_ENGINE_ERROR = 1004
    
    // LLM相关错误
    const val LLM_API_KEY_MISSING = 2001
    const val LLM_NETWORK_ERROR = 2002
    const val LLM_API_ERROR = 2003
    const val LLM_PARSE_ERROR = 2004
    const val LLM_QUOTA_EXCEEDED = 2005
    
    // 存储相关错误
    const val STORAGE_PERMISSION_DENIED = 3001
    const val STORAGE_SPACE_INSUFFICIENT = 3002
    const val STORAGE_IO_ERROR = 3003
    
    // 悬浮窗相关错误
    const val FLOATING_WINDOW_PERMISSION_DENIED = 4001
    const val FLOATING_WINDOW_SYSTEM_ERROR = 4002
    
    // 通用错误
    const val UNKNOWN_ERROR = 9999
}

/**
 * 错误信息获取
 */
fun getErrorMessage(context: Context, errorCode: Int): String {
    return when (errorCode) {
        ErrorCodes.ASR_INIT_FAILED -> "ASR引擎初始化失败"
        ErrorCodes.ASR_PERMISSION_DENIED -> "缺少录音权限"
        ErrorCodes.LLM_API_KEY_MISSING -> "LLM API密钥未配置"
        ErrorCodes.LLM_NETWORK_ERROR -> "网络连接失败"
        ErrorCodes.STORAGE_SPACE_INSUFFICIENT -> "存储空间不足"
        ErrorCodes.FLOATING_WINDOW_PERMISSION_DENIED -> "缺少悬浮窗权限"
        else -> "未知错误"
    }
}
```

## 使用最佳实践

### 1. 生命周期管理

```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var asrEngine: SingleModelASREngine
    private lateinit var meetingRecordManager: MeetingRecordManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化管理器（单例模式）
        meetingRecordManager = MeetingRecordManager.getInstance(this)
        
        // 初始化ASR引擎
        asrEngine = SingleModelASREngine(
            assetManager = assets,
            context = this
        )
    }
    
    override fun onResume() {
        super.onResume()
        // 检查权限和初始化状态
        checkPermissionsAndInitialize()
    }
    
    override fun onPause() {
        super.onPause()
        // 暂停录音（如果正在录音）
        if (isRecording) {
            pauseRecording()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 释放资源
        asrEngine.release()
    }
}
```

### 2. 错误处理

```kotlin
try {
    val result = LLMManager.optimizeAsrContent(this, content)
    if (result.success) {
        handleSuccess(result.content)
    } else {
        handleError(result.error)
    }
} catch (e: Exception) {
    Log.e(TAG, "LLM调用异常", e)
    handleException(e)
}
```

### 3. 异步处理

```kotlin
// 使用协程处理耗时操作
lifecycleScope.launch {
    try {
        showLoading(true)
        
        val optimizedResult = withContext(Dispatchers.IO) {
            LLMManager.optimizeAsrContent(this@MainActivity, originalText)
        }
        
        if (optimizedResult.success) {
            updateUI(optimizedResult.content)
        } else {
            showError(optimizedResult.error)
        }
    } catch (e: Exception) {
        showError("处理失败: ${e.message}")
    } finally {
        showLoading(false)
    }
}
```

---

*本API文档提供了VocalMind项目的完整接口说明，开发者可以根据此文档进行功能集成和扩展开发。*