package com.vectora.vocalmind

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.*
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat

/**
 * 悬浮窗视图
 * 支持拖拽、点击、长按交互，边缘吸附，录音状态动画
 */
class FloatingWindowView(
    private val context: Context,
    private val windowManager: WindowManager,
    private val callback: FloatingWindowCallback
) : FrameLayout(context) {

    companion object {
        private const val TAG = "FloatingWindowView"
        private const val EDGE_MARGIN = 20 // 边缘吸附边距
        private const val LONG_PRESS_TIMEOUT = 500L // 长按时间
        private const val MENU_HIDE_DELAY = 3000L // 菜单自动隐藏时间
    }

    // UI组件
    private lateinit var cvFloatingButton: CardView
    private lateinit var ivMicIcon: ImageView
    private lateinit var viewPulseBackground: View
    private lateinit var llActionMenu: LinearLayout
    private lateinit var cvOpenApp: CardView
    private lateinit var cvCloseFloating: CardView

    // 窗口参数
    private val windowParams: WindowManager.LayoutParams
    private val displayMetrics = DisplayMetrics()

    // 交互状态
    private var isDragging = false
    private var isMenuVisible = false
    private var isRecording = false
    private var isLongPressed = false // 长按标志
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f

    // 动画相关
    private var pulseAnimator: AnimatorSet? = null
    private val handler = Handler(Looper.getMainLooper())
    private var longPressRunnable: Runnable? = null
    private var menuHideRunnable: Runnable? = null

    init {
        // 获取屏幕尺寸
        windowManager.defaultDisplay.getMetrics(displayMetrics)

        // 设置窗口参数
        windowParams = WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.WRAP_CONTENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            type = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            format = PixelFormat.TRANSLUCENT
            gravity = Gravity.TOP or Gravity.START
            x = displayMetrics.widthPixels - 200 // 默认位置右侧
            y = displayMetrics.heightPixels / 2
        }

        initView()
        setupTouchListener()
    }

    /**
     * 初始化视图
     */
    private fun initView() {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.floating_window_layout, this, true)

        // 初始化UI组件
        cvFloatingButton = findViewById(R.id.cv_floating_button)
        ivMicIcon = findViewById(R.id.iv_mic_icon)
        viewPulseBackground = findViewById(R.id.view_pulse_background)
        llActionMenu = findViewById(R.id.ll_action_menu)
        cvOpenApp = findViewById(R.id.cv_open_app)
        cvCloseFloating = findViewById(R.id.cv_close_floating)

        // 设置点击事件
        cvOpenApp.setOnClickListener {
            hideMenu()
            callback.onOpenMainApp()
        }

        cvCloseFloating.setOnClickListener {
            hideMenu()
            callback.onCloseFloatingWindow()
        }

        Log.d(TAG, "悬浮窗视图初始化完成")
    }

    /**
     * 设置触摸监听器
     */
    private fun setupTouchListener() {
        cvFloatingButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    handleTouchDown(event)
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    handleTouchMove(event)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    handleTouchUp(event)
                    true
                }
                else -> false
            }
        }
    }

    /**
     * 处理触摸按下
     */
    private fun handleTouchDown(event: MotionEvent) {
        initialX = windowParams.x
        initialY = windowParams.y
        initialTouchX = event.rawX
        initialTouchY = event.rawY
        isDragging = false
        isLongPressed = false // 重置长按标志

        // 隐藏菜单
        if (isMenuVisible) {
            hideMenu()
        }

        // 开始长按检测
        longPressRunnable = Runnable {
            if (!isDragging) {
                showMenu()
            }
        }
        handler.postDelayed(longPressRunnable!!, LONG_PRESS_TIMEOUT)
    }

    /**
     * 处理触摸移动
     */
    private fun handleTouchMove(event: MotionEvent) {
        val deltaX = event.rawX - initialTouchX
        val deltaY = event.rawY - initialTouchY

        // 判断是否开始拖拽
        if (!isDragging && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
            isDragging = true
            // 取消长按检测
            longPressRunnable?.let { handler.removeCallbacks(it) }
        }

        if (isDragging) {
            // 更新窗口位置
            windowParams.x = initialX + deltaX.toInt()
            windowParams.y = initialY + deltaY.toInt()
            
            // 限制在屏幕范围内
            windowParams.x = windowParams.x.coerceIn(0, displayMetrics.widthPixels - width)
            windowParams.y = windowParams.y.coerceIn(0, displayMetrics.heightPixels - height)
            
            windowManager.updateViewLayout(this, windowParams)
        }
    }

    /**
     * 处理触摸抬起
     */
    private fun handleTouchUp(event: MotionEvent) {
        // 取消长按检测
        longPressRunnable?.let { handler.removeCallbacks(it) }

        if (isDragging) {
            // 边缘吸附
            snapToEdge()
            isDragging = false
        } else if (!isLongPressed) {
            // 单击事件 - 切换录音状态（只有在非长按情况下才触发）
            callback.onToggleRecording()
        }
        
        // 重置长按标志
        isLongPressed = false
    }

    /**
     * 边缘吸附
     */
    private fun snapToEdge() {
        val screenWidth = displayMetrics.widthPixels
        val buttonCenterX = windowParams.x + width / 2

        val targetX = if (buttonCenterX < screenWidth / 2) {
            EDGE_MARGIN // 吸附到左边
        } else {
            screenWidth - width - EDGE_MARGIN // 吸附到右边
        }

        // 动画移动到边缘
        val animator = ValueAnimator.ofInt(windowParams.x, targetX)
        animator.duration = 300
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.addUpdateListener { animation ->
            windowParams.x = animation.animatedValue as Int
            windowManager.updateViewLayout(this@FloatingWindowView, windowParams)
        }
        animator.start()

        Log.d(TAG, "边缘吸附: 从 ${windowParams.x} 移动到 $targetX")
    }

    /**
     * 显示下拉菜单
     */
    private fun showMenu() {
        if (isMenuVisible) return

        isLongPressed = true // 设置长按标志
        
        llActionMenu.visibility = View.VISIBLE
        llActionMenu.alpha = 0f
        
        // 立即隐藏录音按钮（避免闪烁）
        cvFloatingButton.alpha = 0f
        
        // 为每个按钮设置初始状态
        cvOpenApp.alpha = 0f
        cvOpenApp.scaleX = 0.8f
        cvOpenApp.scaleY = 0.8f
        
        cvCloseFloating.alpha = 0f
        cvCloseFloating.scaleX = 0.8f
        cvCloseFloating.scaleY = 0.8f

        // 整体菜单动画
        llActionMenu.animate()
            .alpha(1f)
            .setDuration(250)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
            
        // 第一个按钮动画（延迟50ms）
        cvOpenApp.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(200)
            .setStartDelay(50)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
            
        // 第二个按钮动画（延迟100ms）
        cvCloseFloating.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(200)
            .setStartDelay(100)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()

        isMenuVisible = true

        // 自动隐藏菜单
        menuHideRunnable = Runnable { hideMenu() }
        handler.postDelayed(menuHideRunnable!!, MENU_HIDE_DELAY)

        Log.d(TAG, "显示下拉菜单")
    }

    /**
     * 隐藏下拉菜单
     */
    private fun hideMenu() {
        if (!isMenuVisible) return

        // 按钮隐藏动画（逆序）
        cvCloseFloating.animate()
            .alpha(0f)
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(150)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
            
        cvOpenApp.animate()
            .alpha(0f)
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(150)
            .setStartDelay(50)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()

        // 整体菜单隐藏动画
        llActionMenu.animate()
            .alpha(0f)
            .setDuration(200)
            .setStartDelay(100)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction {
                llActionMenu.visibility = View.GONE
                // 恢复录音按钮显示
                cvFloatingButton.animate()
                    .alpha(1f)
                    .setDuration(150)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .start()
            }
            .start()

        isMenuVisible = false

        // 取消自动隐藏
        menuHideRunnable?.let { handler.removeCallbacks(it) }

        Log.d(TAG, "隐藏下拉菜单")
    }

    /**
     * 更新录音状态
     */
    fun updateRecordingState(recording: Boolean) {
        if (isRecording == recording) return

        isRecording = recording
        
        if (recording) {
            startRecordingAnimation()
        } else {
            stopRecordingAnimation()
        }

        Log.d(TAG, "更新录音状态: $recording")
    }

    /**
     * 开始录音动画
     */
    private fun startRecordingAnimation() {
        // 停止之前的动画
        pulseAnimator?.cancel()

        // 更改按钮颜色为红色
        cvFloatingButton.setCardBackgroundColor(ContextCompat.getColor(context, android.R.color.holo_red_dark))
        
        // 显示脉动背景
        viewPulseBackground.visibility = View.VISIBLE

        // 创建脉动动画
        val scaleXAnimator = ObjectAnimator.ofFloat(viewPulseBackground, "scaleX", 1f, 1.3f, 1f).apply {
            repeatCount = ValueAnimator.INFINITE
        }
        val scaleYAnimator = ObjectAnimator.ofFloat(viewPulseBackground, "scaleY", 1f, 1.3f, 1f).apply {
            repeatCount = ValueAnimator.INFINITE
        }
        val alphaAnimator = ObjectAnimator.ofFloat(viewPulseBackground, "alpha", 0.8f, 0.3f, 0.8f).apply {
            repeatCount = ValueAnimator.INFINITE
        }

        pulseAnimator = AnimatorSet().apply {
            playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator)
            duration = 1000
            interpolator = AccelerateDecelerateInterpolator()
        }
        pulseAnimator?.start()
    }

    /**
     * 停止录音动画
     */
    private fun stopRecordingAnimation() {
        // 停止脉动动画
        pulseAnimator?.cancel()
        
        // 恢复按钮颜色为蓝色
        cvFloatingButton.setCardBackgroundColor(ContextCompat.getColor(context, android.R.color.holo_blue_bright))
        
        // 隐藏脉动背景
        viewPulseBackground.visibility = View.GONE
    }

    /**
     * 获取窗口参数
     */
    fun getWindowParams(): WindowManager.LayoutParams = windowParams

    /**
     * 清理资源
     */
    fun cleanup() {
        pulseAnimator?.cancel()
        handler.removeCallbacksAndMessages(null)
        Log.d(TAG, "悬浮窗视图资源已清理")
    }

    /**
     * 悬浮窗回调接口
     */
    interface FloatingWindowCallback {
        fun onToggleRecording()
        fun onOpenMainApp()
        fun onCloseFloatingWindow()
    }
}
