package com.vectora.vocalmind.server

import com.google.gson.annotations.SerializedName

/**
 * 服务器API数据模型
 */

// 基础响应模型
data class ApiResponse<T>(
    @SerializedName("success")
    val success: Boolean,
    @SerializedName("data")
    val data: T? = null,
    @SerializedName("error")
    val error: ApiError? = null,
    @SerializedName("message")
    val message: String? = null
)

// 错误信息模型
data class ApiError(
    @SerializedName("code")
    val code: String,
    @SerializedName("message")
    val message: String,
    @SerializedName("details")
    val details: Map<String, Any>? = null
)

// 用户注册请求
data class RegisterRequest(
    @SerializedName("email")
    val email: String,
    @SerializedName("password")
    val password: String,
    @SerializedName("name")
    val name: String
)

// 用户登录请求
data class LoginRequest(
    @SerializedName("email")
    val email: String,
    @SerializedName("password")
    val password: String
)

// 用户信息模型
data class User(
    @SerializedName("id")
    val id: String,
    @SerializedName("email")
    val email: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("role")
    val role: String,
    @SerializedName("is_active")
    val isActive: Boolean = true,
    @SerializedName("is_verified")
    val isVerified: Boolean = true,
    @SerializedName("total_llm_calls")
    val totalLlmCalls: Int = 0,
    @SerializedName("monthly_llm_calls")
    val monthlyLlmCalls: Int = 0,
    @SerializedName("created_at")
    val createdAt: String? = null,
    @SerializedName("updated_at")
    val updatedAt: String? = null,
    @SerializedName("last_login_at")
    val lastLoginAt: String? = null
)

// 登录响应数据
data class LoginData(
    @SerializedName("access_token")
    val accessToken: String,
    @SerializedName("token_type")
    val tokenType: String,
    @SerializedName("expires_in")
    val expiresIn: Long,
    @SerializedName("user")
    val user: User
)

// LLM聊天消息
data class LLMMessage(
    @SerializedName("role")
    val role: String, // "user" 或 "assistant"
    @SerializedName("content")
    val content: String
)

// LLM聊天请求
data class LLMChatRequest(
    @SerializedName("provider")
    val provider: String, // "deepseek" 或 "gemini"
    @SerializedName("messages")
    val messages: List<LLMMessage>,
    @SerializedName("stream")
    val stream: Boolean = false
)

// LLM聊天响应数据
data class LLMChatData(
    @SerializedName("content")
    val content: String,
    @SerializedName("usage")
    val usage: LLMUsage? = null
)

// LLM使用量信息
data class LLMUsage(
    @SerializedName("tokens_used")
    val tokensUsed: Int? = null,
    @SerializedName("tokens")
    val tokens: Int? = null,
    @SerializedName("cost")
    val cost: Double? = null
)

// 流式响应数据
data class StreamChunk(
    @SerializedName("type")
    val type: String, // "chunk" 或 "done"
    @SerializedName("content")
    val content: String? = null,
    @SerializedName("usage")
    val usage: LLMUsage? = null
)

// API密钥创建请求
data class CreateApiKeyRequest(
    @SerializedName("name")
    val name: String,
    @SerializedName("permissions")
    val permissions: List<String>
)

// API密钥信息
data class ApiKeyInfo(
    @SerializedName("id")
    val id: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("key")
    val key: String? = null, // 只在创建时返回完整密钥
    @SerializedName("key_prefix")
    val keyPrefix: String? = null,
    @SerializedName("permissions")
    val permissions: List<String>,
    @SerializedName("is_active")
    val isActive: Boolean = true,
    @SerializedName("last_used_at")
    val lastUsedAt: String? = null,
    @SerializedName("created_at")
    val createdAt: String
)

// 使用量统计
data class UsageStats(
    @SerializedName("period")
    val period: String,
    @SerializedName("llm_calls")
    val llmCalls: LLMCallStats,
    @SerializedName("tokens_used")
    val tokensUsed: Int,
    @SerializedName("cost")
    val cost: Double
)

// LLM调用统计
data class LLMCallStats(
    @SerializedName("total")
    val total: Int,
    @SerializedName("deepseek")
    val deepseek: Int,
    @SerializedName("gemini")
    val gemini: Int
)

// 服务器健康检查响应
data class HealthResponse(
    @SerializedName("status")
    val status: String,
    @SerializedName("timestamp")
    val timestamp: String,
    @SerializedName("version")
    val version: String? = null
)
