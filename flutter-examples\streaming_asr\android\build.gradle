allprojects {
    repositories {
        //引用本地maven仓库
        mavenLocal()
        //阿里云maven镜像
        maven { url 'https://maven.aliyun.com/repository/public' }
        //阿里云maven镜像，google maven镜像
        maven { url 'https://maven.aliyun.com/repository/google' }
        //阿里云maven镜像，gradle plugin 镜像
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        //华为云maven镜像
        maven { url 'https://mirrors.huaweicloud.com/repository/maven'}

        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
