package com.vectora.vocalmind

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 系统启动广播接收器
 * 用于在系统重启后重新调度所有活跃的TODO提醒
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到系统广播: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                rescheduleAllReminders(context)
            }
        }
    }
    
    /**
     * 重新调度所有提醒
     */
    private fun rescheduleAllReminders(context: Context) {
        Log.i(TAG, "开始重新调度所有TODO提醒")
        
        // 使用协程处理异步操作
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val reminderManager = TodoReminderManager.getInstance(context)
                reminderManager.rescheduleAllReminders()
                
                Log.i(TAG, "TODO提醒重新调度完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "重新调度TODO提醒失败", e)
            }
        }
    }
}
