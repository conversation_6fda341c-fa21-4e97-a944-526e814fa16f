<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <!-- TODO提醒相关权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- 忽略电池优化权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <!-- 设备管理员权限（可选，用于确保提醒不被杀死） -->
    <uses-permission android:name="android.permission.DEVICE_ADMIN" />
    <!-- 在锁屏上显示通知 -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <!-- 音频权限确保响铃 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.VocalMindAI"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <!-- 欢迎页面 - 应用启动入口 -->
        <activity
            android:name=".WelcomeActivity"
            android:exported="true"
            android:label="VocalMind AI"
            android:theme="@style/Theme.VocalMindAI">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主应用页面 -->
        <activity
            android:name=".SingleModelActivity"
            android:exported="false"
            android:label="VocalMind AI"
            android:theme="@style/Theme.VocalMindAI.Optimized" />

        <!-- LLM设置页面 -->
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="LLM 设置"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>

        <!-- 会议记录列表页面 -->
        <activity
            android:name=".MeetingRecordsActivity"
            android:exported="false"
            android:label="会议记录"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>

        <!-- 会议详情页面 -->
        <activity
            android:name=".MeetingDetailActivity"
            android:exported="false"
            android:label="会议详情"
            android:parentActivityName=".MeetingRecordsActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MeetingRecordsActivity" />
        </activity>

        <!-- AI聊天页面 -->
        <activity
            android:name=".AiChatActivity"
            android:exported="false"
            android:label="AI问答"
            android:parentActivityName=".MeetingDetailActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MeetingDetailActivity" />
        </activity>

        <!-- 服务器设置页面 -->
        <activity
            android:name=".ServerSettingsActivity"
            android:exported="false"
            android:label="服务器设置"
            android:parentActivityName=".SettingsActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SettingsActivity" />
        </activity>

        <!-- SaaS标准认证流程页面 -->
        <!-- 欢迎页面 -->
<!--        <activity-->
<!--            android:name=".WelcomeActivity"-->
<!--            android:exported="false"-->
<!--            android:label="欢迎"-->
<!--            android:theme="@style/Theme.VocalMindAI" />-->

        <!-- 登录页面 -->
        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:label="登录"
            android:parentActivityName=".WelcomeActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".WelcomeActivity" />
        </activity>

        <!-- 注册页面 -->
        <activity
            android:name=".RegisterActivity"
            android:exported="false"
            android:label="注册"
            android:parentActivityName=".WelcomeActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".WelcomeActivity" />
        </activity>

        <!-- 忘记密码页面 -->
        <activity
            android:name=".ForgotPasswordActivity"
            android:exported="false"
            android:label="忘记密码"
            android:parentActivityName=".LoginActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".LoginActivity" />
        </activity>

        <!-- TODO列表页面 -->
        <activity
            android:name=".TodoActivity"
            android:exported="false"
            android:label="AI待办"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>

        <!-- TODO编辑页面 -->
        <activity
            android:name=".TodoEditActivity"
            android:exported="false"
            android:label="编辑待办事项"
            android:parentActivityName=".TodoActivity"
            android:theme="@style/Theme.VocalMindAI">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".TodoActivity" />
        </activity>

        <!-- 停止提醒响铃的透明Activity -->
        <activity
            android:name=".StopReminderActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:launchMode="singleTop"
            android:excludeFromRecents="true"
            android:noHistory="true" />

        <!-- 音频录制前台服务 -->
        <service
            android:name=".AudioRecordingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- 悬浮窗服务 -->
        <service
            android:name=".FloatingWindowService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" >
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="floating_window" />
        </service>

        <!-- TODO提醒广播接收器 -->
        <receiver
            android:name=".TodoReminderReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.vectora.vocalmind.TODO_REMINDER" />
                <action android:name="com.vectora.vocalmind.STOP_REMINDER" />
            </intent-filter>
        </receiver>

        <!-- 系统启动广播接收器（用于重新调度提醒） -->
        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
