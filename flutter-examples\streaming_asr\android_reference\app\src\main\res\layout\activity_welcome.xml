<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/welcome_background"
    tools:context=".WelcomeActivity">

    <!-- 顶部Logo区域 -->
    <LinearLayout
        android:id="@+id/layout_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingTop="32dp"
        android:paddingBottom="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- App Logo -->
        <ImageView
            android:id="@+id/iv_app_logo"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_app_logo"
            android:layout_marginBottom="12dp"
            android:contentDescription="App Logo" />

        <!-- App Name -->
        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="VocalMind"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text"
            android:layout_marginBottom="8dp" />

        <!-- App Tagline -->
        <TextView
            android:id="@+id/tv_app_tagline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="智能语音识别助手"
            android:textSize="16sp"
            android:textColor="@color/secondary_text"
            android:alpha="0.8" />

    </LinearLayout>

    <!-- 中间内容区域 - ViewPager -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewpager_welcome"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="24dp"
        app:layout_constraintTop_toBottomOf="@id/layout_header"
        app:layout_constraintBottom_toTopOf="@id/tablayout_indicators"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 页面指示器 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tablayout_indicators"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginBottom="32dp"
        app:tabBackground="@drawable/tab_indicator_selector"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMaxWidth="24dp"
        app:tabMinWidth="24dp"
        app:tabPaddingStart="8dp"
        app:tabPaddingEnd="8dp"
        app:layout_constraintBottom_toTopOf="@id/layout_buttons"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 底部按钮区域 -->
    <LinearLayout
        android:id="@+id/layout_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="32dp"
        android:paddingBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 登录按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="登录"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:backgroundTint="@color/primary_color"
            app:cornerRadius="28dp"
            app:elevation="4dp"
            android:layout_marginBottom="12dp"
            style="@style/Widget.MaterialComponents.Button" />

        <!-- 注册按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_register"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="注册"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary_color"
            android:backgroundTint="@color/white"
            app:cornerRadius="28dp"
            app:strokeColor="@color/primary_color"
            app:strokeWidth="2dp"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <!-- 跳过按钮 -->
        <TextView
            android:id="@+id/btn_skip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="暂时跳过"
            android:textSize="14sp"
            android:textColor="@color/secondary_text"
            android:padding="12dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
