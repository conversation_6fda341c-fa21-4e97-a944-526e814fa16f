<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingBottom="0dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/btn_settings"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="设置"
            android:src="@drawable/ic_settings_apple" />

    </LinearLayout>

    <!-- Recording Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:gravity="center"
        android:orientation="vertical">

        <!-- Recording Button Container -->
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:clipChildren="false"
            android:clipToPadding="false">

            <!-- Main Recording Button -->
            <ImageButton
                android:id="@+id/btn_record"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:background="@color/transparent"
                android:scaleType="fitXY"
                android:src="@drawable/record_button_idle" />

        </FrameLayout>

        <!-- Recording Status Text -->
        <TextView
            android:id="@+id/tv_recording_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="点击开始录音"
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
            android:textColor="@color/apple_secondary_label" />

        <!-- Floating Window Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_floating_window"
            style="@style/Widget.VoiceAssistant.Button.Outlined"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:text="悬浮窗"
            android:textSize="14sp"
            app:icon="@drawable/ic_mic"
            app:iconGravity="start"
            app:iconSize="18dp" />

        <!-- Import Audio File Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_import_audio"
            style="@style/Widget.VoiceAssistant.Button.Outlined"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:text="导入文件"
            android:textSize="14sp"
            app:icon="@drawable/ic_file_import"
            app:iconGravity="start"
            app:iconSize="18dp" />

    </LinearLayout>

    <!-- Transcription Results Section -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_results"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        app:cardBackgroundColor="@color/apple_system_background"
        app:cardCornerRadius="20dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <!-- Results Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="20dp"
                android:paddingBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="转录结果"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                <TextView
                    android:id="@+id/tv_word_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/count_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="4dp"
                    android:text="0 字"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption" />

            </LinearLayout>

            <!-- Results Content -->
            <ScrollView
                android:id="@+id/scroll_results"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadeScrollbars="false"
                android:fillViewport="false"
                android:paddingHorizontal="20dp"
                android:scrollbars="vertical">

                <TextView
                    android:id="@+id/tv_results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:hint="转录结果将在这里显示..."
                    android:lineSpacingExtra="6dp"
                    android:minHeight="120dp"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textIsSelectable="true" />

            </ScrollView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Action Buttons -->
    <LinearLayout
        android:id="@+id/ll_actions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_summary"
            style="@style/Widget.VoiceAssistant.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:text="💡 智能总结" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_todo"
            style="@style/Widget.VoiceAssistant.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="4dp"
            android:layout_weight="1"
            android:text="📝 TODO" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_ai_chat"
            style="@style/Widget.VoiceAssistant.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_weight="1"
            android:text="🤖 AI聊天" />

    </LinearLayout>

</LinearLayout>
