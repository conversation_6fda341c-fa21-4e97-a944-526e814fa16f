package com.vectora.vocalmind

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 录音Fragment
 * 从SingleModelActivity迁移录音相关功能
 */
class RecordingFragment : Fragment() {

    companion object {
        private const val TAG = "RecordingFragment"
        
        fun newInstance(): RecordingFragment {
            return RecordingFragment()
        }
    }

    // UI组件
    private lateinit var btnRecord: ImageButton
    private lateinit var btnSettings: ImageButton
    private lateinit var btnSummary: MaterialButton
    private lateinit var btnTodo: MaterialButton
    private lateinit var btnAiChat: MaterialButton
    private lateinit var btnImportAudio: MaterialButton
    private lateinit var btnFloatingWindow: MaterialButton
    private lateinit var tvResults: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvRecordingStatus: TextView
    private lateinit var llActions: LinearLayout

    // 录音相关状态
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private var recognitionCount = 0
    private var sessionStartTime = 0L
    private val timeHandler = Handler(Looper.getMainLooper())
    private var timeUpdateRunnable: Runnable? = null

    // 当前会话的会议记录ID
    private var currentMeetingRecordId: String? = null
    private var currentAudioFilePath: String? = null

    // 实时预览状态管理
    private var isShowingPreview = false
    private var currentPreviewText = ""
    private var baseResultsText = ""
    private var previewTimestamp = ""

    // 录音按钮动画相关
    private var recordButtonAnimator: android.animation.AnimatorSet? = null

    // 文件选择器
    private val audioFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { handleSelectedAudioFile(it) }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_recording, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupClickListeners()
        updateUI()
    }

    private fun initViews(view: View) {
        btnRecord = view.findViewById(R.id.btn_record)
        btnSettings = view.findViewById(R.id.btn_settings)
        btnSummary = view.findViewById(R.id.btn_summary)
        btnTodo = view.findViewById(R.id.btn_todo)
        btnAiChat = view.findViewById(R.id.btn_ai_chat)
        btnImportAudio = view.findViewById(R.id.btn_import_audio)
        btnFloatingWindow = view.findViewById(R.id.btn_floating_window)
        tvResults = view.findViewById(R.id.tv_results)
        tvWordCount = view.findViewById(R.id.tv_word_count)
        tvRecordingStatus = view.findViewById(R.id.tv_recording_status)
        llActions = view.findViewById(R.id.ll_actions)

        // 设置初始状态
        tvRecordingStatus.text = "点击开始录音"
        tvResults.hint = "转录结果将在这里显示..."
        tvWordCount.text = "0 字"
        llActions.visibility = View.GONE
    }

    private fun setupClickListeners() {
        btnRecord.setOnClickListener { toggleRecording() }
        btnSettings.setOnClickListener { openSettings() }
        btnSummary.setOnClickListener { generateMeetingSummary() }
        btnTodo.setOnClickListener { openTodoList() }
        btnTodo.setOnLongClickListener {
            generateTodoFromCurrentContent()
            true
        }
        btnAiChat.setOnClickListener { startAiChat() }
        btnImportAudio.setOnClickListener { importAudioFile() }
        btnFloatingWindow.setOnClickListener { toggleFloatingWindow() }
    }

    private fun toggleRecording() {
        // 通过Activity调用录音功能
        (activity as? SingleModelActivity)?.toggleRecording()
    }

    private fun generateMeetingSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以总结")
            return
        }

        // 确保会议记录已保存
        ensureMeetingRecordSaved()

        // 打开会议详情页面
        openMeetingDetailPage()
    }

    private fun openTodoList() {
        try {
            val intent = Intent(requireContext(), TodoActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开TODO列表页面失败", e)
            showToast("打开TODO列表页面失败: ${e.message}")
        }
    }

    private fun generateTodoFromCurrentContent() {
        if (recognitionResults.isEmpty()) {
            showToast("暂无转录内容，无法生成TODO")
            return
        }

        // 通过Activity调用TODO生成功能
        (activity as? SingleModelActivity)?.generateTodoFromCurrentContent()
    }

    private fun startAiChat() {
        try {
            if (recognitionResults.isEmpty()) {
                showToast("请先录音或导入音频文件获取转录内容")
                return
            }

            val intent = Intent(requireContext(), AiChatActivity::class.java)
            
            if (currentMeetingRecordId != null) {
                intent.putExtra("meeting_record_id", currentMeetingRecordId)
            }
            
            val currentTime = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())
            intent.putExtra("meeting_title", "语音转录 - $currentTime")
            intent.putExtra("meeting_content", recognitionResults.toString())
            
            startActivity(intent)
            
        } catch (e: Exception) {
            Log.e(TAG, "启动AI聊天页面失败", e)
            showToast("启动AI聊天页面失败: ${e.message}")
        }
    }

    private fun importAudioFile() {
        try {
            AppleInfoDialog(
                context = requireContext(),
                title = "📁 导入音频文件",
                message = "支持的音频格式：\n• WAV (推荐)\n• MP3 (常用格式)\n• M4A / AAC\n\n注意：\n• 文件大小限制：100MB以内\n• 非WAV格式将自动转换\n• 转录包含完整的声纹识别功能\n• 结果将自动保存到会议记录",
                positiveButtonText = "选择音频文件",
                negativeButtonText = "取消",
                onPositiveClick = {
                    launchAudioFilePicker()
                }
            ).show()

        } catch (e: Exception) {
            Log.e(TAG, "导入音频文件失败", e)
            showToast("导入音频文件失败: ${e.message}")
        }
    }

    private fun launchAudioFilePicker() {
        try {
            audioFilePickerLauncher.launch("audio/*")
        } catch (e: Exception) {
            Log.e(TAG, "启动文件选择器失败", e)
            showToast("启动文件选择器失败: ${e.message}")
        }
    }

    private fun handleSelectedAudioFile(uri: Uri) {
        // 通过Activity处理音频文件
        (activity as? SingleModelActivity)?.handleSelectedAudioFile(uri)
    }

    private fun toggleFloatingWindow() {
        // 通过Activity调用悬浮窗功能
        (activity as? SingleModelActivity)?.toggleFloatingWindow()
    }

    private fun ensureMeetingRecordSaved() {
        // 通过Activity确保会议记录已保存
        (activity as? SingleModelActivity)?.ensureMeetingRecordSaved()
    }

    private fun openMeetingDetailPage() {
        // 通过Activity打开会议详情页面
        (activity as? SingleModelActivity)?.openMeetingDetailPage()
    }

    // 公共方法供Activity调用以更新UI状态
    fun updateRecordingState(isRecording: Boolean) {
        if (isRecording) {
            btnRecord.setImageResource(R.drawable.record_button_recording)
            startRecordingAnimation()
            tvRecordingStatus.text = "正在录音，请说话..."
            llActions.visibility = View.GONE

            // 开始录音时重置预览状态 - 确保新会话从干净状态开始
            isShowingPreview = false
            currentPreviewText = ""
            previewTimestamp = ""
        } else {
            btnRecord.setImageResource(R.drawable.record_button_idle)
            stopRecordingAnimation()
            tvRecordingStatus.text = if (recognitionResults.isNotEmpty()) "录音完成" else "点击开始录音"
            if (recognitionResults.isNotEmpty()) {
                llActions.visibility = View.VISIBLE
            }
        }
        updateUI()
    }

    fun updateResults(results: String) {
        recognitionResults.clear()
        recognitionResults.append(results)
        baseResultsText = results
        tvResults.text = results

        // 重置预览状态 - 修复实时预览叠加问题
        isShowingPreview = false
        currentPreviewText = ""
        previewTimestamp = ""

        wordCount = results.length
        updateStatistics()
    }

    fun showPreview(text: String) {
        if (text.isBlank()) return

        if (!isShowingPreview) {
            previewTimestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            currentPreviewText = text
            isShowingPreview = true
        } else {
            currentPreviewText += text
        }

        val previewLine = "[$previewTimestamp] $currentPreviewText"
        val displayText = if (baseResultsText.isNotEmpty()) {
            "$baseResultsText$previewLine"
        } else {
            previewLine
        }

        tvResults.text = displayText
    }

    private fun updateUI() {
        val isRecording = (activity as? SingleModelActivity)?.isRecording() ?: false
        
        btnSummary.isEnabled = !isRecording && recognitionResults.isNotEmpty()
        btnTodo.isEnabled = !isRecording
        btnAiChat.isEnabled = !isRecording && recognitionResults.isNotEmpty()
    }

    private fun updateStatistics() {
        tvWordCount.text = "$wordCount 字"
    }

    private fun startRecordingAnimation() {
        stopRecordingAnimation()
        
        val scaleXAnimator = android.animation.ObjectAnimator.ofFloat(btnRecord, "scaleX", 1.0f, 1.1f, 1.0f)
        val scaleYAnimator = android.animation.ObjectAnimator.ofFloat(btnRecord, "scaleY", 1.0f, 1.1f, 1.0f)
        val rotationAnimator = android.animation.ObjectAnimator.ofFloat(btnRecord, "rotation", 0f, 360f)
        
        scaleXAnimator.duration = 1500
        scaleYAnimator.duration = 1500
        rotationAnimator.duration = 3000
        
        scaleXAnimator.repeatCount = android.animation.ValueAnimator.INFINITE
        scaleYAnimator.repeatCount = android.animation.ValueAnimator.INFINITE
        rotationAnimator.repeatCount = android.animation.ValueAnimator.INFINITE
        
        recordButtonAnimator = android.animation.AnimatorSet()
        recordButtonAnimator?.playTogether(scaleXAnimator, scaleYAnimator, rotationAnimator)
        recordButtonAnimator?.start()
    }
    
    private fun stopRecordingAnimation() {
        recordButtonAnimator?.cancel()
        recordButtonAnimator = null
        
        btnRecord.scaleX = 1.0f
        btnRecord.scaleY = 1.0f
        btnRecord.rotation = 0f
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopRecordingAnimation()
        timeUpdateRunnable?.let { timeHandler.removeCallbacks(it) }
    }

    private fun openSettings() {
        (activity as? SingleModelActivity)?.openSettings()
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
}
