package com.vectora.vocalmind

import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import io.noties.markwon.Markwon
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
import io.noties.markwon.ext.tables.TablePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin

/**
 * 苹果风格的会议总结弹窗
 */
class AppleMeetingSummaryDialog(
    context: Context,
    private val summary: String,
    private val originalContent: String
) : Dialog(context) {

    companion object {
        private const val TAG = "AppleMeetingSummaryDialog"
    }

    private lateinit var tvSummaryContent: TextView
    private lateinit var btnCopySummary: Button
    private lateinit var btnCopyOriginal: Button
    private lateinit var btnClose: ImageView
    private lateinit var scrollViewContent: ScrollView
    private lateinit var markwon: Markwon

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置无标题栏
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        
        // 加载布局
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_meeting_summary_apple, null)
        setContentView(view)
        
        // 设置窗口属性
        setupWindow()
        
        // 初始化Markdown渲染器
        initMarkwon()
        
        // 初始化视图
        initViews(view)
        
        // 设置数据
        setupData()
        
        // 设置点击事件
        setupClickListeners()
    }
    
    private fun setupWindow() {
        window?.let { window ->
            // 设置背景透明
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            
            // 设置窗口属性
            val layoutParams = window.attributes
            val displayMetrics = context.resources.displayMetrics
            
            layoutParams.width = (displayMetrics.widthPixels * 0.85).toInt()
            // 让Dialog高度自适应内容，通过ScrollView的maxHeight来控制最大高度
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            
            // 设置窗口动画
            window.setWindowAnimations(android.R.style.Animation_Dialog)
            
            // 设置窗口标志
            window.setFlags(
                WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                WindowManager.LayoutParams.FLAG_DIM_BEHIND
            )
            
            // 设置背景暗度
            layoutParams.dimAmount = 0.5f
            
            window.attributes = layoutParams
            
            // 设置最大高度
            window.setLayout(layoutParams.width, WindowManager.LayoutParams.WRAP_CONTENT)
        }
    }
    
    private fun initViews(view: View) {
        tvSummaryContent = view.findViewById(R.id.tv_summary_content)
        btnCopySummary = view.findViewById(R.id.btn_copy_summary)
        btnCopyOriginal = view.findViewById(R.id.btn_copy_original)
        btnClose = view.findViewById(R.id.btn_close)
        scrollViewContent = view.findViewById(R.id.scroll_view_content)
    }
    
    private fun initMarkwon() {
        markwon = Markwon.builder(context)
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(TablePlugin.create(context))
            .usePlugin(TaskListPlugin.create(context))
            .build()
    }
    
    /**
     * 设置TextView的Markdown内容
     */
    private fun setMarkdownText(textView: TextView, markdownText: String?) {
        val safeText = markdownText ?: ""
        if (safeText.trim().isEmpty()) {
            textView.text = safeText
            return
        }
        
        try {
            markwon.setMarkdown(textView, safeText)
        } catch (e: Exception) {
            Log.w(TAG, "Markdown渲染失败，使用原始文本: ${e.message}")
            textView.text = safeText
        }
    }
    
    private fun setupData() {
        // 动态设置ScrollView的最大高度
        setupScrollViewMaxHeight()
        
        // 设置总结内容
        setMarkdownText(tvSummaryContent, summary)
    }
    
    private fun setupScrollViewMaxHeight() {
        val displayMetrics = context.resources.displayMetrics
        
        // 计算可用窗口高度，考虑状态栏等系统UI
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val windowMetrics = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            windowManager.currentWindowMetrics
        } else {
            null
        }
        
        val availableHeight = if (windowMetrics != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            windowMetrics.bounds.height()
        } else {
            displayMetrics.heightPixels
        }
        
        // 设置ScrollView最大高度为可用高度的60%（为标题栏和按钮预留空间）
        val maxScrollHeight = (availableHeight * 0.5).toInt()
        
        // 动态设置ScrollView的最大高度
        val layoutParams = scrollViewContent.layoutParams
        if (layoutParams is ViewGroup.LayoutParams) {
            // 创建新的LayoutParams来设置最大高度
            val newLayoutParams = LinearLayout.LayoutParams(
                layoutParams.width,
                Math.min(maxScrollHeight, ViewGroup.LayoutParams.WRAP_CONTENT)
            )
            // 如果内容高度超过最大高度，则设置为最大高度，否则使用WRAP_CONTENT
            newLayoutParams.height = maxScrollHeight
            
            // 应用新的LayoutParams
            scrollViewContent.layoutParams = newLayoutParams
        }
    }
    
    private fun setupClickListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dismiss()
        }
        
        // 复制总结按钮
        btnCopySummary.setOnClickListener {
            copyToClipboard("会议总结", summary)
            showToast("会议总结已复制到剪贴板")
        }
        
        // 复制原文按钮
        btnCopyOriginal.setOnClickListener {
            copyToClipboard("会议原文", originalContent)
            showToast("会议原文已复制到剪贴板")
        }
        
        // 点击外部区域关闭弹窗
        setCanceledOnTouchOutside(true)
    }
    
    /**
     * 复制文本到剪贴板
     */
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }
    
    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
}