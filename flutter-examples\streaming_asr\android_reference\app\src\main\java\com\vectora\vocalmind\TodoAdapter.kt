package com.vectora.vocalmind

import android.graphics.Color
import android.graphics.Paint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import java.util.*

/**
 * TODO列表适配器
 * 支持拖拽排序、完成状态切换等功能
 */
class TodoAdapter(
    private val onItemClick: (TodoItem) -> Unit,
    private val onItemToggle: (TodoItem) -> Unit,
    private val onItemDelete: (TodoItem) -> Unit
) : RecyclerView.Adapter<TodoAdapter.TodoViewHolder>() {
    
    private var todos = mutableListOf<TodoItem>()
    
    fun updateTodos(newTodos: List<TodoItem>) {
        todos.clear()
        todos.addAll(newTodos)
        notifyDataSetChanged()
    }
    
    fun getTodos(): List<TodoItem> = todos.toList()
    
    fun moveItem(fromPosition: Int, toPosition: Int) {
        if (fromPosition < toPosition) {
            for (i in fromPosition until toPosition) {
                Collections.swap(todos, i, i + 1)
            }
        } else {
            for (i in fromPosition downTo toPosition + 1) {
                Collections.swap(todos, i, i - 1)
            }
        }
        notifyItemMoved(fromPosition, toPosition)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TodoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_todo, parent, false)
        return TodoViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: TodoViewHolder, position: Int) {
        holder.bind(todos[position])
    }
    
    override fun getItemCount(): Int = todos.size
    
    inner class TodoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: CardView = itemView.findViewById(R.id.card_todo)
        private val checkBox: CheckBox = itemView.findViewById(R.id.cb_completed)
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_title)
        private val tvDescription: TextView = itemView.findViewById(R.id.tv_description)
        private val tvCategory: TextView = itemView.findViewById(R.id.tv_category)
        private val tvPriority: TextView = itemView.findViewById(R.id.tv_priority)
        private val tvDueDate: TextView = itemView.findViewById(R.id.tv_due_date)
        private val tvCreatedAt: TextView = itemView.findViewById(R.id.tv_created_at)
        private val btnDelete: ImageButton = itemView.findViewById(R.id.btn_delete)
        private val layoutDueDate: LinearLayout = itemView.findViewById(R.id.layout_due_date)
        private val ivDragHandle: ImageView = itemView.findViewById(R.id.iv_drag_handle)
        
        fun bind(todoItem: TodoItem) {
            // 设置基本信息
            tvTitle.text = todoItem.title
            tvDescription.text = todoItem.description
            tvCategory.text = todoItem.category
            tvCreatedAt.text = todoItem.getFormattedCreatedAt()
            
            // 设置完成状态
            checkBox.isChecked = todoItem.isCompleted
            updateCompletionStyle(todoItem.isCompleted)
            
            // 设置优先级
            tvPriority.text = todoItem.priority.displayName
            tvPriority.setBackgroundColor(Color.parseColor(todoItem.priority.color))
            tvPriority.setTextColor(Color.WHITE)
            
            // 设置截止时间
            if (todoItem.dueDate != null) {
                layoutDueDate.visibility = View.VISIBLE
                tvDueDate.text = todoItem.getFormattedDueDate()
                
                // 根据截止时间状态设置颜色
                when {
                    todoItem.isOverdue() -> {
                        tvDueDate.setTextColor(Color.parseColor("#dc3545"))
                        tvDueDate.text = "⚠️ ${todoItem.getFormattedDueDate()}"
                    }
                    todoItem.isDueSoon() -> {
                        tvDueDate.setTextColor(Color.parseColor("#fd7e14"))
                        tvDueDate.text = "🔔 ${todoItem.getFormattedDueDate()}"
                    }
                    else -> {
                        tvDueDate.setTextColor(Color.parseColor("#6c757d"))
                        tvDueDate.text = "📅 ${todoItem.getFormattedDueDate()}"
                    }
                }
            } else {
                layoutDueDate.visibility = View.GONE
            }
            
            // 设置描述可见性
            if (todoItem.description.isNotEmpty()) {
                tvDescription.visibility = View.VISIBLE
            } else {
                tvDescription.visibility = View.GONE
            }
            
            // 设置点击事件
            cardView.setOnClickListener {
                onItemClick(todoItem)
            }
            
            checkBox.setOnClickListener {
                onItemToggle(todoItem)
            }
            
            btnDelete.setOnClickListener {
                onItemDelete(todoItem)
            }
            
            // 设置卡片样式
            updateCardStyle(todoItem)
        }
        
        private fun updateCompletionStyle(isCompleted: Boolean) {
            if (isCompleted) {
                tvTitle.paintFlags = tvTitle.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                tvTitle.alpha = 0.6f
                tvDescription.alpha = 0.6f
                tvCategory.alpha = 0.6f
                tvCreatedAt.alpha = 0.6f
                cardView.alpha = 0.8f
            } else {
                tvTitle.paintFlags = tvTitle.paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()
                tvTitle.alpha = 1.0f
                tvDescription.alpha = 1.0f
                tvCategory.alpha = 1.0f
                tvCreatedAt.alpha = 1.0f
                cardView.alpha = 1.0f
            }
        }
        
        private fun updateCardStyle(todoItem: TodoItem) {
            // 根据优先级和状态设置卡片边框颜色
            val borderColor = when {
                todoItem.isCompleted -> Color.parseColor("#28a745")
                todoItem.isOverdue() -> Color.parseColor("#dc3545")
                todoItem.isDueSoon() -> Color.parseColor("#fd7e14")
                todoItem.priority == TodoItem.Priority.URGENT -> Color.parseColor("#dc3545")
                todoItem.priority == TodoItem.Priority.HIGH -> Color.parseColor("#fd7e14")
                else -> Color.parseColor("#e9ecef")
            }
            
            // 设置卡片边框（这里需要在布局中定义相应的drawable）
            cardView.setCardBackgroundColor(Color.WHITE)
            
            // 设置卡片阴影
            cardView.cardElevation = if (todoItem.isCompleted) 2f else 4f
        }
    }
}

/**
 * 拖拽排序回调类
 */
class TodoItemTouchHelperCallback(
    private val adapter: TodoAdapter,
    private val onItemMoved: (List<TodoItem>) -> Unit
) : ItemTouchHelper.Callback() {
    
    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }
    
    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val fromPosition = viewHolder.adapterPosition
        val toPosition = target.adapterPosition
        
        adapter.moveItem(fromPosition, toPosition)
        return true
    }
    
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // 不支持滑动删除
    }
    
    override fun isLongPressDragEnabled(): Boolean = true
    
    override fun isItemViewSwipeEnabled(): Boolean = false
    
    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        super.onSelectedChanged(viewHolder, actionState)
        
        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
            viewHolder?.itemView?.alpha = 0.8f
            viewHolder?.itemView?.scaleX = 1.05f
            viewHolder?.itemView?.scaleY = 1.05f
        }
    }
    
    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        
        viewHolder.itemView.alpha = 1.0f
        viewHolder.itemView.scaleX = 1.0f
        viewHolder.itemView.scaleY = 1.0f
        
        // 通知排序变化
        onItemMoved(adapter.getTodos())
    }
}
