package com.vectora.vocalmind

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView

/**
 * 苹果风格的错误提示弹窗
 */
class AppleErrorDialog(
    context: Context,
    private val title: String,
    private val message: String,
    private val positiveButtonText: String = "确定",
    private val negativeButtonText: String? = null,
    private val onPositiveClick: (() -> Unit)? = null,
    private val onNegativeClick: (() -> Unit)? = null
) : Dialog(context) {

    private lateinit var tvTitle: TextView
    private lateinit var tvMessage: TextView
    private lateinit var btnPositive: Button
    private lateinit var btnNegative: Button
    private lateinit var btnClose: ImageView
    private lateinit var buttonContainer: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置无标题栏
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        
        // 加载布局
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_apple_error, null)
        setContentView(view)
        
        // 设置窗口属性
        setupWindow()
        
        // 初始化视图
        initViews(view)
        
        // 设置数据
        setupData()
        
        // 设置点击事件
        setupClickListeners()
    }
    
    private fun setupWindow() {
        window?.let { window ->
            // 设置背景透明
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            
            // 设置窗口属性
            val layoutParams = window.attributes
            layoutParams.width = (context.resources.displayMetrics.widthPixels * 0.85).toInt()
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            
            // 设置窗口动画
            window.setWindowAnimations(android.R.style.Animation_Dialog)
            
            // 设置窗口标志
            window.setFlags(
                WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                WindowManager.LayoutParams.FLAG_DIM_BEHIND
            )
            
            // 设置背景暗度
            layoutParams.dimAmount = 0.5f
            
            window.attributes = layoutParams
        }
    }
    
    private fun initViews(view: View) {
        tvTitle = view.findViewById(R.id.tv_title)
        tvMessage = view.findViewById(R.id.tv_message)
        btnPositive = view.findViewById(R.id.btn_positive)
        btnNegative = view.findViewById(R.id.btn_negative)
        btnClose = view.findViewById(R.id.btn_close)
        buttonContainer = view.findViewById(R.id.button_container)
    }
    
    private fun setupData() {
        tvTitle.text = title
        tvMessage.text = message
        btnPositive.text = positiveButtonText
        
        // 根据是否有负面按钮来调整布局
        if (negativeButtonText != null) {
            btnNegative.text = negativeButtonText
            btnNegative.visibility = View.VISIBLE
            // 显示分割线
            buttonContainer.findViewById<View>(R.id.button_divider)?.visibility = View.VISIBLE
        } else {
            btnNegative.visibility = View.GONE
            // 隐藏分割线
            buttonContainer.findViewById<View>(R.id.button_divider)?.visibility = View.GONE
        }
    }
    
    private fun setupClickListeners() {
        // 关闭按钮
        btnClose.setOnClickListener {
            dismiss()
        }
        
        // 确定按钮
        btnPositive.setOnClickListener {
            onPositiveClick?.invoke()
            dismiss()
        }
        
        // 取消按钮
        btnNegative.setOnClickListener {
            onNegativeClick?.invoke()
            dismiss()
        }
        
        // 点击外部区域关闭弹窗
        setCanceledOnTouchOutside(true)
    }
}