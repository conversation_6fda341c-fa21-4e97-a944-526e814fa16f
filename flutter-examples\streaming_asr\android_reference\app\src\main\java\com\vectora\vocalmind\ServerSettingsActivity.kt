package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.switchmaterial.SwitchMaterial
import com.vectora.vocalmind.server.*
import kotlinx.coroutines.launch

/**
 * 服务器设置Activity
 * 管理服务器连接配置和模式切换
 */
class ServerSettingsActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ServerSettingsActivity"
    }

    // UI组件
    private lateinit var switchServerMode: SwitchMaterial
    private lateinit var editServerUrl: EditText
    private lateinit var btnTestConnection: Button
    private lateinit var textConnectionStatus: TextView
    private lateinit var btnUserAuth: Button
    private lateinit var textUserInfo: TextView

    private lateinit var layoutServerConfig: LinearLayout

    private lateinit var apiService: ServerApiService
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_server_settings_new)
        
        initViews()
        initServices()
        loadSettings()
        updateUI()
    }
    
    private fun initViews() {
        // 服务器配置相关
        switchServerMode = findViewById(R.id.switch_server_mode)
        editServerUrl = findViewById(R.id.edit_server_url)
        btnTestConnection = findViewById(R.id.btn_test_connection)
        textConnectionStatus = findViewById(R.id.text_connection_status)
        layoutServerConfig = findViewById(R.id.layout_server_config)

        // 用户认证相关
        btnUserAuth = findViewById(R.id.btn_user_auth)
        textUserInfo = findViewById(R.id.text_user_info)

        // 设置事件监听器
        switchServerMode.setOnCheckedChangeListener { _, isChecked ->
            onServerModeChanged(isChecked)
        }

        btnTestConnection.setOnClickListener { testConnection() }
        btnUserAuth.setOnClickListener { openUserAuth() }

        editServerUrl.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                saveServerUrl()
            }
        }
    }
    
    private fun initServices() {
        apiService = ServerApiService.getInstance(this)
    }
    
    private fun loadSettings() {
        // 加载服务器模式设置
        val useServerMode = ServerConfigManager.isUseServerMode(this)
        switchServerMode.isChecked = useServerMode

        // 加载服务器URL
        val serverUrl = ServerConfigManager.getServerUrl(this)
        editServerUrl.setText(serverUrl)

        // 加载用户信息
        updateUserInfo()
    }
    
    private fun updateUI() {
        val useServerMode = switchServerMode.isChecked

        // 显示/隐藏服务器配置
        layoutServerConfig.visibility = if (useServerMode) View.VISIBLE else View.GONE

        // 更新用户认证按钮状态
        updateUserAuthButton(useServerMode)
        
        if (useServerMode) {
            // 更新登录状态相关UI
            val isLoggedIn = UserAuthManager.isLoggedIn(this)
            if (isLoggedIn) {
                // 这些UI组件在新的设计中已经移除，不需要处理
                // editEmail.isEnabled = false
                // editPassword.isEnabled = false
                // 已登录状态 - 在新设计中通过updateUserInfo()处理
                updateUserInfo()
            } else {
                // 未登录状态 - 在新设计中通过updateUserInfo()处理
                updateUserInfo()
            }
        }
    }
    
    private fun onServerModeChanged(useServerMode: Boolean) {
        Log.d(TAG, "服务器模式切换: $useServerMode")
        
        // 保存设置
        ServerConfigManager.setUseServerMode(this, useServerMode)
        
        // 更新UI
        updateUI()
        
        // 如果关闭服务器模式，清除认证信息
        if (!useServerMode) {
            UserAuthManager.logout(this)
        }
        
        showToast(if (useServerMode) "已启用服务器模式" else "已切换到隐私模式")
    }

    private fun updateUserAuthButton(useServerMode: Boolean) {
        if (useServerMode) {
            val isLoggedIn = UserAuthManager.isLoggedIn(this)
            btnUserAuth.visibility = View.VISIBLE
            btnUserAuth.text = if (isLoggedIn) "用户管理" else "登录/注册"
        } else {
            btnUserAuth.visibility = View.GONE
        }
    }

    private fun updateUserInfo() {
        val currentUser = UserAuthManager.getCurrentUser(this)
        if (currentUser != null) {
            textUserInfo.text = "当前用户: ${currentUser.name} (${currentUser.email})"
            textUserInfo.visibility = View.VISIBLE
        } else {
            textUserInfo.visibility = View.GONE
        }
    }

    private fun openUserAuth() {
        val isLoggedIn = UserAuthManager.isLoggedIn(this)
        if (isLoggedIn) {
            // 已登录，显示用户管理选项
            showUserManagementDialog()
        } else {
            // 未登录，跳转到欢迎页面
            startActivity(Intent(this, WelcomeActivity::class.java))
        }
    }

    private fun showUserManagementDialog() {
        val currentUser = UserAuthManager.getCurrentUser(this)
        if (currentUser == null) return

        val options = arrayOf("查看个人信息", "登出")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("用户管理")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> showUserProfile(currentUser)
                    1 -> logout()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showUserProfile(user: com.vectora.vocalmind.server.User) {
        val message = """
            姓名: ${user.name}
            邮箱: ${user.email}
            角色: ${user.role}
            状态: ${if (user.isActive) "活跃" else "非活跃"}
            LLM调用次数: ${user.totalLlmCalls}
            本月调用次数: ${user.monthlyLlmCalls}
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("个人信息")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun logout() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("确认登出")
            .setMessage("确定要登出当前账户吗？")
            .setPositiveButton("确定") { _, _ ->
                UserAuthManager.logout(this)
                updateUI()
                updateUserInfo()
                showToast("已登出")
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun saveServerUrl() {
        val url = editServerUrl.text.toString().trim()
        if (url.isNotEmpty()) {
            if (ServerConfigManager.isValidServerUrl(url)) {
                ServerConfigManager.setServerUrl(this, url)
                Log.d(TAG, "服务器URL已保存: $url")
            } else {
                showToast("无效的服务器URL格式")
                // 恢复原来的URL
                editServerUrl.setText(ServerConfigManager.getServerUrl(this))
            }
        }
    }
    
    private fun testConnection() {
        Log.d(TAG, "开始测试服务器连接")
        
        // 先保存URL
        saveServerUrl()
        
        btnTestConnection.isEnabled = false
        textConnectionStatus.text = "正在连接..."
        
        lifecycleScope.launch {
            try {
                val result = apiService.checkServerConnection()
                
                runOnUiThread {
                    if (result.isSuccess()) {
                        textConnectionStatus.text = "连接成功 ✓"
                        textConnectionStatus.setTextColor(getColor(android.R.color.holo_green_dark))
                        showToast("服务器连接成功")
                    } else {
                        textConnectionStatus.text = "连接失败 ✗"
                        textConnectionStatus.setTextColor(getColor(android.R.color.holo_red_dark))
                        showToast("连接失败: ${result.getErrorMessage()}")
                    }
                    btnTestConnection.isEnabled = true
                }
            } catch (e: Exception) {
                Log.e(TAG, "测试连接异常", e)
                runOnUiThread {
                    textConnectionStatus.text = "连接异常 ✗"
                    textConnectionStatus.setTextColor(getColor(android.R.color.holo_red_dark))
                    showToast("连接异常: ${e.message}")
                    btnTestConnection.isEnabled = true
                }
            }
        }
    }

    // private fun logout() {
    //     Log.d(TAG, "用户登出")
    //
    //     val success = apiService.logout()
    //     if (success) {
    //         showToast("已登出")
    //         editPassword.setText("")
    //         updateUI()
    //     } else {
    //         showToast("登出失败")
    //     }
    // }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
