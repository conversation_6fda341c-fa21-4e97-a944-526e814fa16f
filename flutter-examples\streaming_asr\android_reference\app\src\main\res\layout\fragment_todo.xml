<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:elevation="2dp"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Title Bar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📅 AI待办"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline"
                     />

                <ImageButton
                    android:id="@+id/btn_settings"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="设置"
                    android:src="@drawable/ic_settings_apple" />

            </LinearLayout>

            <!-- Statistics -->
            <TextView
                android:id="@+id/tv_statistics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="left|center"
                android:text="📊 统计信息加载中..."
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_blue" />

            <!-- Filter and Search -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <Spinner
                    android:id="@+id/spinner_filter"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/spinner_background" />

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_weight="2"
                    android:background="@drawable/search_background"
                    android:hint="搜索..."
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="12dp"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Content -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:padding="16dp"
                tools:listitem="@layout/item_todo" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="📝"
                    android:textSize="64sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    android:text="暂无待办事项"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Title"
                    android:textSize="18sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:gravity="center"
                    android:text="点击右下角的 + 按钮添加新的待办事项\n或者创建一些示例数据开始体验"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textColor="@color/apple_gray" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_create_sample"
                    style="@style/Widget.VoiceAssistant.Button.Outlined"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="创建示例数据"
                    app:icon="@drawable/ic_add"
                    app:iconGravity="start" />

            </LinearLayout>

            <!-- Floating Action Button -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="16dp"
                android:contentDescription="添加待办事项"
                android:src="@drawable/ic_add"
                app:backgroundTint="@color/apple_blue"
                app:tint="@color/white" />

        </FrameLayout>

</LinearLayout>
