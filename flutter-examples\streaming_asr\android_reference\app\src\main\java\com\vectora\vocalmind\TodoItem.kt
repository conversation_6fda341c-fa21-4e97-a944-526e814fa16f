package com.vectora.vocalmind

import java.text.SimpleDateFormat
import java.util.*

/**
 * TODO项数据类
 * 支持优先级、完成状态、创建时间等功能
 */
data class TodoItem(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val isCompleted: Boolean = false,
    val priority: Priority = Priority.MEDIUM,
    val category: String = "通用",
    val createdAt: Long = System.currentTimeMillis(),
    val completedAt: Long? = null,
    val dueDate: Long? = null,
    val reminderTime: Long? = null, // 提醒时间
    val meetingRecordId: String? = null, // 关联的会议记录ID
    val order: Int = 0 // 用于拖拽排序
) {
    
    /**
     * 优先级枚举
     */
    enum class Priority(val displayName: String, val color: String, val value: Int) {
        LOW("低", "#28a745", 1),
        MEDIUM("中", "#ffc107", 2),
        HIGH("高", "#fd7e14", 3),
        URGENT("紧急", "#dc3545", 4);
        
        companion object {
            fun fromValue(value: Int): Priority {
                return values().find { it.value == value } ?: MEDIUM
            }
        }
    }
    
    /**
     * 获取格式化的创建时间
     */
    fun getFormattedCreatedAt(): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(createdAt))
    }
    
    /**
     * 获取格式化的完成时间
     */
    fun getFormattedCompletedAt(): String? {
        return completedAt?.let { 
            SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(it))
        }
    }
    
    /**
     * 获取格式化的截止时间
     */
    fun getFormattedDueDate(): String? {
        return dueDate?.let {
            SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(it))
        }
    }

    /**
     * 获取格式化的提醒时间
     */
    fun getFormattedReminderTime(): String? {
        return reminderTime?.let {
            SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date(it))
        }
    }
    
    /**
     * 检查是否已过期
     */
    fun isOverdue(): Boolean {
        return dueDate?.let { it < System.currentTimeMillis() && !isCompleted } ?: false
    }
    
    /**
     * 检查是否即将到期（24小时内）
     */
    fun isDueSoon(): Boolean {
        return dueDate?.let {
            val now = System.currentTimeMillis()
            val twentyFourHours = 24 * 60 * 60 * 1000
            it > now && it <= now + twentyFourHours && !isCompleted
        } ?: false
    }

    /**
     * 检查是否有提醒时间且未过期
     */
    fun hasActiveReminder(): Boolean {
        return reminderTime?.let {
            it > System.currentTimeMillis() && !isCompleted
        } ?: false
    }

    /**
     * 检查提醒时间是否已过
     */
    fun isReminderOverdue(): Boolean {
        return reminderTime?.let {
            it <= System.currentTimeMillis() && !isCompleted
        } ?: false
    }
    
    /**
     * 创建副本并标记为完成
     */
    fun markAsCompleted(): TodoItem {
        return copy(
            isCompleted = true,
            completedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * 创建副本并标记为未完成
     */
    fun markAsIncomplete(): TodoItem {
        return copy(
            isCompleted = false,
            completedAt = null
        )
    }
    
    /**
     * 转换为JSON字符串（用于持久化）
     */
    fun toJson(): String {
        return """
        {
            "id": "$id",
            "title": "${title.replace("\"", "\\\"")}",
            "description": "${description.replace("\"", "\\\"")}",
            "isCompleted": $isCompleted,
            "priority": ${priority.value},
            "category": "${category.replace("\"", "\\\"")}",
            "createdAt": $createdAt,
            "completedAt": ${completedAt ?: "null"},
            "dueDate": ${dueDate ?: "null"},
            "reminderTime": ${reminderTime ?: "null"},
            "meetingRecordId": ${if (meetingRecordId != null) "\"$meetingRecordId\"" else "null"},
            "order": $order
        }
        """.trimIndent()
    }
    
    companion object {
        /**
         * 从JSON字符串创建TodoItem
         */
        fun fromJson(json: String): TodoItem? {
            return try {
                val jsonObj = org.json.JSONObject(json)
                TodoItem(
                    id = jsonObj.getString("id"),
                    title = jsonObj.getString("title"),
                    description = jsonObj.optString("description", ""),
                    isCompleted = jsonObj.getBoolean("isCompleted"),
                    priority = Priority.fromValue(jsonObj.getInt("priority")),
                    category = jsonObj.optString("category", "通用"),
                    createdAt = jsonObj.getLong("createdAt"),
                    completedAt = if (jsonObj.isNull("completedAt")) null else jsonObj.getLong("completedAt"),
                    dueDate = if (jsonObj.isNull("dueDate")) null else jsonObj.getLong("dueDate"),
                    reminderTime = if (jsonObj.isNull("reminderTime")) null else jsonObj.getLong("reminderTime"),
                    meetingRecordId = if (jsonObj.isNull("meetingRecordId")) null else jsonObj.getString("meetingRecordId"),
                    order = jsonObj.optInt("order", 0)
                )
            } catch (e: Exception) {
                null
            }
        }
        
        /**
         * 创建示例TODO项（用于测试）
         */
        fun createSample(): List<TodoItem> {
            return listOf(
                TodoItem(
                    title = "完成项目报告",
                    description = "整理本周的工作进展，准备下周的汇报材料",
                    priority = Priority.HIGH,
                    category = "工作",
                    dueDate = System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000 // 2天后
                ),
                TodoItem(
                    title = "购买生活用品",
                    description = "牛奶、面包、洗发水",
                    priority = Priority.MEDIUM,
                    category = "生活"
                ),
                TodoItem(
                    title = "预约体检",
                    description = "联系医院预约年度体检",
                    priority = Priority.LOW,
                    category = "健康"
                )
            )
        }
    }
}
