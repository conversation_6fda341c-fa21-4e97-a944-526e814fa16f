<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_todo"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:layout_marginHorizontal="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Drag Handle -->
        <ImageView
            android:id="@+id/iv_drag_handle"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:contentDescription="拖拽排序"
            android:src="@drawable/ic_drag_handle"
            android:alpha="0.5" />

        <!-- Checkbox -->
        <CheckBox
            android:id="@+id/cb_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginEnd="12dp"
            android:layout_marginTop="2dp" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title and Priority -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_priority"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/priority_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="中"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Description -->
            <TextView
                android:id="@+id/tv_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- Category and Created Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/category_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="通用"
                    android:textColor="@color/apple_blue"
                    android:textSize="12sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tv_created_at"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textColor="@color/text_tertiary"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Due Date -->
            <LinearLayout
                android:id="@+id/layout_due_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_due_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Delete Button -->
        <ImageButton
            android:id="@+id/btn_delete"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="删除"
            android:src="@drawable/ic_delete"
            android:tint="@color/apple_red"
            android:alpha="0.7" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
