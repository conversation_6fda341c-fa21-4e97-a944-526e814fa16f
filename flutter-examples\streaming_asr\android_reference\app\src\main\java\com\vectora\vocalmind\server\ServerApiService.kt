package com.vectora.vocalmind.server

import android.content.Context
import android.util.Log
import com.google.gson.reflect.TypeToken

/**
 * 服务器API服务类
 * 提供具体的API调用方法
 */
class ServerApiService private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ServerApiService"
        
        @Volatile
        private var INSTANCE: ServerApiService? = null
        
        fun getInstance(context: Context): ServerApiService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ServerApiService(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val apiClient = ServerApiClient.getInstance(context)
    
    // ==================== 认证相关API ====================
    
    /**
     * 用户注册
     */
    suspend fun register(email: String, password: String, name: String): ApiResult<User> {
        Log.d(TAG, "开始用户注册: $email")
        
        val request = RegisterRequest(email, password, name)
        val result = apiClient.post<ApiResponse<User>>("/auth/register", request, object : TypeToken<ApiResponse<User>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "用户注册成功: ${response.data.email}")
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "注册失败"
                    Log.e(TAG, "用户注册失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "用户注册请求失败: ${result.message}")
                result
            }
        }
    }
    
    /**
     * 用户登录
     */
    suspend fun login(email: String, password: String): ApiResult<LoginData> {
        Log.d(TAG, "开始用户登录: $email")

        val request = LoginRequest(email, password)
        val result = apiClient.post<ApiResponse<LoginData>>("/auth/login", request, object : TypeToken<ApiResponse<LoginData>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "用户登录成功: ${response.data.user.email}")
                    // 保存登录信息
                    UserAuthManager.saveLoginData(context, response.data)
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "登录失败"
                    Log.e(TAG, "用户登录失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "用户登录请求失败: ${result.message}")
                result
            }
        }
    }
    
    /**
     * 获取用户信息
     */
    suspend fun getUserProfile(): ApiResult<User> {
        Log.d(TAG, "获取用户信息")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val result = apiClient.get<ApiResponse<User>>("/users/profile", object : TypeToken<ApiResponse<User>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "获取用户信息成功: ${response.data.email}")
                    // 更新本地用户信息
                    UserAuthManager.updateUserInfo(context, response.data)
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "获取用户信息失败"
                    Log.e(TAG, "获取用户信息失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "获取用户信息请求失败: ${result.message}")
                result
            }
        }
    }
    
    // ==================== LLM相关API ====================
    
    /**
     * LLM聊天（非流式）
     */
    suspend fun chatWithLLM(provider: String, messages: List<LLMMessage>): ApiResult<LLMChatData> {
        Log.d(TAG, "开始LLM聊天: provider=$provider, messages=${messages.size}")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val request = LLMChatRequest(provider, messages, false)
        val result = apiClient.post<ApiResponse<LLMChatData>>("/llm/chat", request, object : TypeToken<ApiResponse<LLMChatData>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "LLM聊天成功，响应长度: ${response.data.content.length}")
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "LLM聊天失败"
                    Log.e(TAG, "LLM聊天失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "LLM聊天请求失败: ${result.message}")
                result
            }
        }
    }
    
    // ==================== API密钥管理API ====================
    
    /**
     * 创建API密钥
     */
    suspend fun createApiKey(name: String, permissions: List<String>): ApiResult<ApiKeyInfo> {
        Log.d(TAG, "创建API密钥: $name")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val request = CreateApiKeyRequest(name, permissions)
        val result = apiClient.post<ApiResponse<ApiKeyInfo>>("/api-keys", request, object : TypeToken<ApiResponse<ApiKeyInfo>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "API密钥创建成功: ${response.data.name}")
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "创建API密钥失败"
                    Log.e(TAG, "创建API密钥失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "创建API密钥请求失败: ${result.message}")
                result
            }
        }
    }
    
    /**
     * 获取API密钥列表
     */
    suspend fun getApiKeys(): ApiResult<List<ApiKeyInfo>> {
        Log.d(TAG, "获取API密钥列表")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val result = apiClient.get<ApiResponse<List<ApiKeyInfo>>>("/api-keys", object : TypeToken<ApiResponse<List<ApiKeyInfo>>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "获取API密钥列表成功，数量: ${response.data.size}")
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "获取API密钥列表失败"
                    Log.e(TAG, "获取API密钥列表失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "获取API密钥列表请求失败: ${result.message}")
                result
            }
        }
    }
    
    /**
     * 删除API密钥
     */
    suspend fun deleteApiKey(keyId: String): ApiResult<Unit> {
        Log.d(TAG, "删除API密钥: $keyId")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val result = apiClient.delete<ApiResponse<Any>>("/api-keys/$keyId", object : TypeToken<ApiResponse<Any>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success) {
                    Log.d(TAG, "API密钥删除成功")
                    ApiResult.Success(Unit)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "删除API密钥失败"
                    Log.e(TAG, "删除API密钥失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "删除API密钥请求失败: ${result.message}")
                result
            }
        }
    }
    
    // ==================== 使用量统计API ====================
    
    /**
     * 获取使用量统计
     */
    suspend fun getUsageStats(period: String = "current"): ApiResult<UsageStats> {
        Log.d(TAG, "获取使用量统计: period=$period")
        
        if (!UserAuthManager.isLoggedIn(context)) {
            return ApiResult.Error("用户未登录")
        }
        
        val result = apiClient.get<ApiResponse<UsageStats>>("/usage/stats?period=$period", object : TypeToken<ApiResponse<UsageStats>>() {}.type)

        return when (result) {
            is ApiResult.Success -> {
                val response = result.data
                if (response.success && response.data != null) {
                    Log.d(TAG, "获取使用量统计成功")
                    ApiResult.Success(response.data)
                } else {
                    val errorMsg = response.error?.message ?: response.message ?: "获取使用量统计失败"
                    Log.e(TAG, "获取使用量统计失败: $errorMsg")
                    ApiResult.Error(errorMsg)
                }
            }
            is ApiResult.Error -> {
                Log.e(TAG, "获取使用量统计请求失败: ${result.message}")
                result
            }
        }
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 检查服务器连接
     */
    suspend fun checkServerConnection(): ApiResult<HealthResponse> {
        return apiClient.checkServerConnection()
    }
    
    /**
     * 登出用户
     */
    fun logout(): Boolean {
        Log.d(TAG, "用户登出")
        return UserAuthManager.logout(context)
    }
}
