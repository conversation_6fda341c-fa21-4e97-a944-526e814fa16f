package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import android.util.Log
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

/**
 * LLM API 密钥安全管理器
 * 支持多个LLM提供商的密钥管理和设置存储
 */
object LLMApiKeyManager {
    private const val TAG = "LLMApiKeyManager"
    private const val PREFS_NAME = "llm_secure_prefs"
    private const val KEY_ENCRYPTION_KEY = "encryption_key"
    private const val KEY_CURRENT_PROVIDER = "current_provider"
    private const val KEY_AUTO_OPTIMIZE = "auto_optimize"
    
    // API密钥存储键名
    private const val KEY_GEMINI_API = "gemini_api_key"
    private const val KEY_DEEPSEEK_API = "deepseek_api_key"
    
    /**
     * 获取指定提供商的API密钥
     */
    fun getApiKey(context: Context, provider: LLMProvider): String {
        // 1. 首先检查 BuildConfig 中的预配置密钥（仅Gemini）
        if (provider == LLMProvider.GEMINI) {
            val buildConfigKey = BuildConfig.GEMINI_API_KEY
            if (buildConfigKey.isNotBlank() && buildConfigKey != "YOUR_GEMINI_API_KEY_HERE") {
                Log.d(TAG, "使用 BuildConfig 中的 Gemini API 密钥")
                return buildConfigKey
            }
        }
        
        // 2. 检查加密存储中的用户输入密钥
        val storedKey = getStoredApiKey(context, provider)
        if (storedKey.isNotBlank()) {
            Log.d(TAG, "使用存储的 ${provider.displayName} API 密钥")
            return storedKey
        }
        
        // 3. 没有找到有效密钥
        Log.d(TAG, "未找到有效的 ${provider.displayName} API 密钥")
        return ""
    }
    
    /**
     * 保存指定提供商的API密钥
     */
    fun saveApiKey(context: Context, provider: LLMProvider, apiKey: String): Boolean {
        return try {
            val encryptedKey = encryptString(context, apiKey)
            val prefs = getSecurePrefs(context)
            val keyName = getApiKeyName(provider)
            prefs.edit().putString(keyName, encryptedKey).apply()
            Log.d(TAG, "${provider.displayName} API 密钥已保存")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存 ${provider.displayName} API 密钥失败", e)
            false
        }
    }
    
    /**
     * 检查指定提供商是否有可用的API密钥
     */
    fun hasValidApiKey(context: Context, provider: LLMProvider): Boolean {
        val apiKey = getApiKey(context, provider)
        return apiKey.isNotBlank() && apiKey != "YOUR_GEMINI_API_KEY_HERE"
    }
    
    /**
     * 清除指定提供商的存储密钥
     */
    fun clearStoredApiKey(context: Context, provider: LLMProvider): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            val keyName = getApiKeyName(provider)
            prefs.edit().remove(keyName).apply()
            Log.d(TAG, "已清除存储的 ${provider.displayName} API 密钥")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清除 ${provider.displayName} API 密钥失败", e)
            false
        }
    }
    
    /**
     * 获取当前选择的LLM提供商
     */
    fun getCurrentProvider(context: Context): LLMProvider {
        val prefs = getSecurePrefs(context)
        val providerId = prefs.getString(KEY_CURRENT_PROVIDER, LLMProvider.GEMINI.id) ?: LLMProvider.GEMINI.id
        return LLMProvider.fromId(providerId)
    }
    
    /**
     * 设置当前选择的LLM提供商
     */
    fun setCurrentProvider(context: Context, provider: LLMProvider): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            prefs.edit().putString(KEY_CURRENT_PROVIDER, provider.id).apply()
            Log.d(TAG, "当前LLM提供商已设置为: ${provider.displayName}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置当前LLM提供商失败", e)
            false
        }
    }
    
    /**
     * 获取自动优化设置
     */
    fun getAutoOptimize(context: Context): Boolean {
        val prefs = getSecurePrefs(context)
        return prefs.getBoolean(KEY_AUTO_OPTIMIZE, true) // 默认为true
    }
    
    /**
     * 设置自动优化
     */
    fun setAutoOptimize(context: Context, autoOptimize: Boolean): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            prefs.edit().putBoolean(KEY_AUTO_OPTIMIZE, autoOptimize).apply()
            Log.d(TAG, "自动优化设置已更新为: $autoOptimize")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置自动优化失败", e)
            false
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    private fun getApiKeyName(provider: LLMProvider): String {
        return when (provider) {
            LLMProvider.GEMINI -> KEY_GEMINI_API
            LLMProvider.DEEPSEEK -> KEY_DEEPSEEK_API
        }
    }
    
    private fun getStoredApiKey(context: Context, provider: LLMProvider): String {
        return try {
            val prefs = getSecurePrefs(context)
            val keyName = getApiKeyName(provider)
            val encryptedKey = prefs.getString(keyName, "") ?: ""
            if (encryptedKey.isNotBlank()) {
                decryptString(context, encryptedKey)
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取存储的 ${provider.displayName} API 密钥失败", e)
            ""
        }
    }
    
    private fun getSecurePrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    private fun getOrCreateEncryptionKey(context: Context): SecretKey {
        val prefs = getSecurePrefs(context)
        val keyString = prefs.getString(KEY_ENCRYPTION_KEY, null)
        
        return if (keyString != null) {
            // 使用现有密钥
            val keyBytes = Base64.decode(keyString, Base64.DEFAULT)
            SecretKeySpec(keyBytes, "AES")
        } else {
            // 生成新密钥
            val keyGenerator = KeyGenerator.getInstance("AES")
            keyGenerator.init(256)
            val secretKey = keyGenerator.generateKey()
            
            // 保存密钥
            val keyBytes = secretKey.encoded
            val keyString64 = Base64.encodeToString(keyBytes, Base64.DEFAULT)
            prefs.edit().putString(KEY_ENCRYPTION_KEY, keyString64).apply()
            
            secretKey
        }
    }
    
    private fun encryptString(context: Context, plainText: String): String {
        val secretKey = getOrCreateEncryptionKey(context)
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        return Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
    }
    
    private fun decryptString(context: Context, encryptedText: String): String {
        val secretKey = getOrCreateEncryptionKey(context)
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val encryptedBytes = Base64.decode(encryptedText, Base64.DEFAULT)
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }
    
    // ==================== 向后兼容方法 ====================
    
    /**
     * 向后兼容：获取Gemini API密钥（保持原有接口）
     */
    fun getGeminiApiKey(context: Context): String {
        return getApiKey(context, LLMProvider.GEMINI)
    }
    
    /**
     * 向后兼容：检查是否有有效的API密钥（检查当前提供商）
     */
    fun hasValidApiKey(context: Context): Boolean {
        val currentProvider = getCurrentProvider(context)
        return hasValidApiKey(context, currentProvider)
    }
    
    /**
     * 向后兼容：获取API URL（使用当前提供商）
     */
    fun getApiUrl(context: Context): String {
        val currentProvider = getCurrentProvider(context)
        return when (currentProvider) {
            LLMProvider.GEMINI -> {
                val apiKey = getApiKey(context, LLMProvider.GEMINI)
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=$apiKey"
            }
            LLMProvider.DEEPSEEK -> {
                "https://api.deepseek.com/v1/chat/completions"
            }
        }
    }
    
    /**
     * 向后兼容：保存API密钥（保存到当前提供商）
     */
    fun saveApiKey(context: Context, apiKey: String): Boolean {
        val currentProvider = getCurrentProvider(context)
        return saveApiKey(context, currentProvider, apiKey)
    }
    
    /**
     * 向后兼容：清除存储的API密钥（清除当前提供商）
     */
    fun clearStoredApiKey(context: Context): Boolean {
        val currentProvider = getCurrentProvider(context)
        return clearStoredApiKey(context, currentProvider)
    }
}
