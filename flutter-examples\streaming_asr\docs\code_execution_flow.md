# 项目代码执行流程

本文档描述了 `streaming_asr` Flutter 示例项目的主要功能模块和代码执行流程。

## 主要功能模块

1.  **UI & 交互模块 (`main.dart`, `streaming_asr.dart`)**: 构建和管理用户界面, 处理用户输入, 并展示识别结果。
2.  **语音识别核心模块 (`streaming_asr.dart`)**: 集成 `sherpa_onnx` 插件, 管理识别器实例和音频数据流, 执行语音到文本的核心转换逻辑。
3.  **模型管理模块 (`online_model.dart`)**: 负责配置和加载位于 `assets` 目录下的 ONNX 模型文件和词汇表。
4.  **音频采集模块 (`record` 插件)**: 使用 `record` 插件从设备麦克风捕获实时音频流。
5.  **工具函数模块 (`utils.dart`)**: 提供辅助函数, 如将 assets 文件复制到可访问目录、将音频数据从 `int16` 转换为 `float32`。

## 代码执行流程

### 流程图

```mermaid
graph TD
    A[用户启动App] --> B{MyHomePage};
    B --> C[显示 StreamingAsrScreen];
    C --> D[UI初始化: 显示标题/文本框/按钮];

    subgraph "点击 'Start' 按钮后"
        E[调用 _start 方法] --> F{首次启动?};
        F -- 是 --> G[初始化 SherpaOnnx];
        G --> H[创建 OnlineRecognizer];
        H --> I[创建 OnlineStream];
        F -- 否 --> J;
        I --> J;
        J[请求麦克风权限] --> K[启动麦克风录音 recorder.startStream];
        K --> L[获取音频数据流 Stream<Uint8List>];
    end

    subgraph "音频流处理循环"
        L --> M[监听音频数据块];
        M --> N[将 Int16 PCM 转为 Float32];
        N --> O[stream.acceptWaveform 音频数据];
        O --> P{recognizer.isReady?};
        P -- 是 --> Q["recognizer.decode (stream)"];
        Q --> R["recognizer.getResult(stream)"];
        R --> S[更新UI文本框];
        S --> T{recognizer.isEndpoint?};
        T -- 是 --> U[重置识别流, 固化本次结果];
        U --> M;
        T -- 否 --> M;
        P -- 否 --> M;
    end

    subgraph "点击 'Stop' 按钮后"
        V[调用 _stop 方法] --> W[停止麦克风录音];
        W --> X[释放并重建 OnlineStream];
        X --> C;
    end

    D --> E;
    D --> V;
```

### 文字说明

1.  **启动与初始化**:
    *   用户打开应用, `main.dart` 中的 `MyApp` 和 `MyHomePage` 被加载, 主界面展示 `StreamingAsrScreen`。
    *   `StreamingAsrScreen` 的 `initState` 方法被调用, 初始化 `AudioRecorder` (录音机) 和 `TextEditingController` (文本控制器)。

2.  **开始识别 (点击 Start)**:
    *   用户点击 "Start" 按钮, `_start()` 方法被触发。
    *   **首次点击**: 程序会首先初始化 `sherpa-onnx` 库, 然后调用 `createOnlineRecognizer()`。
    *   `createOnlineRecognizer()` 内部会调用 `online_model.dart` 中的 `getOnlineModelConfig()` 来获取模型配置。此过程会使用 `utils.dart` 的 `copyAssetFile` 将 ONNX 模型文件从 assets 目录复制到应用的工作目录。
    *   创建 `OnlineRecognizer` (识别器) 和 `OnlineStream` (音频流) 实例。
    *   **所有点击**: 请求麦克风权限, 然后调用 `_audioRecorder.startStream()` 开始录音。录音参数被设置为 16kHz 采样率, 16-bit PCM 格式。

3.  **实时处理音频流**:
    *   `startStream` 返回一个 `Stream`, 程序开始监听从麦克风传来的音频数据块 (`Uint8List`)。
    *   对于每一个数据块:
        *   使用 `convertBytesToFloat32` 将其从 `Int16` 格式转换为 `Float32` 格式。
        *   调用 `_stream!.acceptWaveform()` 将转换后的音频样本送入识别流。
        *   循环调用 `_recognizer!.decode(_stream!)` 来处理内部缓冲区的音频数据。
        *   调用 `_recognizer!.getResult(_stream!).text` 获取当前的识别结果 (可能是中间结果)。
        *   将获取到的文本更新到界面的 `TextField` 中。
        *   检查 `_recognizer!.isEndpoint()` 判断一句话是否结束。如果结束, 则将当前识别结果固化, 并调用 `_recognizer!.reset(_stream!)` 重置流, 准备识别下一句话。

4.  **停止识别 (点击 Stop)**:
    *   用户点击 "Stop" 按钮, `_stop()` 方法被触发。
    *   调用 `_audioRecorder.stop()` 停止录音。
    *   当前的 `OnlineStream` 被释放, 并创建一个新的空白 `OnlineStream` 供下一次识别使用。应用返回等待状态。
