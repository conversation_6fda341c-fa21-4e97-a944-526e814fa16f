<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="🔧 LLM 设置"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- LLM提供商选择 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/card_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🤖 选择 LLM 提供商"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="12dp" />

            <RadioGroup
                android:id="@+id/rg_llm_provider"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/rb_gemini"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Gemini 2.0 (Google)"
                    android:textSize="16sp"
                    android:padding="8dp" />

                <RadioButton
                    android:id="@+id/rb_deepseek"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="DeepSeek"
                    android:textSize="16sp"
                    android:padding="8dp" />

            </RadioGroup>

        </LinearLayout>

        <!-- Gemini API密钥配置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/card_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔑 Gemini API 密钥"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="在 Google AI Studio 获取免费 API 密钥"
                android:textSize="12sp"
                android:textColor="#666666"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/et_gemini_api_key"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="输入 Gemini API 密钥"
                android:inputType="textPassword"
                android:background="@drawable/edittext_background"
                android:padding="12dp"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <Button
                    android:id="@+id/btn_toggle_gemini_visibility"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="显示/隐藏"
                    android:textSize="12sp"
                    android:layout_marginEnd="8dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

                <Button
                    android:id="@+id/btn_clear_gemini"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="清除"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

            </LinearLayout>

        </LinearLayout>

        <!-- DeepSeek API密钥配置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/card_background"
            android:padding="16dp"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔑 DeepSeek API 密钥"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="在 DeepSeek 官网注册并获取 API 密钥"
                android:textSize="12sp"
                android:textColor="#666666"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/et_deepseek_api_key"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="输入 DeepSeek API 密钥"
                android:inputType="textPassword"
                android:background="@drawable/edittext_background"
                android:padding="12dp"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <Button
                    android:id="@+id/btn_toggle_deepseek_visibility"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="显示/隐藏"
                    android:textSize="12sp"
                    android:layout_marginEnd="8dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

                <Button
                    android:id="@+id/btn_clear_deepseek"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="清除"
                    android:textSize="12sp"
                    android:layout_marginStart="8dp"
                    style="@style/Widget.AppCompat.Button.Borderless" />

            </LinearLayout>

        </LinearLayout>

        <!-- 自动优化设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/card_background"
            android:padding="16dp"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="⚙️ 自动优化设置"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <Switch
                    android:id="@+id/switch_auto_optimize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="自动优化 ASR 结果"
                        android:textSize="16sp"
                        android:textColor="#333333" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="停止录音后自动使用 LLM 优化语音识别结果"
                        android:textSize="12sp"
                        android:textColor="#666666" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_save_settings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="💾 保存设置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginEnd="8dp"
                android:background="@drawable/button_primary"
                android:textColor="@android:color/white"
                android:padding="12dp" />

            <Button
                android:id="@+id/btn_test_connection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔗 测试连接"
                android:textSize="16sp"
                android:layout_marginStart="8dp"
                android:background="@drawable/button_secondary"
                android:textColor="#333333"
                android:padding="12dp" />

        </LinearLayout>

        <!-- 状态显示 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请配置 LLM 设置"
            android:textSize="14sp"
            android:textColor="#666666"
            android:gravity="center"
            android:layout_marginTop="16dp"
            android:padding="12dp"
            android:background="@drawable/status_background" />

    </LinearLayout>

</ScrollView>
