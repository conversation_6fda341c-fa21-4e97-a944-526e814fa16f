package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import kotlinx.coroutines.launch

/**
 * AI驱动的TODO列表Activity
 * 支持拖拽排序、手动编辑、智能分类等功能
 */
class TodoActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TodoActivity"
        private const val REQUEST_ADD_TODO = 1001
        private const val REQUEST_EDIT_TODO = 1002
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSettings: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvStatistics: TextView
    private lateinit var spinnerFilter: Spinner
    private lateinit var etSearch: EditText
    private lateinit var recyclerView: RecyclerView
    private lateinit var fabAdd: FloatingActionButton
    private lateinit var layoutEmpty: LinearLayout
    private lateinit var btnCreateSample: Button
    
    // 数据和适配器
    private lateinit var todoManager: TodoManager
    private lateinit var reminderManager: TodoReminderManager
    private lateinit var todoAdapter: GroupedTodoAdapter
    private var currentFilter = FilterType.ALL
    private var currentSearchQuery = ""
    private var allTodos = mutableListOf<TodoItem>()
    private var filteredTodos = mutableListOf<TodoItem>()
    
    // 过滤类型
    enum class FilterType(val displayName: String) {
        ALL("全部"),
        INCOMPLETE("未完成"),
        COMPLETED("已完成"),
        HIGH_PRIORITY("高优先级"),
        OVERDUE("已过期"),
        DUE_SOON("即将到期")
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_todo)

        initViews()
        initTodoManager()
        initRecyclerView()
        initFilterSpinner()
        setupListeners()
        loadTodos()

        // 检查电池优化和权限
        checkPermissionsAndOptimizations()
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSettings = findViewById(R.id.btn_settings)
        tvTitle = findViewById(R.id.tv_title)
        tvStatistics = findViewById(R.id.tv_statistics)
        spinnerFilter = findViewById(R.id.spinner_filter)
        etSearch = findViewById(R.id.et_search)
        recyclerView = findViewById(R.id.recycler_view)
        fabAdd = findViewById(R.id.fab_add)
        layoutEmpty = findViewById(R.id.layout_empty)
        btnCreateSample = findViewById(R.id.btn_create_sample)
        
        tvTitle.text = "📝 AI待办"
    }
    
    private fun initTodoManager() {
        todoManager = TodoManager.getInstance(this)
        reminderManager = TodoReminderManager.getInstance(this)
    }
    
    private fun initRecyclerView() {
        todoAdapter = GroupedTodoAdapter(
            onItemClick = { todoItem ->
                editTodo(todoItem)
            },
            onItemToggle = { todoItem ->
                updateTodoStatus(todoItem, !todoItem.isCompleted)
            },
            onItemDelete = { todoItem ->
                deleteTodo(todoItem)
            }
        )

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = todoAdapter

        // 设置拖拽排序功能
        val itemTouchHelper = ItemTouchHelper(GroupedTodoItemTouchHelperCallback(todoAdapter) { reorderedList ->
            // 更新TODO排序
            lifecycleScope.launch {
                try {
                    val success = todoManager.updateTodoOrder(reorderedList)
                    if (!success) {
                        showToast("排序更新失败")
                        // 重新加载数据以恢复原始顺序
                        loadTodos()
                    }
                } catch (e: Exception) {
                    showToast("排序更新失败: ${e.message}")
                    loadTodos()
                }
            }
        })
        itemTouchHelper.attachToRecyclerView(recyclerView)
    }
    
    private fun initFilterSpinner() {
        val filterOptions = FilterType.values().map { it.displayName }
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, filterOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerFilter.adapter = adapter
    }
    
    private fun setupListeners() {
        btnBack.setOnClickListener { finish() }
        
        btnSettings.setOnClickListener {
            // TODO: 打开TODO设置页面
            showToast("TODO设置功能开发中...")
        }
        
        fabAdd.setOnClickListener {
            addNewTodo()
        }
        
        btnCreateSample.setOnClickListener {
            createSampleTodos()
        }
        
        // 过滤器选择监听
        spinnerFilter.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                currentFilter = FilterType.values()[position]
                loadTodos()
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        // 搜索监听 - 使用TextWatcher代替SearchView
        etSearch.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                currentSearchQuery = s?.toString() ?: ""
                filterTodos(currentSearchQuery)
            }

            override fun afterTextChanged(s: android.text.Editable?) {}
        })
    }
    
    private fun loadTodos() {
        lifecycleScope.launch {
            try {
                val todos = when (currentFilter) {
                    FilterType.ALL -> todoManager.getAllTodos()
                    FilterType.INCOMPLETE -> todoManager.getIncompleteTodos()
                    FilterType.COMPLETED -> todoManager.getCompletedTodos()
                    FilterType.HIGH_PRIORITY -> todoManager.getTodosByPriority(TodoItem.Priority.HIGH) +
                                              todoManager.getTodosByPriority(TodoItem.Priority.URGENT)
                    FilterType.OVERDUE -> todoManager.getOverdueTodos()
                    FilterType.DUE_SOON -> todoManager.getDueSoonTodos()
                }

                allTodos.clear()
                allTodos.addAll(todos)

                filterTodos(currentSearchQuery)
                updateStatistics()

            } catch (e: Exception) {
                Log.e(TAG, "加载TODO列表失败", e)
                showToast("加载失败: ${e.message}")
            }
        }
    }

    private fun filterTodos(query: String) {
        filteredTodos.clear()

        if (query.isEmpty()) {
            filteredTodos.addAll(allTodos)
        } else {
            filteredTodos.addAll(allTodos.filter { todo ->
                todo.title.contains(query, ignoreCase = true) ||
                todo.description.contains(query, ignoreCase = true) ||
                todo.category.contains(query, ignoreCase = true)
            })
        }

        todoAdapter.updateTodos(filteredTodos)
        updateEmptyState(filteredTodos.isEmpty())
    }
    
    private fun updateStatistics() {
        val total = allTodos.size
        val completed = allTodos.count { it.isCompleted }
        val incomplete = total - completed
        val overdue = allTodos.count { it.isOverdue() }

        tvStatistics.text = "总计: $total | 已完成: $completed | 未完成: $incomplete | 过期: $overdue"
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            recyclerView.visibility = View.GONE
            layoutEmpty.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            layoutEmpty.visibility = View.GONE
        }
    }
    
    private fun addNewTodo() {
        val intent = Intent(this, TodoEditActivity::class.java)
        startActivityForResult(intent, REQUEST_ADD_TODO)
    }
    
    private fun editTodo(todoItem: TodoItem) {
        val intent = Intent(this, TodoEditActivity::class.java)
        intent.putExtra("todo_id", todoItem.id)
        startActivityForResult(intent, REQUEST_EDIT_TODO)
    }
    
    private fun updateTodoStatus(todoItem: TodoItem, isCompleted: Boolean) {
        lifecycleScope.launch {
            try {
                val updatedTodo = todoItem.copy(
                    isCompleted = isCompleted,
                    completedAt = if (isCompleted) System.currentTimeMillis() else null
                )

                val success = todoManager.updateTodo(updatedTodo)
                if (success) {
                    loadTodos()

                    // 如果完成了任务，取消提醒
                    if (isCompleted) {
                        reminderManager.cancelReminder(todoItem.id)
                    } else if (todoItem.dueDate != null) {
                        // 如果重新标记为未完成且有截止时间，重新设置提醒
                        reminderManager.setReminder(updatedTodo)
                    }
                } else {
                    showToast("更新失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新TODO状态失败", e)
                showToast("更新失败: ${e.message}")
            }
        }
    }
    
    private fun deleteTodo(todoItem: TodoItem) {
        AppleInfoDialog(
            context = this,
            title = "删除待办事项",
            message = "确定要删除「${todoItem.title}」吗？\n\n此操作无法撤销。",
            positiveButtonText = "删除",
            negativeButtonText = "取消",
            onPositiveClick = {
                lifecycleScope.launch {
                    try {
                        // 先取消提醒
                        reminderManager.cancelReminder(todoItem.id)

                        val success = todoManager.deleteTodo(todoItem.id)
                        if (success) {
                            loadTodos()
                            showToast("已删除")
                        } else {
                            showToast("删除失败")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "删除TODO失败", e)
                        showToast("删除失败: ${e.message}")
                    }
                }
            }
        ).show()
    }
    
    private fun createSampleTodos() {
        lifecycleScope.launch {
            try {
                val sampleTodos = TodoItem.createSample()
                val success = todoManager.addTodos(sampleTodos)
                if (success) {
                    loadTodos()
                    showToast("已创建示例待办事项")
                } else {
                    showToast("创建失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建示例TODO失败", e)
                showToast("创建失败: ${e.message}")
            }
        }
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_ADD_TODO, REQUEST_EDIT_TODO -> {
                    loadTodos()
                }
            }
        }
    }
    
    override fun onResume() {
        super.onResume()

        // 当用户打开TODO列表时，停止任何正在播放的提醒响铃
        TodoNotificationHelper.stopCurrentRingtone()

        loadTodos()
    }
    
    /**
     * 检查权限和电池优化设置
     */
    private fun checkPermissionsAndOptimizations() {
        // 检查电池优化
        if (!BatteryOptimizationHelper.isIgnoringBatteryOptimizations(this)) {
            BatteryOptimizationHelper.requestIgnoreBatteryOptimizations(this)
        }

        // 检查精确闹钟权限（Android 12+）
        BatteryOptimizationHelper.checkAndRequestExactAlarmPermission(this)

        // 检查通知权限
        val notificationHelper = TodoNotificationHelper(this)
        if (!notificationHelper.areNotificationsEnabled()) {
            showToast("请开启通知权限以接收 TODO 提醒")
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
