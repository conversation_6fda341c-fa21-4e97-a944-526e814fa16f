package com.vectora.vocalmind

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 音频录制前台服务
 * 解决Android 11+后台麦克风访问限制问题
 */
class AudioRecordingService : Service() {

    companion object {
        private const val TAG = "AudioRecordingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "audio_recording_channel"
        private const val SAMPLE_RATE = 16000
        private const val BUFFER_SIZE = 1600 // 100ms at 16kHz
    }

    // Binder for Activity communication
    private val binder = AudioRecordingBinder()
    
    // Audio recording components
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null
    
    // ASR Engine reference
    private var asrEngine: SingleModelASREngine? = null
    
    // Audio recording manager
    private var audioRecordingManager: AudioRecordingManager? = null
    private var currentAudioFilePath: String? = null
    
    // Callback interface for communication with Activity
    interface AudioRecordingCallback {
        fun onAudioData(audioData: FloatArray)
        fun onRecordingStarted()
        fun onRecordingStopped(audioFilePath: String?)
        fun onError(error: String)
    }
    
    private var callback: AudioRecordingCallback? = null
    
    inner class AudioRecordingBinder : Binder() {
        fun getService(): AudioRecordingService = this@AudioRecordingService
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "AudioRecordingService created")
        createNotificationChannel()
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "AudioRecordingService started")
        
        // 处理停止录音的Intent
        if (intent?.action == "STOP_RECORDING") {
            Log.i(TAG, "收到停止录音指令")
            stopRecording()
        }
        
        return START_NOT_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "AudioRecordingService destroyed")
        stopRecording()
    }
    
    /**
     * 设置回调接口
     */
    fun setCallback(callback: AudioRecordingCallback?) {
        this.callback = callback
    }
    
    /**
     * 设置ASR引擎
     */
    fun setAsrEngine(engine: SingleModelASREngine) {
        this.asrEngine = engine
    }
    
    /**
     * 设置音频录制管理器
     */
    fun setAudioRecordingManager(manager: AudioRecordingManager) {
        this.audioRecordingManager = manager
    }
    
    /**
     * 开始录音
     */
    fun startRecording(meetingId: String): Boolean {
        if (isRecording.get()) {
            Log.w(TAG, "Recording is already in progress")
            return false
        }
        
        try {
            // 初始化麦克风
            if (!initMicrophone()) {
                Log.e(TAG, "Failed to initialize microphone")
                callback?.onError("麦克风初始化失败")
                return false
            }
            
            // 启动前台服务
            startForegroundService()
            
            // 开始录音
            audioRecord?.startRecording()
            isRecording.set(true)
            
            // 根据设置决定是否保存录音文件
            if (SettingsActivity.getSaveRecordingSetting(this)) {
                currentAudioFilePath = audioRecordingManager?.startRecording(meetingId)
                if (currentAudioFilePath == null) {
                    Log.w(TAG, "Failed to start audio file recording, but continuing ASR")
                }
            } else {
                Log.i(TAG, "Recording save is disabled, skipping audio file recording")
                currentAudioFilePath = null
            }
            
            // 重置ASR引擎
            asrEngine?.reset()
            
            // 启动录音线程
            recordingThread = Thread {
                recordAudio()
            }
            recordingThread?.start()
            
            callback?.onRecordingStarted()
            Log.i(TAG, "Recording started successfully")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start recording", e)
            callback?.onError("录音启动失败: ${e.message}")
            stopRecording()
            return false
        }
    }
    
    /**
     * 停止录音
     */
    fun stopRecording(): String? {
        if (!isRecording.get()) {
            Log.w(TAG, "Recording is not in progress")
            return null
        }
        
        isRecording.set(false)
        
        try {
            // 停止AudioRecord
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
            
            // 等待录音线程结束
            recordingThread?.join(1000)
            recordingThread = null
            
            // 停止录音文件保存（仅在启用录音保存时）
            if (SettingsActivity.getSaveRecordingSetting(this) && audioRecordingManager != null) {
                val finalAudioFilePath = audioRecordingManager?.stopRecording()
                if (finalAudioFilePath != null) {
                    currentAudioFilePath = finalAudioFilePath
                    Log.i(TAG, "Audio file saved: $finalAudioFilePath")
                } else {
                    Log.w(TAG, "Failed to save audio file")
                    currentAudioFilePath = null
                }
            } else {
                Log.i(TAG, "Recording save is disabled or no recording manager, skipping audio file save")
                currentAudioFilePath = null
            }
            
            // 停止前台服务
            stopForeground(STOP_FOREGROUND_REMOVE)
            
            callback?.onRecordingStopped(currentAudioFilePath)
            Log.i(TAG, "Recording stopped successfully")
            
            return currentAudioFilePath
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop recording", e)
            callback?.onError("录音停止失败: ${e.message}")
            return null
        }
    }
    
    /**
     * 检查是否正在录音
     */
    fun isRecording(): Boolean {
        return isRecording.get()
    }
    
    /**
     * 初始化麦克风
     */
    private fun initMicrophone(): Boolean {
        val bufferSizeInBytes = AudioRecord.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )
        
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            maxOf(bufferSizeInBytes, BUFFER_SIZE * 4)
        )
        
        return audioRecord?.state == AudioRecord.STATE_INITIALIZED
    }
    
    /**
     * 录音处理线程
     */
    private fun recordAudio() {
        Log.i(TAG, "Audio recording thread started")
        
        val buffer = ShortArray(BUFFER_SIZE)
        
        while (isRecording.get()) {
            try {
                val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (ret > 0) {
                    // 保存音频数据到文件（仅在启用录音保存时）
                    if (SettingsActivity.getSaveRecordingSetting(this@AudioRecordingService)) {
                        audioRecordingManager?.writeAudioData(buffer, ret)
                    }
                    
                    // 转换为Float数组 (归一化到[-1, 1])
                    val samples = FloatArray(ret) { buffer[it] / 32768.0f }
                    
                    // 通过回调发送音频数据给Activity
                    callback?.onAudioData(samples)
                    
                    // 直接处理ASR（如果引擎已设置）
                    asrEngine?.processAudio(samples)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Audio processing failed", e)
                break
            }
        }
        
        Log.i(TAG, "Audio recording thread ended")
    }
    
    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 需要指定前台服务类型
            startForeground(
                NOTIFICATION_ID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
            )
        } else {
            startForeground(NOTIFICATION_ID, notification)
        }
        
        Log.i(TAG, "Foreground service started with microphone type")
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "音频录制服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "语音转录录音服务"
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        // 创建点击通知时打开应用的Intent
        val intent = Intent(this, SingleModelActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // 创建停止录音的Intent
        val stopIntent = Intent(this, AudioRecordingService::class.java).apply {
            action = "STOP_RECORDING"
        }
        
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("语音转录正在进行")
            .setContentText("点击返回应用，或点击停止按钮结束录音")
            .setSmallIcon(android.R.drawable.ic_btn_speak_now)
            .setContentIntent(pendingIntent)
            .addAction(
                android.R.drawable.ic_media_pause,
                "停止录音",
                stopPendingIntent
            )
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
}