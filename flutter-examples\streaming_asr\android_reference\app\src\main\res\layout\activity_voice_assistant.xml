<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- Main Content with Tab Layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">



        <!-- ViewPager2 for Tab Content -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- 炫酷底部导航栏 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false">

            <View
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@drawable/bottom_nav_background"
                android:elevation="12dp" />

            <!-- 自定义底部导航内容 -->
            <LinearLayout
                android:id="@+id/custom_bottom_nav"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                android:gravity="center_vertical"
                android:elevation="13dp">

                <!-- 会议记录 Tab -->
                <FrameLayout
                    android:id="@+id/tab_meeting_records"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/tab_button_selector">

                    <ImageView
                        android:id="@+id/icon_meeting_records"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_meeting_records_apple"
                        android:tint="@color/apple_secondary_label" />

                </FrameLayout>

                <!-- 录音 Tab (中间特殊按钮) -->
                <FrameLayout
                    android:id="@+id/tab_recording"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:clipChildren="false"
                    android:clipToPadding="false">

                    <!-- 中间浮动按钮 -->
                    <FrameLayout
                        android:layout_width="34dp"
                        android:layout_height="39dp"
                        android:layout_marginTop="3dp"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/center_tab_background"
                        android:elevation="5dp"
                       >

                        <ImageView
                            android:id="@+id/icon_recording"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="2dp"
                            android:src="@drawable/ic_add"
                            android:tint="@color/white" />

                    </FrameLayout>

                </FrameLayout>

                <!-- 待办 Tab -->
                <FrameLayout
                    android:id="@+id/tab_todo"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/tab_button_selector">

                    <ImageView
                        android:id="@+id/icon_todo"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_todo"
                        android:tint="@color/apple_secondary_label" />

                </FrameLayout>

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
