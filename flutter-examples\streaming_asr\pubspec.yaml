name: streaming_asr

description: >
  This example shows how to implement real-time speech recognition using sherpa-onnx.

publish_to: 'none'

version: 1.12.7

topics:
  - speech-recognition

issue_tracker: https://github.com/k2-fsa/sherpa-onnx/issues

repository: https://github.com/k2-fsa/sherpa-onnx/tree/master/sherpa-onnx/flutter

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=2.8.1"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.6

  path_provider: ^2.1.3
  path: ^1.9.0

  # Note: record does not support Linux for streaming ASR
  record: ^5.1.0
  url_launcher: ^6.2.6

  sherpa_onnx: ^1.12.7
  flutter_riverpod: ^2.6.1
  go_router: ^14.2.0
  # sherpa_onnx:
  #   path: ../../flutter/sherpa_onnx

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/decoder-epoch-99-avg-1.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/encoder-epoch-99-avg-1.int8.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.int8.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/joiner-epoch-99-avg-1.onnx
    - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/tokens.txt

    #    - assets/
    # - assets/sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20/
