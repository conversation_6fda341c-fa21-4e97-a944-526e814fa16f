<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingBottom="0dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:paddingTop="16dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="📚 记录"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline" />

            <TextView
                android:id="@+id/tv_record_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 条记录"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label" />

        </LinearLayout>

        <ImageButton
            android:id="@+id/btn_clear_all"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="清空所有记录"
            android:src="@drawable/ic_delete"
            android:visibility="gone" />

        <ImageButton
            android:id="@+id/btn_settings"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="设置"
            android:src="@drawable/ic_settings_apple" />

    </LinearLayout>

    <!-- Search Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/apple_system_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp"
                    android:alpha="0.6"
                    android:src="@drawable/ic_search" />

                <EditText
                    android:id="@+id/et_search"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="搜索..."
                    android:inputType="text"
                    android:maxLines="1"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                    android:textColorHint="@color/apple_tertiary_label" />

                <ImageButton
                    android:id="@+id/btn_clear_search"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:alpha="0.6"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="清除搜索"
                    android:src="@drawable/ic_clear"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Meeting Records List -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_meeting_records"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:scrollbars="vertical" />

        <!-- Empty View -->
        <LinearLayout
            android:id="@+id/ll_empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="📝"
                android:textSize="48sp" />

            <TextView
                android:id="@+id/tv_empty_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="暂无会议记录\n\n开始录音后会自动保存会议记录"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                android:textColor="@color/apple_gray" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
