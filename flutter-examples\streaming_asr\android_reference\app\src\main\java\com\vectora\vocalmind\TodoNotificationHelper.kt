package com.vectora.vocalmind

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

/**
 * TODO提醒通知助手类
 * 负责创建通知渠道、显示通知、播放响铃和振动
 */
class TodoNotificationHelper(private val context: Context) {

    companion object {
        private const val CHANNEL_ID = "todo_reminder_channel"
        private const val CHANNEL_NAME = "待办事项提醒"
        private const val CHANNEL_DESCRIPTION = "用于显示待办事项提醒通知"
        private const val NOTIFICATION_ID_BASE = 10000
        const val ACTION_STOP_REMINDER = "com.vectora.vocalmind.STOP_REMINDER"

        // 静态变量管理当前播放的响铃
        @Volatile
        private var currentRingtone: android.media.Ringtone? = null

        /**
         * 停止当前播放的响铃
         */
        fun stopCurrentRingtone() {
            currentRingtone?.let { ringtone ->
                if (ringtone.isPlaying) {
                    ringtone.stop()
                }
                currentRingtone = null
            }
        }
    }

    private val notificationManager = NotificationManagerCompat.from(context)
    
    init {
        createNotificationChannel()
    }
    
    /**
     * 创建通知渠道（Android 8.0+）
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION

                // 启用振动和灯光
                enableVibration(true)
                enableLights(true)
                lightColor = android.graphics.Color.BLUE

                // 设置为可以绕过勿扰模式
                setBypassDnd(true)

                // 设置锁屏显示
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC

                // 设置默认响铃声音 - 使用闹钟声音更响亮
                val alarmSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
                    ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

                setSound(alarmSoundUri, AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_ALARM)
                    .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                    .build())

                // 设置强烈的振动模式
                vibrationPattern = longArrayOf(0, 1000, 500, 1000, 500, 1000)

                // 显示角标
                setShowBadge(true)
            }

            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            systemNotificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 显示TODO提醒通知
     */
    fun showReminderNotification(todoItem: TodoItem) {
        // 先停止之前的响铃
        stopCurrentRingtone()

        // 创建停止响铃的Intent
        val stopRingtoneIntent = Intent(context, TodoReminderReceiver::class.java).apply {
            action = ACTION_STOP_REMINDER
        }
        val stopRingtonePendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            stopRingtoneIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 创建点击通知后的Intent - 使用专门的停止响铃Activity
        val intent = Intent(context, StopReminderActivity::class.java).apply {
            putExtra(StopReminderActivity.EXTRA_TARGET_TODO_ID, todoItem.id)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            todoItem.id.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // 构建高优先级通知
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("📝 待办事项提醒")
            .setContentText(todoItem.title)
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText(buildNotificationText(todoItem)))
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setDefaults(NotificationCompat.DEFAULT_LIGHTS) // 只使用灯光，声音和振动单独控制
            // 全屏Intent用于重要提醒
            .setFullScreenIntent(pendingIntent, true)
            // 设置为持续通知，用户必须手动清除
            .setOngoing(false)
            // 在锁屏上显示
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            // 设置大图标
            .setLargeIcon(android.graphics.BitmapFactory.decodeResource(
                context.resources, R.drawable.ic_notification))
            // 添加停止响铃的操作按钮
            .addAction(R.drawable.ic_close, "停止响铃", stopRingtonePendingIntent)
            .build()
        
        // 显示通知
        val notificationId = NOTIFICATION_ID_BASE + todoItem.id.hashCode()
        notificationManager.notify(notificationId, notification)
        
        // 触发振动和响铃
        triggerVibrationAndSound()
    }
    
    /**
     * 构建通知文本内容
     */
    private fun buildNotificationText(todoItem: TodoItem): String {
        return buildString {
            append("📋 ${todoItem.title}\n")
            
            if (todoItem.description.isNotEmpty()) {
                append("📝 ${todoItem.description}\n")
            }
            
            append("🏷️ 分类: ${todoItem.category}\n")
            append("⭐ 优先级: ${todoItem.priority.displayName}\n")
            
            todoItem.dueDate?.let {
                append("⏰ 截止时间: ${todoItem.getFormattedDueDate()}\n")
            }
            
            append("🔔 提醒时间: ${todoItem.getFormattedReminderTime()}")
        }
    }
    
    /**
     * 触发振动和响铃
     */
    private fun triggerVibrationAndSound() {
        // 触发振动
        triggerVibration()
        
        // 播放响铃声音
        playNotificationSound()
    }
    
    /**
     * 触发系统级振动
     */
    private fun triggerVibration() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Android 12+ 使用 VibratorManager
                val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                val vibrator = vibratorManager.defaultVibrator

                // 使用更强烈的振动模式
                val vibrationEffect = VibrationEffect.createWaveform(
                    longArrayOf(0, 1000, 500, 1000, 500, 1000), // 更长的振动
                    -1 // 不重复
                )

                // 使用音频属性确保振动能够执行
                val audioAttributes = AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_ALARM)
                    .build()

                vibrator.vibrate(vibrationEffect, audioAttributes)

            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 使用 VibrationEffect
                val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

                val vibrationEffect = VibrationEffect.createWaveform(
                    longArrayOf(0, 1000, 500, 1000, 500, 1000),
                    -1
                )
                vibrator.vibrate(vibrationEffect)

            } else {
                // Android 8.0 以下版本
                @Suppress("DEPRECATION")
                val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                @Suppress("DEPRECATION")
                vibrator.vibrate(longArrayOf(0, 1000, 500, 1000, 500, 1000), -1)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 播放系统级响铃声音
     */
    private fun playNotificationSound() {
        try {
            // 先停止之前的响铃
            stopCurrentRingtone()

            // 优先使用闹钟声音，更响亮
            val alarmUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
            val soundUri = alarmUri ?: RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

            val ringtone = RingtoneManager.getRingtone(context, soundUri)
            ringtone?.apply {
                // 设置音频流类型为闹钟，确保能够播放
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    audioAttributes = AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .setUsage(AudioAttributes.USAGE_ALARM)
                        .setFlags(AudioAttributes.FLAG_AUDIBILITY_ENFORCED)
                        .build()
                }

                // 保存当前响铃实例以便后续控制
                currentRingtone = this
                play()

                // 设置自动停止时间（30秒后自动停止，避免无限响铃）
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    stopCurrentRingtone()
                }, 30000)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 取消指定TODO的提醒通知
     */
    fun cancelReminderNotification(todoId: String) {
        val notificationId = NOTIFICATION_ID_BASE + todoId.hashCode()
        notificationManager.cancel(notificationId)
    }
    
    /**
     * 取消所有TODO提醒通知
     */
    fun cancelAllReminderNotifications() {
        notificationManager.cancelAll()
    }
    
    /**
     * 检查通知权限是否已授予
     */
    fun areNotificationsEnabled(): Boolean {
        return notificationManager.areNotificationsEnabled()
    }
}
