package com.vectora.vocalmind

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.*
import io.noties.markwon.Markwon
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
import io.noties.markwon.ext.tables.TablePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin

/**
 * AI聊天页面
 * 基于会议记录内容与AI进行智能问答
 */
class AiChatActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "AiChatActivity"
        const val EXTRA_MEETING_RECORD_ID = "meeting_record_id"
        const val EXTRA_MEETING_TITLE = "meeting_title"
        const val EXTRA_MEETING_CONTENT = "meeting_content"
    }

    private lateinit var btnBack: ImageButton
    private lateinit var btnClearChat: ImageButton
    private lateinit var tvMeetingContext: TextView
    private lateinit var rvChatMessages: RecyclerView
    private lateinit var etMessage: EditText
    private lateinit var btnSend: ImageView
    private lateinit var progressLoading: ProgressBar

    private lateinit var chatAdapter: ChatMessageAdapter
    private lateinit var chatManager: ChatManager
    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var markwon: Markwon

    private var meetingRecordId: String = ""
    private var meetingTitle: String = ""
    private var meetingContent: String = ""
    private var isProcessing = false

    // 滚动节流相关
    private var lastScrollTime = 0L
    private val scrollThrottleDelay = 100L // 100ms节流

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ai_chat)

        initMarkwon()
        initViews()
        initManagers()
        loadIntentData()
        setupRecyclerView()
        setupInputListeners()
        loadChatHistory()
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnClearChat = findViewById(R.id.btn_clear_chat)
        tvMeetingContext = findViewById(R.id.tv_meeting_context)
        rvChatMessages = findViewById(R.id.rv_chat_messages)
        etMessage = findViewById(R.id.et_message)
        btnSend = findViewById(R.id.btn_send)
        progressLoading = findViewById(R.id.progress_loading)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnClearChat.setOnClickListener { showClearChatDialog() }
        btnSend.setOnClickListener { sendMessage() }
    }

    private fun initMarkwon() {
        markwon = Markwon.builder(this)
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(TablePlugin.create(this))
            .usePlugin(TaskListPlugin.create(this))
            .build()
    }

    /**
     * 设置TextView的Markdown内容
     */
    private fun setMarkdownText(textView: TextView, markdownText: String?) {
        val safeText = markdownText ?: ""
        if (safeText.trim().isEmpty()) {
            textView.text = safeText
            return
        }

        try {
            markwon.setMarkdown(textView, safeText)
        } catch (e: Exception) {
            Log.w(TAG, "Markdown渲染失败，使用原始文本: ${e.message}")
            textView.text = safeText
        }
    }

    private fun initManagers() {
        chatManager = ChatManager.getInstance(this)
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun loadIntentData() {
        meetingRecordId = intent.getStringExtra(EXTRA_MEETING_RECORD_ID) ?: ""
        meetingTitle = intent.getStringExtra(EXTRA_MEETING_TITLE) ?: "会议记录"
        meetingContent = intent.getStringExtra(EXTRA_MEETING_CONTENT) ?: ""

        if (meetingRecordId.isEmpty() || meetingContent.isEmpty()) {
            Log.e(TAG, "会议记录ID或内容为空")
            showToast("会议记录数据无效")
            finish()
            return
        }

        tvMeetingContext.text = "基于「$meetingTitle」的内容"
    }

    private fun setupRecyclerView() {
        chatAdapter = ChatMessageAdapter()
        chatAdapter.initMarkwon(this) // 初始化Markdown渲染器
        rvChatMessages.apply {
            layoutManager = LinearLayoutManager(this@AiChatActivity)
            adapter = chatAdapter

            // 移除setHasFixedSize(true)，因为它与动态内容变化冲突
            // setHasFixedSize(true)

            // 禁用change动画，避免流式更新时的闪烁问题
            (itemAnimator as? androidx.recyclerview.widget.SimpleItemAnimator)?.supportsChangeAnimations = false

            // 添加布局变化监听器来追踪可能的跳动原因
            addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                Log.d(TAG, "RecyclerView layout changed - this might cause jumping")
            }
        }
    }

    private fun setupInputListeners() {
        etMessage.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                btnSend.isEnabled = !s.isNullOrBlank() && !isProcessing
            }
        })
    }

    private fun loadChatHistory() {
        val history = chatManager.getChatHistory(meetingRecordId)
        if (history.isNotEmpty()) {
            chatAdapter.addMessages(history)
            scrollToBottom()
        } else {
            // 添加欢迎消息
            addWelcomeMessage()
        }
    }

    private fun addWelcomeMessage() {
        val welcomeMessage = ChatMessage(
            content = "您好！我是AI助手，可以基于您的会议记录内容回答问题。\n\n您可以询问：\n• 会议的主要内容\n• 重要决策和结论\n• 具体的讨论细节\n• 行动项目和任务分配\n\n请输入您的问题吧！",
            isFromUser = false,
            meetingRecordId = meetingRecordId
        )
        chatAdapter.addMessage(welcomeMessage)
        chatManager.saveChatMessage(welcomeMessage)
        scrollToBottom()
    }

    private fun sendMessage() {
        val messageText = etMessage.text.toString().trim()
        if (messageText.isEmpty() || isProcessing) return

        // 检查LLM是否可用（在协程中异步检查）
        checkLLMAvailability()

        // 添加用户消息
        val userMessage = ChatMessage(
            content = messageText,
            isFromUser = true,
            meetingRecordId = meetingRecordId
        )
        chatAdapter.addMessage(userMessage)
        chatManager.saveChatMessage(userMessage)
        scrollToBottom()

        // 清空输入框
        etMessage.text.clear()

        // 发送给AI处理
        processAiResponse(messageText)
    }

    private fun processAiResponse(userMessage: String) {
        isProcessing = true
        btnSend.isEnabled = false
        progressLoading.visibility = View.VISIBLE

        // 创建AI消息占位符
        val aiMessage = ChatMessage(
            content = "",
            isFromUser = false,
            meetingRecordId = meetingRecordId
        )
        chatAdapter.addMessage(aiMessage)
        scrollToBottom()
        
        val aiMessageIndex = chatAdapter.itemCount - 1
        val streamingManager = StreamingLLMManager(this)
        
        // 构建完整的提示词
        val fullPrompt = LLMManager.buildChatPromptForStreaming(
            userMessage = userMessage,
            originalContent = meetingContent,
            optimizedContent = "", // AiChatActivity中暂时没有优化内容，使用空字符串
            chatHistory = chatAdapter.getMessages().takeLast(10)
        )
        
        // 获取当前LLM提供商
        val currentProvider = LLMManager.getCurrentProvider(this)
        
        streamingManager.streamChat(
            provider = currentProvider,
            prompt = fullPrompt,
            callback = object : StreamingCallback {
                override fun onConnectionOpened() {
                    runOnUiThread {
                        progressLoading.visibility = View.GONE
                        Log.d(TAG, "流式连接已建立")
                    }
                }
                
                override fun onDataReceived(chunk: String) {
                    runOnUiThread {
                        // 更新AI消息内容
                        aiMessage.content += chunk
                        
                        // 添加详细日志追踪ViewHolder获取情况
                        Log.d(TAG, "onDataReceived: aiMessageIndex=$aiMessageIndex, chunk length=${chunk.length}")
                        
                        // 使用直接更新方法避免ViewHolder重新绑定导致的跳动
                        val viewHolder = chatAdapter.getViewHolderAt(rvChatMessages, aiMessageIndex)
                        if (viewHolder != null) {
                            // 直接更新TextView内容，避免notifyItemChanged
                            Log.d(TAG, "ViewHolder found, updating content directly")
                            viewHolder.updateAiMessageContent(aiMessage.content, chatAdapter)
                        } else {
                            // 如果ViewHolder不可见，使用传统方法
                            Log.d(TAG, "ViewHolder not found, using notifyItemChanged (this causes jumping!)")
                            chatAdapter.notifyItemChanged(aiMessageIndex)
                        }
                        
                        // 流式更新过程中减少滚动，只在内容较多时滚动
                        if (aiMessage.content.length % 50 == 0) {
                            Log.d(TAG, "Calling scrollToBottomIfNeededThrottled (every 50 chars)")
                            scrollToBottomIfNeededThrottled()
                        }
                    }
                }
                
                override fun onCompleted(fullContent: String) {
                    runOnUiThread {
                        isProcessing = false
                        btnSend.isEnabled = etMessage.text.isNotBlank()
                        
                        // 添加详细的调试日志
                        Log.d(TAG, "准备保存AI消息:")
                        Log.d(TAG, "  - 消息ID: ${aiMessage.id}")
                        Log.d(TAG, "  - 消息内容长度: ${aiMessage.content.length}")
                        Log.d(TAG, "  - 消息内容预览: ${aiMessage.content.take(50)}...")
                        Log.d(TAG, "  - meetingRecordId: ${aiMessage.meetingRecordId}")
                        Log.d(TAG, "  - isFromUser: ${aiMessage.isFromUser}")
                        
                        // 保存完整的AI消息
                        try {
                            chatManager.saveChatMessage(aiMessage)
                            Log.d(TAG, "AI消息保存成功")
                            
                            // 验证保存结果
                            val savedMessages = chatManager.getChatHistory(meetingRecordId)
                            Log.d(TAG, "当前聊天历史总数: ${savedMessages.size}")
                            val lastMessage = savedMessages.lastOrNull()
                            if (lastMessage != null) {
                                Log.d(TAG, "最后一条消息: ID=${lastMessage.id}, isFromUser=${lastMessage.isFromUser}, 内容长度=${lastMessage.content.length}")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "保存AI消息时发生异常", e)
                        }
                        
                        Log.d(TAG, "流式响应完成")
                        
                        // 流式更新结束，执行最终滚动
                        scrollToBottomIfNeededThrottled()
                    }
                }
                
                override fun onError(error: String) {
                    runOnUiThread {
                        progressLoading.visibility = View.GONE
                        isProcessing = false
                        btnSend.isEnabled = etMessage.text.isNotBlank()
                        
                        Log.e(TAG, "流式响应错误: $error")
                        
                        // 如果AI消息为空，显示错误信息
                        if (aiMessage.content.isEmpty()) {
                            aiMessage.content = "抱歉，我暂时无法回答您的问题。请稍后再试。\n\n错误信息：$error"

                            // 使用直接更新方法避免跳动
                            val viewHolder = chatAdapter.getViewHolderAt(rvChatMessages, aiMessageIndex)
                            if (viewHolder != null) {
                                viewHolder.updateAiMessageContent(aiMessage.content, chatAdapter)
                            } else {
                                chatAdapter.notifyItemChanged(aiMessageIndex)
                            }
                        }
                        
                        showToast("AI回复失败: $error")
                        scrollToBottomIfNeeded()
                    }
                }
                
                override fun onConnectionClosed() {
                    Log.d(TAG, "流式连接已关闭")
                }
            }
        )
    }

    private fun scrollToBottom() {
        if (chatAdapter.itemCount > 0) {
            rvChatMessages.smoothScrollToPosition(chatAdapter.itemCount - 1)
        }
    }
    
    private fun scrollToBottomIfNeeded() {
        val layoutManager = rvChatMessages.layoutManager as? LinearLayoutManager ?: return
        val itemCount = chatAdapter.itemCount
        val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
        val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
        val lastItemPosition = itemCount - 1
        
        Log.d(TAG, "scrollToBottomIfNeeded: itemCount=$itemCount, lastVisible=$lastVisiblePosition, firstVisible=$firstVisiblePosition, lastItem=$lastItemPosition")
        
        // 只有当用户接近底部时才自动滚动
        if (lastVisiblePosition >= lastItemPosition - 2) {
            Log.d(TAG, "User is near bottom, executing smoothScrollToPosition($lastItemPosition)")
            // 延迟滚动，让TextView先完成布局变化
            rvChatMessages.post {
                rvChatMessages.smoothScrollToPosition(lastItemPosition)
            }
        } else {
            Log.d(TAG, "User not near bottom, skipping scroll")
        }
    }
    
    /**
     * 节流版本的滚动到底部方法，减少频繁滚动导致的跳动
     */
    private fun scrollToBottomIfNeededThrottled() {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastScroll = currentTime - lastScrollTime
        Log.d(TAG, "scrollToBottomIfNeededThrottled: timeSinceLastScroll=${timeSinceLastScroll}ms, throttleDelay=${scrollThrottleDelay}ms")
        
        if (timeSinceLastScroll >= scrollThrottleDelay) {
            Log.d(TAG, "Throttle passed, executing scroll")
            lastScrollTime = currentTime
            scrollToBottomIfNeeded()
        } else {
            Log.d(TAG, "Throttle blocked scroll, need to wait ${scrollThrottleDelay - timeSinceLastScroll}ms more")
        }
    }

    private fun showClearChatDialog() {
        AlertDialog.Builder(this)
            .setTitle("清空聊天记录")
            .setMessage("确定要清空所有聊天记录吗？\n\n此操作不可撤销。")
            .setPositiveButton("清空") { _, _ ->
                clearChatHistory()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearChatHistory() {
        chatAdapter.clearMessages()
        chatManager.clearChatHistory(meetingRecordId)
        addWelcomeMessage()
        showToast("聊天记录已清空")
    }

    private fun showLLMConfigDialog() {
        AlertDialog.Builder(this)
            .setTitle("LLM配置")
            .setMessage("当前没有可用的LLM配置。请先在设置中配置API密钥。")
            .setPositiveButton("去设置") { _, _ ->
                // TODO: 跳转到设置页面
                showToast("请在设置中配置LLM API密钥")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 检查LLM可用性
     */
    private fun checkLLMAvailability() {
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@AiChatActivity)) {
                showLLMConfigDialog()
            }
        }
    }
}