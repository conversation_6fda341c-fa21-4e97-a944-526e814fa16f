package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 声纹数据持久化管理器
 * 负责保存和恢复声纹数据，解决应用重启后声纹丢失的问题
 */
class SpeakerDataManager(private val context: Context) {
    
    companion object {
        private const val TAG = "SpeakerDataManager"
        private const val PREFS_NAME = "speaker_data"
        private const val KEY_SPEAKERS = "speakers"
        private const val SPEAKERS_DIR = "speakers"
        private const val SAMPLE_FILE_PREFIX = "sample_"
        private const val SAMPLE_FILE_SUFFIX = ".raw"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val speakersDir: File = File(context.filesDir, SPEAKERS_DIR)
    
    init {
        // 确保声纹目录存在
        if (!speakersDir.exists()) {
            speakersDir.mkdirs()
        }
    }
    
    /**
     * 保存声纹数据
     */
    fun saveSpeaker(speakerName: String, audioSamples: List<FloatArray>): Boolean {
        return try {
            Log.i(TAG, "保存声纹数据: $speakerName, 样本数: ${audioSamples.size}")
            
            // 创建说话人目录
            val speakerDir = File(speakersDir, speakerName)
            if (!speakerDir.exists()) {
                speakerDir.mkdirs()
            }
            
            // 清空旧的样本文件
            speakerDir.listFiles()?.forEach { it.delete() }
            
            // 保存每个音频样本
            audioSamples.forEachIndexed { index, samples ->
                val sampleFile = File(speakerDir, "$SAMPLE_FILE_PREFIX$index$SAMPLE_FILE_SUFFIX")
                saveFloatArrayToFile(samples, sampleFile)
            }
            
            // 更新SharedPreferences
            updateSpeakerList(speakerName, audioSamples.size)
            
            Log.i(TAG, "声纹数据保存成功: $speakerName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存声纹数据失败: $speakerName", e)
            false
        }
    }
    
    /**
     * 加载声纹数据
     */
    fun loadSpeaker(speakerName: String): List<FloatArray>? {
        return try {
            val speakerDir = File(speakersDir, speakerName)
            if (!speakerDir.exists()) {
                Log.w(TAG, "声纹目录不存在: $speakerName")
                return null
            }
            
            val sampleFiles = speakerDir.listFiles { _, name ->
                name.startsWith(SAMPLE_FILE_PREFIX) && name.endsWith(SAMPLE_FILE_SUFFIX)
            }?.sortedBy { it.name }
            
            if (sampleFiles.isNullOrEmpty()) {
                Log.w(TAG, "没有找到声纹样本文件: $speakerName")
                return null
            }
            
            val audioSamples = mutableListOf<FloatArray>()
            sampleFiles.forEach { file ->
                val samples = loadFloatArrayFromFile(file)
                if (samples != null) {
                    audioSamples.add(samples)
                }
            }
            
            Log.i(TAG, "加载声纹数据成功: $speakerName, 样本数: ${audioSamples.size}")
            audioSamples
        } catch (e: Exception) {
            Log.e(TAG, "加载声纹数据失败: $speakerName", e)
            null
        }
    }
    
    /**
     * 删除声纹数据
     */
    fun removeSpeaker(speakerName: String): Boolean {
        return try {
            val speakerDir = File(speakersDir, speakerName)
            if (speakerDir.exists()) {
                speakerDir.deleteRecursively()
            }
            
            // 从SharedPreferences中移除
            removeSpeakerFromList(speakerName)
            
            Log.i(TAG, "删除声纹数据成功: $speakerName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "删除声纹数据失败: $speakerName", e)
            false
        }
    }
    
    /**
     * 获取所有已保存的声纹名称
     */
    fun getAllSpeakers(): Set<String> {
        return prefs.getStringSet(KEY_SPEAKERS, emptySet()) ?: emptySet()
    }
    
    /**
     * 检查声纹是否存在
     */
    fun containsSpeaker(speakerName: String): Boolean {
        return getAllSpeakers().contains(speakerName)
    }
    
    /**
     * 清空所有声纹数据
     */
    fun clearAllSpeakers(): Boolean {
        return try {
            if (speakersDir.exists()) {
                speakersDir.deleteRecursively()
                speakersDir.mkdirs()
            }
            
            prefs.edit().clear().apply()
            
            Log.i(TAG, "清空所有声纹数据成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清空声纹数据失败", e)
            false
        }
    }
    
    /**
     * 将FloatArray保存到文件
     */
    private fun saveFloatArrayToFile(samples: FloatArray, file: File) {
        FileOutputStream(file).use { fos ->
            val buffer = ByteBuffer.allocate(samples.size * 4).order(ByteOrder.LITTLE_ENDIAN)
            samples.forEach { buffer.putFloat(it) }
            fos.write(buffer.array())
        }
    }
    
    /**
     * 从文件加载FloatArray
     */
    private fun loadFloatArrayFromFile(file: File): FloatArray? {
        return try {
            FileInputStream(file).use { fis ->
                val bytes = fis.readBytes()
                val buffer = ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN)
                val samples = FloatArray(bytes.size / 4)
                for (i in samples.indices) {
                    samples[i] = buffer.getFloat()
                }
                samples
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载音频文件失败: ${file.name}", e)
            null
        }
    }
    
    /**
     * 更新声纹列表
     */
    private fun updateSpeakerList(speakerName: String, sampleCount: Int) {
        val speakers = getAllSpeakers().toMutableSet()
        speakers.add(speakerName)
        
        prefs.edit()
            .putStringSet(KEY_SPEAKERS, speakers)
            .putInt("${speakerName}_samples", sampleCount)
            .apply()
    }
    
    /**
     * 从列表中移除声纹
     */
    private fun removeSpeakerFromList(speakerName: String) {
        val speakers = getAllSpeakers().toMutableSet()
        speakers.remove(speakerName)
        
        prefs.edit()
            .putStringSet(KEY_SPEAKERS, speakers)
            .remove("${speakerName}_samples")
            .apply()
    }
    
    /**
     * 获取声纹样本数量
     */
    fun getSpeakerSampleCount(speakerName: String): Int {
        return prefs.getInt("${speakerName}_samples", 0)
    }
}
