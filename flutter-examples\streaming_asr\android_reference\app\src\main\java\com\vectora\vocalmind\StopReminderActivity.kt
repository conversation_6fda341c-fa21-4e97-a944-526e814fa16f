package com.vectora.vocalmind

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log

/**
 * 停止提醒响铃的透明Activity
 * 用于在用户点击通知时立即停止响铃，然后跳转到目标页面
 */
class StopReminderActivity : Activity() {
    
    companion object {
        private const val TAG = "StopReminderActivity"
        const val EXTRA_TARGET_TODO_ID = "target_todo_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "停止响铃Activity启动")
        
        // 立即停止响铃
        TodoNotificationHelper.stopCurrentRingtone()
        
        // 获取目标TODO ID
        val todoId = intent.getStringExtra(EXTRA_TARGET_TODO_ID)
        
        if (todoId != null) {
            // 跳转到TODO编辑页面
            val editIntent = Intent(this, TodoEditActivity::class.java).apply {
                putExtra("todo_id", todoId)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            startActivity(editIntent)
        } else {
            // 如果没有TODO ID，跳转到TODO列表
            val listIntent = Intent(this, TodoActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            startActivity(listIntent)
        }
        
        // 立即结束当前Activity
        finish()
    }
}
