<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background_apple"
    android:padding="0dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="20dp"
        android:background="@color/apple_system_background">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="🤖 AI会议总结"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/apple_label"
            android:gravity="center" />

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close_apple"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/apple_gray_5" />

    <!-- 内容区域 -->
    <ScrollView
        android:id="@+id/scroll_view_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="vertical"
        android:fadeScrollbars="false"
        android:background="@color/apple_system_background">

        <TextView
            android:id="@+id/tv_summary_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="20dp"
            android:textSize="15sp"
            android:textColor="@color/apple_label"
            android:lineSpacingExtra="4dp"
            android:textIsSelectable="true" />

    </ScrollView>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/apple_gray_5" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/apple_system_background">

        <Button
            android:id="@+id/btn_copy_original"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="复制原文"
            android:textSize="16sp"
            android:textColor="@color/apple_blue"
            android:background="?android:attr/selectableItemBackground"
            style="?android:attr/borderlessButtonStyle" />

        <!-- 垂直分割线 -->
        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/apple_gray_5" />

        <Button
            android:id="@+id/btn_copy_summary"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="复制总结"
            android:textSize="16sp"
            android:textColor="@color/apple_blue"
            android:textStyle="bold"
            android:background="?android:attr/selectableItemBackground"
            style="?android:attr/borderlessButtonStyle" />

    </LinearLayout>

</LinearLayout>