package com.vectora.vocalmind.server

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

/**
 * 服务器配置管理器
 * 管理服务器连接配置、用户偏好等
 */
object ServerConfigManager {
    private const val TAG = "ServerConfigManager"
    private const val PREFS_NAME = "server_config_prefs"
    
    // 配置键名
    private const val KEY_SERVER_URL = "server_url"
    private const val KEY_USE_SERVER_MODE = "use_server_mode"
    private const val KEY_CONNECTION_TIMEOUT = "connection_timeout"
    private const val KEY_READ_TIMEOUT = "read_timeout"
    private const val KEY_AUTO_RETRY = "auto_retry"
    private const val KEY_MAX_RETRY_COUNT = "max_retry_count"
    
    // 默认配置
    private const val DEFAULT_SERVER_URL = "http://192.168.1.163:8024"
    private const val DEFAULT_CONNECTION_TIMEOUT = 30000L // 30秒
    private const val DEFAULT_READ_TIMEOUT = 60000L // 60秒
    private const val DEFAULT_MAX_RETRY_COUNT = 3
    
    /**
     * 获取加密的SharedPreferences
     */
    private fun getSecurePrefs(context: Context): SharedPreferences {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
        
        return EncryptedSharedPreferences.create(
            context,
            PREFS_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    /**
     * 设置服务器URL
     */
    fun setServerUrl(context: Context, url: String): Boolean {
        return try {
            val cleanUrl = url.trim().removeSuffix("/")
            getSecurePrefs(context).edit()
                .putString(KEY_SERVER_URL, cleanUrl)
                .apply()
            Log.d(TAG, "服务器URL已设置: $cleanUrl")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置服务器URL失败", e)
            false
        }
    }
    
    /**
     * 获取服务器URL
     */
    fun getServerUrl(context: Context): String {
        return try {
            getSecurePrefs(context).getString(KEY_SERVER_URL, DEFAULT_SERVER_URL) ?: DEFAULT_SERVER_URL
        } catch (e: Exception) {
            Log.e(TAG, "获取服务器URL失败", e)
            DEFAULT_SERVER_URL
        }
    }
    
    /**
     * 设置是否使用服务器模式
     */
    fun setUseServerMode(context: Context, useServer: Boolean): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putBoolean(KEY_USE_SERVER_MODE, useServer)
                .apply()
            Log.d(TAG, "服务器模式设置: $useServer")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置服务器模式失败", e)
            false
        }
    }
    
    /**
     * 是否使用服务器模式
     */
    fun isUseServerMode(context: Context): Boolean {
        return try {
            getSecurePrefs(context).getBoolean(KEY_USE_SERVER_MODE, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取服务器模式设置失败", e)
            false
        }
    }
    
    /**
     * 设置连接超时时间
     */
    fun setConnectionTimeout(context: Context, timeout: Long): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putLong(KEY_CONNECTION_TIMEOUT, timeout)
                .apply()
            Log.d(TAG, "连接超时时间设置: ${timeout}ms")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置连接超时时间失败", e)
            false
        }
    }
    
    /**
     * 获取连接超时时间
     */
    fun getConnectionTimeout(context: Context): Long {
        return try {
            getSecurePrefs(context).getLong(KEY_CONNECTION_TIMEOUT, DEFAULT_CONNECTION_TIMEOUT)
        } catch (e: Exception) {
            Log.e(TAG, "获取连接超时时间失败", e)
            DEFAULT_CONNECTION_TIMEOUT
        }
    }
    
    /**
     * 设置读取超时时间
     */
    fun setReadTimeout(context: Context, timeout: Long): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putLong(KEY_READ_TIMEOUT, timeout)
                .apply()
            Log.d(TAG, "读取超时时间设置: ${timeout}ms")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置读取超时时间失败", e)
            false
        }
    }
    
    /**
     * 获取读取超时时间
     */
    fun getReadTimeout(context: Context): Long {
        return try {
            getSecurePrefs(context).getLong(KEY_READ_TIMEOUT, DEFAULT_READ_TIMEOUT)
        } catch (e: Exception) {
            Log.e(TAG, "获取读取超时时间失败", e)
            DEFAULT_READ_TIMEOUT
        }
    }
    
    /**
     * 设置是否自动重试
     */
    fun setAutoRetry(context: Context, autoRetry: Boolean): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putBoolean(KEY_AUTO_RETRY, autoRetry)
                .apply()
            Log.d(TAG, "自动重试设置: $autoRetry")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置自动重试失败", e)
            false
        }
    }
    
    /**
     * 是否自动重试
     */
    fun isAutoRetry(context: Context): Boolean {
        return try {
            getSecurePrefs(context).getBoolean(KEY_AUTO_RETRY, true)
        } catch (e: Exception) {
            Log.e(TAG, "获取自动重试设置失败", e)
            true
        }
    }
    
    /**
     * 设置最大重试次数
     */
    fun setMaxRetryCount(context: Context, count: Int): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putInt(KEY_MAX_RETRY_COUNT, count)
                .apply()
            Log.d(TAG, "最大重试次数设置: $count")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设置最大重试次数失败", e)
            false
        }
    }
    
    /**
     * 获取最大重试次数
     */
    fun getMaxRetryCount(context: Context): Int {
        return try {
            getSecurePrefs(context).getInt(KEY_MAX_RETRY_COUNT, DEFAULT_MAX_RETRY_COUNT)
        } catch (e: Exception) {
            Log.e(TAG, "获取最大重试次数失败", e)
            DEFAULT_MAX_RETRY_COUNT
        }
    }
    
    /**
     * 获取完整的API基础URL
     */
    fun getApiBaseUrl(context: Context): String {
        val serverUrl = getServerUrl(context)
        return "$serverUrl/api/v1"
    }
    
    /**
     * 重置所有配置为默认值
     */
    fun resetToDefaults(context: Context): Boolean {
        return try {
            getSecurePrefs(context).edit()
                .putString(KEY_SERVER_URL, DEFAULT_SERVER_URL)
                .putBoolean(KEY_USE_SERVER_MODE, true)
                .putLong(KEY_CONNECTION_TIMEOUT, DEFAULT_CONNECTION_TIMEOUT)
                .putLong(KEY_READ_TIMEOUT, DEFAULT_READ_TIMEOUT)
                .putBoolean(KEY_AUTO_RETRY, true)
                .putInt(KEY_MAX_RETRY_COUNT, DEFAULT_MAX_RETRY_COUNT)
                .apply()
            Log.d(TAG, "配置已重置为默认值")
            true
        } catch (e: Exception) {
            Log.e(TAG, "重置配置失败", e)
            false
        }
    }
    
    /**
     * 验证服务器URL格式
     */
    fun isValidServerUrl(url: String): Boolean {
        return try {
            val cleanUrl = url.trim()
            cleanUrl.startsWith("http://") || cleanUrl.startsWith("https://")
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取配置摘要（用于调试）
     */
    fun getConfigSummary(context: Context): String {
        return try {
            """
            服务器配置摘要:
            - 服务器URL: ${getServerUrl(context)}
            - 使用服务器模式: ${isUseServerMode(context)}
            - 连接超时: ${getConnectionTimeout(context)}ms
            - 读取超时: ${getReadTimeout(context)}ms
            - 自动重试: ${isAutoRetry(context)}
            - 最大重试次数: ${getMaxRetryCount(context)}
            """.trimIndent()
        } catch (e: Exception) {
            "获取配置摘要失败: ${e.message}"
        }
    }
}
