package com.vectora.vocalmind

import android.app.*
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 悬浮窗服务
 * 管理悬浮窗的生命周期，处理与录音服务的状态同步
 */
class FloatingWindowService : Service(), FloatingWindowView.FloatingWindowCallback {

    companion object {
        private const val TAG = "FloatingWindowService"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "floating_window_channel"
    }

    // Binder for Activity communication
    private val binder = FloatingWindowBinder()
    
    // 悬浮窗相关
    private var windowManager: WindowManager? = null
    private var floatingWindowView: FloatingWindowView? = null
    private val isFloatingWindowShowing = AtomicBoolean(false)
    
    // 录音服务连接
    private var audioRecordingService: AudioRecordingService? = null
    private var isAudioServiceBound = false
    
    // 录音状态同步
    private var isRecording = false
    
    inner class FloatingWindowBinder : Binder() {
        fun getService(): FloatingWindowService = this@FloatingWindowService
    }
    
    // 录音服务连接管理
    private val audioServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "AudioRecordingService connected to FloatingWindowService")
            val binder = service as AudioRecordingService.AudioRecordingBinder
            audioRecordingService = binder.getService()
            isAudioServiceBound = true
            
            // 同步当前录音状态
            syncRecordingState()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "AudioRecordingService disconnected from FloatingWindowService")
            audioRecordingService = null
            isAudioServiceBound = false
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "FloatingWindowService created")
        
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
        
        // 绑定录音服务
        bindAudioRecordingService()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "FloatingWindowService started")
        
        when (intent?.action) {
            "SHOW_FLOATING_WINDOW" -> {
                showFloatingWindow()
            }
            "HIDE_FLOATING_WINDOW" -> {
                hideFloatingWindow()
            }
            "UPDATE_RECORDING_STATE" -> {
                val recording = intent.getBooleanExtra("recording", false)
                updateRecordingState(recording)
            }
        }
        
        return START_NOT_STICKY
    }

    override fun onBind(intent: Intent?): IBinder {
        return binder
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "FloatingWindowService destroyed")
        
        hideFloatingWindow()
        unbindAudioRecordingService()
    }

    /**
     * 显示悬浮窗
     */
    fun showFloatingWindow() {
        if (isFloatingWindowShowing.get()) {
            Log.d(TAG, "悬浮窗已经显示")
            return
        }

        try {
            // 检查权限
            if (!FloatingWindowPermissionManager.hasOverlayPermission(this)) {
                Log.w(TAG, "没有悬浮窗权限，无法显示悬浮窗")
                return
            }

            // 创建悬浮窗视图
            floatingWindowView = FloatingWindowView(this, windowManager!!, this)
            
            // 添加到窗口管理器
            windowManager?.addView(floatingWindowView, floatingWindowView!!.getWindowParams())
            
            isFloatingWindowShowing.set(true)
            
            // 启动前台服务
            startForegroundService()
            
            // 同步录音状态
            syncRecordingState()
            
            Log.i(TAG, "悬浮窗显示成功")

        } catch (e: Exception) {
            Log.e(TAG, "显示悬浮窗失败", e)
            isFloatingWindowShowing.set(false)
        }
    }

    /**
     * 隐藏悬浮窗
     */
    fun hideFloatingWindow() {
        if (!isFloatingWindowShowing.get()) {
            Log.d(TAG, "悬浮窗已经隐藏")
            return
        }

        try {
            floatingWindowView?.let { view ->
                view.cleanup()
                windowManager?.removeView(view)
            }
            floatingWindowView = null
            isFloatingWindowShowing.set(false)
            
            // 停止前台服务
            stopForeground(true)
            stopSelf()
            
            Log.i(TAG, "悬浮窗隐藏成功")

        } catch (e: Exception) {
            Log.e(TAG, "隐藏悬浮窗失败", e)
        }
    }

    /**
     * 更新录音状态
     */
    fun updateRecordingState(recording: Boolean) {
        isRecording = recording
        floatingWindowView?.updateRecordingState(recording)
        Log.d(TAG, "更新悬浮窗录音状态: $recording")
    }

    /**
     * 同步录音状态
     */
    private fun syncRecordingState() {
        if (isAudioServiceBound && audioRecordingService != null) {
            val currentRecording = audioRecordingService!!.isRecording()
            updateRecordingState(currentRecording)
        }
    }

    /**
     * 绑定录音服务
     */
    private fun bindAudioRecordingService() {
        try {
            val intent = Intent(this, AudioRecordingService::class.java)
            val success = bindService(intent, audioServiceConnection, Context.BIND_AUTO_CREATE)
            if (success) {
                Log.i(TAG, "正在绑定AudioRecordingService")
            } else {
                Log.e(TAG, "绑定AudioRecordingService失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "绑定AudioRecordingService异常", e)
        }
    }

    /**
     * 解绑录音服务
     */
    private fun unbindAudioRecordingService() {
        try {
            if (isAudioServiceBound) {
                unbindService(audioServiceConnection)
                isAudioServiceBound = false
                audioRecordingService = null
                Log.i(TAG, "AudioRecordingService已解绑")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑AudioRecordingService异常", e)
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE)
        } else {
            startForeground(NOTIFICATION_ID, notification)
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "悬浮窗服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "悬浮录音按钮服务"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, SingleModelActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("悬浮录音")
            .setContentText("悬浮录音按钮正在运行")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }

    // FloatingWindowView.FloatingWindowCallback 实现
    override fun onToggleRecording() {
        // 通过广播通知主应用切换录音状态
        val intent = Intent("com.vectora.vocalmind.TOGGLE_RECORDING")
        sendBroadcast(intent)
        Log.d(TAG, "发送切换录音状态广播")
    }

    override fun onOpenMainApp() {
        try {
            val intent = Intent(this, SingleModelActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
            startActivity(intent)
            Log.d(TAG, "打开主应用")
        } catch (e: Exception) {
            Log.e(TAG, "打开主应用失败", e)
        }
    }

    override fun onCloseFloatingWindow() {
        hideFloatingWindow()
    }

    /**
     * 检查悬浮窗是否显示
     */
    fun isFloatingWindowShowing(): Boolean = isFloatingWindowShowing.get()
}
