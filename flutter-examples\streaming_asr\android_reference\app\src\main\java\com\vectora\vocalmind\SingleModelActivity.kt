package com.vectora.vocalmind

import android.Manifest
import android.content.BroadcastReceiver
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.OpenableColumns
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
// import androidx.appcompat.app.AlertDialog // 已替换为苹果风格自定义弹窗
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import android.widget.FrameLayout
import android.widget.ImageView
import android.view.animation.AnimationUtils
import kotlinx.coroutines.*
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

// FloatingWindow相关导入
import com.vectora.vocalmind.FloatingWindowService
import com.vectora.vocalmind.FloatingWindowPermissionManager
import com.vectora.vocalmind.FloatingWindowSettings

/**
 * ASR Activity - Tab布局版本
 * 重构为Tab布局，包含会议记录、录音、待办三个Tab
 * 保持单模型ASR架构，但UI改为Tab形式
 */
class SingleModelActivity : AppCompatActivity(), SingleModelASREngine.ASRListener {

    companion object {
        private const val TAG = "SingleModelActivity"
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val SAMPLE_RATE = 16000
    }

    private val permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    // 自定义底部导航相关UI组件
    private lateinit var viewPager: ViewPager2
    private lateinit var tabsAdapter: MainTabsAdapter
    private lateinit var customBottomNav: CustomBottomNavigation

    // ASR引擎
    private lateinit var asrEngine: SingleModelASREngine
    private var isInitialized = false

    // 自动优化设置
    private var autoOptimize = true

    // 会议记录管理器
    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var audioRecordingManager: AudioRecordingManager

    // 当前录音文件路径
    private var currentAudioFilePath: String? = null

    // 音频录制服务相关
    private var audioRecordingService: AudioRecordingService? = null
    private var isServiceBound = false
    private val isRecording = AtomicBoolean(false)

    // 悬浮窗服务相关
    private var floatingWindowService: FloatingWindowService? = null
    private var isFloatingServiceBound = false

    // 结果管理
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private var recognitionCount = 0
    private var sessionStartTime = 0L
    private val timeHandler = Handler(Looper.getMainLooper())
    private var timeUpdateRunnable: Runnable? = null

    // 当前会话的会议记录ID（用于更新优化内容和总结）
    private var currentMeetingRecordId: String? = null

    // 实时预览状态管理
    private var isShowingPreview = false
    private var currentPreviewText = ""
    private var baseResultsText = ""
    private var previewTimestamp = ""

    // 广播接收器 - 接收悬浮窗的录音切换指令
    private val recordingToggleReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.vectora.vocalmind.TOGGLE_RECORDING") {
                Log.d(TAG, "收到悬浮窗录音切换指令")
                toggleRecording()
            }
        }
    }
    
    // 悬浮窗服务连接管理
    private val floatingServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "FloatingWindowService connected")
            val binder = service as FloatingWindowService.FloatingWindowBinder
            floatingWindowService = binder.getService()
            isFloatingServiceBound = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "FloatingWindowService disconnected")
            floatingWindowService = null
            isFloatingServiceBound = false
        }
    }

    // 音频录制服务连接管理
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "AudioRecordingService connected")
            val binder = service as AudioRecordingService.AudioRecordingBinder
            audioRecordingService = binder.getService()
            isServiceBound = true
            
            // 设置回调和依赖
            audioRecordingService?.setCallback(audioRecordingCallback)
            audioRecordingService?.setAsrEngine(asrEngine)
            audioRecordingService?.setAudioRecordingManager(audioRecordingManager)
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "AudioRecordingService disconnected")
            audioRecordingService = null
            isServiceBound = false
        }
    }
    
    // 音频录制回调
    private val audioRecordingCallback = object : AudioRecordingService.AudioRecordingCallback {
        override fun onAudioData(audioData: FloatArray) {
            // 音频数据已经在Service中处理，这里可以做额外处理（如果需要）
        }
        
        override fun onRecordingStarted() {
            runOnUiThread {
                isRecording.set(true)
                sessionStartTime = System.currentTimeMillis()

                // 录音时保持屏幕常亮
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                // 通知RecordingFragment更新UI
                getRecordingFragment()?.updateRecordingState(true)
                startTimeUpdate()

                // 底部导航录音状态由CustomBottomNavigation自动处理

                // 通知悬浮窗服务更新状态
                notifyFloatingWindowRecordingState(true)

                Log.i(TAG, "录音已开始，屏幕保持常亮")
                showToast("开始录音")
            }
        }
        
        override fun onRecordingStopped(audioFilePath: String?) {
            runOnUiThread {
                isRecording.set(false)

                // 录音停止时清除屏幕常亮
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                // 保存录音文件路径
                currentAudioFilePath = audioFilePath
                if (audioFilePath != null) {
                    Log.i(TAG, "录音文件保存完成: $audioFilePath")
                } else {
                    Log.w(TAG, "录音文件保存失败")
                }

                // 停止时间更新
                stopTimeUpdate()

                // 通知悬浮窗服务更新状态
                notifyFloatingWindowRecordingState(false)

                // 底部导航录音状态由CustomBottomNavigation自动处理

                // 自动优化ASR结果
                if (autoOptimize && recognitionResults.isNotEmpty()) {
                    autoOptimizeAsrContent()
                }

                // 自动保存会议记录
                if (recognitionResults.isNotEmpty()) {
                    autoSaveMeetingRecord()
                }

                // 通知RecordingFragment更新UI
                getRecordingFragment()?.updateRecordingState(false)

                Log.i(TAG, "录音已停止，屏幕常亮已清除")
                showToast("录音已停止")
            }
        }
        
        override fun onError(error: String) {
            runOnUiThread {
                Log.e(TAG, "录音服务错误: $error")
                showToast("录音错误: $error")
                
                // 重置状态
                isRecording.set(false)

                // 录音错误时清除屏幕常亮
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

                // 底部导航录音状态由CustomBottomNavigation自动处理
                // 通知RecordingFragment更新UI
                getRecordingFragment()?.updateRecordingState(false)
            }
        }
    }

    // 文件选择器
    private val audioFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { handleSelectedAudioFile(it) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.i(TAG, "开始创建SingleModelActivity")

            // 使用Tab布局
            setContentView(R.layout.activity_voice_assistant)

            // 请求权限
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

            // 初始化UI
            initViews()
            setupTabs()

            // 初始化会议记录管理器
            initMeetingRecordManager()

            // 加载设置
            loadSettings()

            // 初始化ASR引擎
            initASREngine()

            // 绑定音频录制服务
            bindAudioRecordingService()

            // 注册广播接收器
            registerRecordingToggleReceiver()

            // 绑定悬浮窗服务
            bindFloatingWindowService()

            // 检查是否需要自动启动悬浮窗
            checkAutoStartFloatingWindow()

            // 处理导入音频的意图
            handleImportIntent()

            Log.i(TAG, "SingleModelActivity创建成功")

        } catch (e: Exception) {
            Log.e(TAG, "Activity创建失败", e)
            showToast("创建失败: ${e.message}")

            // 如果初始化失败，退出应用
            finish()
        }
    }

    /**
     * 处理导入音频的意图
     */
    private fun handleImportIntent() {
        try {
            val action = intent.getStringExtra("action")
            if (action == "import_audio") {
                // 延迟执行，确保UI完全初始化
                Handler(Looper.getMainLooper()).postDelayed({
                    importAudioFile()
                }, 500)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理导入意图失败", e)
        }
    }

    private fun initViews() {
        try {
            // 初始化基础UI组件
            viewPager = findViewById(R.id.view_pager)

            // 初始化自定义底部导航
            customBottomNav = CustomBottomNavigation(this)
            customBottomNav.initialize(findViewById(R.id.custom_bottom_nav))

            Log.i(TAG, "UI初始化成功")

        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
            showToast("UI初始化失败: ${e.message}")
        }
    }

    private fun setupTabs() {
        try {
            // 创建Tab适配器
            tabsAdapter = MainTabsAdapter(this)
            viewPager.adapter = tabsAdapter

            // 设置ViewPager页面变化监听器，实现与底部导航的双向绑定
            viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // 当ViewPager页面变化时，同步更新底部导航状态
                    customBottomNav.selectTab(position, true)
                }
            })

            // 设置自定义底部导航的Tab选择监听器
            customBottomNav.setOnTabSelectedListener { tabIndex ->
                viewPager.currentItem = tabIndex
            }

            // 设置默认显示第二个Tab（录音）
            viewPager.currentItem = MainTabsAdapter.TAB_RECORDING
            customBottomNav.selectTab(MainTabsAdapter.TAB_RECORDING, false)

            Log.i(TAG, "Tab设置成功")

        } catch (e: Exception) {
            Log.e(TAG, "Tab设置失败", e)
            showToast("Tab设置失败: ${e.message}")
        }
    }





    /**
     * 初始化会议记录管理器
     */
    private fun initMeetingRecordManager() {
        try {
            meetingRecordManager = MeetingRecordManager.getInstance(this)
            audioRecordingManager = AudioRecordingManager(this)
            Log.d(TAG, "会议记录管理器初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "会议记录管理器初始化失败", e)
        }
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        try {
            // 从LLMApiKeyManager加载自动优化设置
            autoOptimize = LLMApiKeyManager.getAutoOptimize(this)
            Log.d(TAG, "自动优化设置已加载: $autoOptimize")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            // 使用默认值
            autoOptimize = true
        }
    }

    /**
     * 打开设置页面 - 供Fragment调用
     */
    fun openSettings() {
        try {
            val intent = android.content.Intent(this, SettingsActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开设置页面失败", e)
            showToast("打开设置页面失败: ${e.message}")
        }
    }

    // ==================== 公共方法供Fragment调用 ====================

    /**
     * 切换录音状态 - 供RecordingFragment调用
     */
    fun toggleRecording() {
        if (!isInitialized) {
            showToast("未初始化完成")
            return
        }

        if (!isRecording.get()) {
            startRecording()
        } else {
            stopRecording()
        }
    }

    /**
     * 获取当前录音状态 - 供RecordingFragment调用
     */
    fun isRecording(): Boolean {
        return isRecording.get()
    }

    /**
     * 处理选中的音频文件 - 供RecordingFragment调用
     */
    fun handleSelectedAudioFile(uri: Uri) {
        try {
            // 获取文件信息
            val fileName = getFileName(uri)
            val fileSize = getFileSize(uri)

            Log.i(TAG, "选中音频文件: $fileName, 大小: ${fileSize / 1024 / 1024}MB")

            // 检查文件大小（限制为100MB）
            if (fileSize > 100 * 1024 * 1024) {
                showToast("文件过大，请选择小于100MB的音频文件")
                return
            }

            // 显示确认对话框
            AppleInfoDialog(
                context = this,
                title = "确认转录",
                message = "文件名: $fileName\n大小: ${String.format("%.1f", fileSize / 1024.0 / 1024.0)} MB\n\n确定要转录这个音频文件吗？",
                positiveButtonText = "开始转录",
                negativeButtonText = "取消",
                onPositiveClick = {
                    startAudioFileTranscription(uri, fileName)
                }
            ).show()

        } catch (e: Exception) {
            Log.e(TAG, "处理音频文件失败", e)
            showToast("处理音频文件失败: ${e.message}")
        }
    }

    /**
     * 切换悬浮窗 - 供RecordingFragment调用
     */
    fun toggleFloatingWindow() {
        try {
            if (!FloatingWindowPermissionManager.hasOverlayPermission(this)) {
                FloatingWindowPermissionManager.requestOverlayPermission(this)
                return
            }

            if (isFloatingServiceBound && floatingWindowService != null) {
                if (floatingWindowService!!.isFloatingWindowShowing()) {
                    floatingWindowService!!.hideFloatingWindow()
                    showToast("悬浮窗已隐藏")
                } else {
                    floatingWindowService!!.showFloatingWindow()
                    showToast("悬浮窗已显示")
                }
            } else {
                showToast("悬浮窗服务未就绪")
            }
        } catch (e: Exception) {
            Log.e(TAG, "切换悬浮窗失败", e)
            showToast("切换悬浮窗失败: ${e.message}")
        }
    }

    /**
     * 确保会议记录已保存 - 供RecordingFragment调用
     */
    fun ensureMeetingRecordSaved() {
        try {
            // 如果已经有会议记录ID，直接返回
            if (currentMeetingRecordId != null) {
                Log.d(TAG, "会议记录已存在: $currentMeetingRecordId")
                return
            }

            val originalContent = recognitionResults.toString()
            if (originalContent.trim().isEmpty()) {
                Log.d(TAG, "识别结果为空，无法保存会议记录")
                return
            }

            // 计算录音时长
            val duration = if (sessionStartTime > 0) {
                (System.currentTimeMillis() - sessionStartTime) / 1000
            } else 0

            // 获取说话人数量（从ASR引擎）
            val speakerCount = try {
                asrEngine.getSpeakerCount()
            } catch (e: Exception) {
                0
            }

            // 创建会议记录
            val meetingRecord = meetingRecordManager.createMeetingRecord(
                originalContent = originalContent,
                wordCount = wordCount,
                duration = duration,
                speakerCount = speakerCount,
                audioFilePath = currentAudioFilePath ?: ""
            )

            // 保存记录
            val success = meetingRecordManager.saveMeetingRecord(meetingRecord)
            if (success) {
                // 保存当前会议记录ID
                currentMeetingRecordId = meetingRecord.id
                Log.i(TAG, "会议记录保存成功: ${meetingRecord.title}")

                // 标题和标签生成已统一到MeetingRecordManager中
            } else {
                Log.w(TAG, "会议记录保存失败")
            }

        } catch (e: Exception) {
            Log.e(TAG, "确保会议记录保存异常", e)
        }
    }

    /**
     * 打开会议详情页面 - 供RecordingFragment调用
     */
    fun openMeetingDetailPage() {
        try {
            val recordId = currentMeetingRecordId
            if (recordId == null) {
                showToast("会议记录未保存，无法打开详情页面")
                return
            }

            val intent = Intent(this, MeetingDetailActivity::class.java)
            intent.putExtra("meeting_record_id", recordId)
            startActivity(intent)

            Log.i(TAG, "打开会议详情页面: $recordId")

        } catch (e: Exception) {
            Log.e(TAG, "打开会议详情页面失败", e)
            showToast("打开会议详情页面失败: ${e.message}")
        }
    }

    /**
     * 打开TODO列表页面 - 供RecordingFragment调用
     */
    fun openTodoList() {
        try {
            val intent = Intent(this, TodoActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开TODO列表页面失败", e)
            showToast("打开TODO列表页面失败: ${e.message}")
        }
    }

    /**
     * 基于当前转录内容生成TODO（如果有内容的话） - 供RecordingFragment调用
     */
    fun generateTodoFromCurrentContent() {
        if (recognitionResults.isEmpty()) {
            showToast("暂无转录内容，无法生成TODO")
            return
        }

        try {
            // 确保会议记录已保存
            ensureMeetingRecordSaved()

            val recordId = currentMeetingRecordId
            if (recordId != null) {
                // 使用TodoGenerator生成TODO
                lifecycleScope.launch {
                    try {
                        if (!LLMManager.isCurrentLLMAvailable(this@SingleModelActivity)) {
                            showLLMConfigDialog()
                            return@launch
                        }

                        val todoGenerator = TodoGenerator(this@SingleModelActivity)
                        val todoManager = TodoManager.getInstance(this@SingleModelActivity)

                        val todoItems = todoGenerator.generateTodosFromMeeting(
                            originalContent = recognitionResults.toString(),
                            optimizedContent = "", // SingleModelActivity中暂时没有优化内容
                            meetingRecordId = recordId,
                            meetingTitle = "语音转录 - ${SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())}"
                        )

                        runOnUiThread {
                            if (todoItems.isNotEmpty()) {
                                val success = todoManager.addTodos(todoItems)
                                if (success) {
                                    showToast("✅ 成功生成 ${todoItems.size} 个待办事项")
                                    // 打开TODO列表查看生成的项目
                                    openTodoList()
                                } else {
                                    showToast("保存TODO失败")
                                }
                            } else {
                                showToast("未能从转录内容中识别出待办事项")
                            }
                        }

                    } catch (e: Exception) {
                        runOnUiThread {
                            Log.e(TAG, "生成TODO失败", e)
                            showToast("生成TODO失败: ${e.message}")
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "生成TODO失败", e)
            showToast("生成TODO失败: ${e.message}")
        }
    }

    /**
     * 启动AI聊天页面
     */
    private fun startAiChat() {
        try {
            // 检查是否有转录内容
            if (recognitionResults.isEmpty()) {
                showToast("请先录音或导入音频文件获取转录内容")
                return
            }

            val intent = Intent(this, AiChatActivity::class.java)
            
            // 如果有当前会议记录ID，使用它
            if (currentMeetingRecordId != null) {
                intent.putExtra("meeting_record_id", currentMeetingRecordId)
            }
            
            // 传递会议标题和内容
            val currentTime = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(Date())
            intent.putExtra("meeting_title", "语音转录 - $currentTime")
            intent.putExtra("meeting_content", recognitionResults.toString())
            
            startActivityForResult(intent, 1001)
            
        } catch (e: Exception) {
            Log.e(TAG, "启动AI聊天页面失败", e)
            showToast("启动AI聊天页面失败: ${e.message}")
        }
    }

    /**
     * 导入音频文件
     */
    private fun importAudioFile() {
        try {
            // 检查是否正在录音
            if (isRecording.get()) {
                showToast("请先停止当前录音")
                return
            }

            // 显示文件格式说明对话框
            AppleInfoDialog(
                context = this,
                title = "📁 导入音频文件",
                message = "支持的音频格式：\n• WAV (推荐)\n• MP3 (常用格式)\n• M4A / AAC\n\n注意：\n• 文件大小限制：100MB以内\n• 非WAV格式将自动转换\n• 转录包含完整的声纹识别功能\n• 结果将自动保存到会议记录",
                positiveButtonText = "选择音频文件",
                negativeButtonText = "取消",
                onPositiveClick = {
                    launchAudioFilePicker()
                }
            ).show()

        } catch (e: Exception) {
            Log.e(TAG, "导入音频文件失败", e)
            showToast("导入音频文件失败: ${e.message}")
        }
    }

    /**
     * 启动音频文件选择器
     */
    private fun launchAudioFilePicker() {
        try {
            // 支持多种音频格式
            audioFilePickerLauncher.launch("audio/*")
        } catch (e: Exception) {
            Log.e(TAG, "启动文件选择器失败", e)
            showToast("启动文件选择器失败: ${e.message}")
        }
    }

    // 私有的handleSelectedAudioFile方法已移除，使用公共版本

    /**
     * 初始化
     */
    private fun initASREngine() {
        Thread {
            try {
                // 初始化ASR引擎，传入Context以支持声纹持久化
                asrEngine = SingleModelASREngine(assets, SAMPLE_RATE, context = this@SingleModelActivity)
                asrEngine.setListener(this@SingleModelActivity)

                val success = asrEngine.initialize()

                runOnUiThread {
                    if (success) {
                        isInitialized = true
                        Log.i(TAG, "初始化成功")

                        // 显示恢复的声纹数量
                        val speakerCount = asrEngine.getSpeakerCount()
                        if (speakerCount > 0) {
                            showToast("已恢复 $speakerCount 个声纹")
                            Log.i(TAG, "已恢复 $speakerCount 个声纹")
                        }
                    } else {
                        showToast("初始化失败")
                        Log.e(TAG, "初始化失败")
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "初始化异常", e)
                runOnUiThread {
                    showToast("初始化失败: ${e.message}")
                }
            }
        }.start()
    }
    
    // 私有的toggleRecording方法已移除，使用公共版本
    
    /**
     * 开始录音
     */
    private fun startRecording() {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            showToast("需要录音权限")
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)
            return
        }
        
        // 检查Service是否已绑定
        if (!isServiceBound || audioRecordingService == null) {
            Log.e(TAG, "音频录制服务未绑定")
            showToast("音频录制服务未就绪，请稍后重试")
            return
        }
        
        try {
            // 自动清理转录结果
            Log.d(TAG, "startRecording: 自动清理转录结果")
            recognitionResults.clear()
            baseResultsText = ""
            isShowingPreview = false
            currentPreviewText = ""
            previewTimestamp = ""
            currentMeetingRecordId = null // 重置会议记录ID
            currentAudioFilePath = null // 重置录音文件路径
            
            wordCount = 0
            recognitionCount = 0
            
            // 重置ASR引擎状态
            asrEngine.reset()
            
            // 生成会议ID
            val tempMeetingId = UUID.randomUUID().toString().substring(0, 8)
            
            // 通过Service开始录音
            val success = audioRecordingService!!.startRecording(tempMeetingId)
            if (!success) {
                Log.e(TAG, "启动录音服务失败")
                showToast("启动录音失败")
                return
            }
            
            Log.i(TAG, "开始录音 - 单模型模式")
            
        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            showToast("录音失败: ${e.message}")
        }
    }
    
    /**
     * 停止录音
     */
    private fun stopRecording() {
        // 检查Service是否已绑定
        if (!isServiceBound || audioRecordingService == null) {
            Log.w(TAG, "音频录制服务未绑定，无法停止录音")
            return
        }
        
        try {
            // 通过Service停止录音
            val finalAudioFilePath = audioRecordingService!!.stopRecording()
            if (finalAudioFilePath != null) {
                currentAudioFilePath = finalAudioFilePath
                Log.i(TAG, "录音文件保存完成: $finalAudioFilePath")
            } else {
                Log.w(TAG, "录音文件保存失败")
                currentAudioFilePath = null
            }
            
            Log.i(TAG, "录音已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
        }
    }

    /**
     * 绑定音频录制服务
     */
    private fun bindAudioRecordingService() {
        try {
            val intent = Intent(this, AudioRecordingService::class.java)
            val success = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
            if (success) {
                Log.i(TAG, "正在绑定AudioRecordingService")
            } else {
                Log.e(TAG, "绑定AudioRecordingService失败")
                showToast("音频录制服务绑定失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "绑定AudioRecordingService异常", e)
            showToast("音频录制服务绑定异常: ${e.message}")
        }
    }
    
    /**
     * 解绑音频录制服务
     */
    private fun unbindAudioRecordingService() {
        try {
            if (isServiceBound) {
                // 停止录音（如果正在录音）
                if (audioRecordingService?.isRecording() == true) {
                    audioRecordingService?.stopRecording()
                }
                
                // 清除回调
                audioRecordingService?.setCallback(null)
                
                // 解绑服务
                unbindService(serviceConnection)
                isServiceBound = false
                audioRecordingService = null
                
                Log.i(TAG, "AudioRecordingService已解绑")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑AudioRecordingService异常", e)
        }
    }
    
    /**
     * 处理ASR识别结果 - 支持说话人识别，并通知RecordingFragment更新UI
     */
    private fun handleASRResult(result: SingleModelASREngine.ASRResult) {
        Log.d(TAG, "收到ASR结果 - 类型: ${result.type}, 文本: '${result.text}', 时间戳: ${result.timestamp}")

        when (result.type) {
            SingleModelASREngine.ResultType.PREVIEW -> {
                // 显示实时预览
                showPreview(result.text)
                // 通知RecordingFragment更新预览
                getRecordingFragment()?.showPreview(result.text)
            }
            SingleModelASREngine.ResultType.FINAL, SingleModelASREngine.ResultType.ENDPOINT -> {
                // 添加带说话人信息的最终结果
                addFinalResultWithSpeaker(result)
                // 通知RecordingFragment更新结果
                getRecordingFragment()?.updateResults(recognitionResults.toString())
            }
        }
    }

    /**
     * 获取RecordingFragment实例
     */
    private fun getRecordingFragment(): RecordingFragment? {
        return try {
            val fragment = supportFragmentManager.findFragmentByTag("f${MainTabsAdapter.TAB_RECORDING}")
            fragment as? RecordingFragment
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 显示实时预览 - 直接更新主结果区域的当前行（累积显示）
     */
    private fun showPreview(text: String) {
        if (text.isBlank()) {
            // Log.d(TAG, "showPreview: 收到空白文本，跳过处理")
            return
        }

        // Log.d(TAG, "showPreview: 收到文本='$text', 当前预览状态=$isShowingPreview, 当前累积文本='$currentPreviewText'")

        // 如果不是正在显示预览，开始新的预览（生成新时间戳）
        if (!isShowingPreview) {
            previewTimestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            currentPreviewText = text  // 开始新预览，直接设置文本
            isShowingPreview = true
            // Log.d(TAG, "showPreview: 开始新预览 - 时间戳=$previewTimestamp, 初始文本='$currentPreviewText'")
        } else {
            // 正在显示预览，累积文本内容（ASR引擎返回的是增量文本）
            currentPreviewText += text  // 累积增量文本
            // Log.d(TAG, "showPreview: 累积预览文本 - 增量='$text', 累积后='$currentPreviewText'")
        }

        // 使用固定的时间戳构建预览行
        val previewLine = "[$previewTimestamp] $currentPreviewText"

        // 更新主结果区域：基础内容 + 当前累积预览行
        val displayText = if (baseResultsText.isNotEmpty()) {
            "$baseResultsText$previewLine"
        } else {
            previewLine
        }

        // Log.d(TAG, "showPreview: 更新显示文本='$displayText'")
        // UI更新现在由RecordingFragment处理
    }

     /**
     * 添加最终结果 - 覆盖预览文本或添加新行，支持说话人信息
     */
    private fun addFinalResult(text: String) {
        if (text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            val formattedResult = "[$timestamp] $text\n"

            Log.d(TAG, "addFinalResult: 收到最终结果='$text', 当前预览状态=$isShowingPreview, 当前预览文本='$currentPreviewText'")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                Log.d(TAG, "addFinalResult: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                Log.d(TAG, "addFinalResult: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()

            Log.d(TAG, "addFinalResult: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += text.length
            recognitionCount++
        }
    }

    /**
     * 添加带说话人信息的最终结果
     */
    private fun addFinalResultWithSpeaker(result: SingleModelASREngine.ASRResult) {
        if (result.text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())

            // 构建结果文本
            val formattedResult = "[$timestamp]-${result.speakerName}: ${result.text}\n"

            // Log.d(TAG, "addFinalResultWithSpeaker: 收到最终结果='${result.text}', 当前预览状态=$isShowingPreview")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                // Log.d(TAG, "addFinalResultWithSpeaker: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                // Log.d(TAG, "addFinalResultWithSpeaker: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()

            // Log.d(TAG, "addFinalResultWithSpeaker: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += result.text.length
            recognitionCount++
        }
    }
    
    /**
     * 清空结果 - 适配新的UI布局
     */
    private fun clearResults() {
        Log.d(TAG, "clearResults: 清空所有结果和预览状态")

        recognitionResults.clear()
        baseResultsText = ""
        isShowingPreview = false
        currentPreviewText = ""
        previewTimestamp = ""
        currentMeetingRecordId = null // 重置会议记录ID
        currentAudioFilePath = null // 重置录音文件路径

        wordCount = 0
        recognitionCount = 0
        showToast("结果已清空")

        Log.d(TAG, "clearResults: 清空完成")
    }

    /**
     * 复制结果到剪贴板
     */
    private fun copyResults() {
        if (recognitionResults.isEmpty()) {
            showToast("没有可复制的内容")
            return
        }

        try {
            // 构建要复制的文本内容
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            val content = buildString {
                append("语音识别结果\n")
                append("生成时间: $timestamp\n")
                append("总字数: $wordCount\n")
                append("识别次数: $recognitionCount\n")
                append("======================================\n\n")
                append(recognitionResults.toString())
            }

            // 复制到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("ASR识别结果", content)
            clipboard.setPrimaryClip(clip)

            showToast("识别结果已复制到剪贴板")

        } catch (e: Exception) {
            Log.e(TAG, "复制结果失败", e)
            showToast("复制失败: ${e.message}")
        }
    }
    
    // UI更新现在由各个Fragment处理

    // 统计信息更新现在由RecordingFragment处理

    // 录音动画现在由RecordingFragment处理

    /**
     * 开始时间更新
     */
    private fun startTimeUpdate() {
        timeUpdateRunnable = object : Runnable {
            override fun run() {
                if (isRecording.get()) {
                    // 时间更新现在由RecordingFragment处理
                    timeHandler.postDelayed(this, 1000)
                }
            }
        }
        timeHandler.post(timeUpdateRunnable!!)
    }

    /**
     * 停止时间更新
     */
    private fun stopTimeUpdate() {
        timeUpdateRunnable?.let { timeHandler.removeCallbacks(it) }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    // ASRListener接口实现
    override fun onResult(result: SingleModelASREngine.ASRResult) {
        runOnUiThread {
            handleASRResult(result)
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            Log.e(TAG, "ASR错误: $error")
            showToast("识别错误: $error")
        }
    }

    override fun onStatusChanged(status: String) {
        runOnUiThread {
            Log.d(TAG, "ASR状态变化: $status")
        }
    }

    override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
        runOnUiThread {
            Log.d(TAG, "说话人识别结果: ${speakerInfo.name} (置信度: ${speakerInfo.confidence})")
            // 说话人识别结果已经在ASRResult中处理，这里只记录日志
        }
    }

    override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 注册成功"
            } else {
                "说话人 '$speakerName' 注册失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 删除成功"
            } else {
                "说话人 '$speakerName' 删除失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onVadStatusChanged(isSpeech: Boolean) {
        runOnUiThread {
            Log.d(TAG, "VAD状态变化: ${if (isSpeech) "检测到语音" else "静音"}")
            // VAD状态变化，可以用于UI指示，暂时只记录日志
        }
    }

    override fun onResume() {
        super.onResume()
        // 重新加载设置，以便从设置页面返回时更新配置
        loadSettings()
        
        // 如果Service已绑定但ASR引擎未设置，重新设置
        if (isServiceBound && audioRecordingService != null && isInitialized) {
            audioRecordingService?.setAsrEngine(asrEngine)
            audioRecordingService?.setAudioRecordingManager(audioRecordingManager)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()

        // 确保清除屏幕常亮标志（如果还在录音状态）
        if (isRecording.get()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            Log.i(TAG, "Activity销毁时清除屏幕常亮")
        }

        // 注销广播接收器
        unregisterRecordingToggleReceiver()

        // 解绑悬浮窗服务
        unbindFloatingWindowService()

        // 解绑音频录制服务
        unbindAudioRecordingService()

        // 释放ASR引擎
        if (isInitialized) {
            try {
                asrEngine.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放ASR引擎失败", e)
            }
        }

        // 停止时间更新
        stopTimeUpdate()

        Log.i(TAG, "Activity已销毁")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, "录音权限已授予")
                showToast("录音权限已授予")
            } else {
                Log.w(TAG, "录音权限被拒绝")
                showToast("录音权限被拒绝，部分功能可能无法使用")
            }
        }
    }

    // ==================== 已移除的功能 ====================
    // 声纹管理功能已移至SettingsActivity





    // ==================== AI 功能 ====================
    // ASR优化功能已移至SettingsActivity，这里保留会议总结功能

    /**
     * 自动优化ASR内容 - 停止录音后自动执行
     */
    private fun autoOptimizeAsrContent() {
        // 检查当前LLM是否可用，如果没有配置则静默跳过
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@SingleModelActivity)) {
                Log.d(TAG, "当前LLM未配置，跳过自动优化")
                return@launch
            }

            performAutoOptimization()
        }
    }

    private suspend fun performAutoOptimization() {

        val originalContent = recognitionResults.toString()
        if (originalContent.trim().isEmpty()) {
            Log.d(TAG, "识别结果为空，跳过自动优化")
            return
        }

        // 显示优化提示
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
        showToast("🔄 正在使用${currentProvider.displayName}自动优化ASR结果...")
        Log.i(TAG, "开始使用${currentProvider.displayName}自动优化ASR结果")

        // 后台执行优化
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@SingleModelActivity, originalContent)

                runOnUiThread {
                    if (result.success) {
                        // 直接替换结果
                        recognitionResults.clear()
                        recognitionResults.append(result.content)
                        baseResultsText = recognitionResults.toString()

                        // 更新统计
                        wordCount = result.content.length
                        // 通知RecordingFragment更新结果
                        getRecordingFragment()?.updateResults(result.content)

                        // 更新已保存的会议记录中的优化内容
                        updateMeetingRecordOptimizedContent(result.content)

                        showToast("✅ ASR结果已自动优化完成")
                        Log.i(TAG, "自动优化ASR结果成功")
                    } else {
                        Log.w(TAG, "自动优化失败: ${result.error}")
                        showToast("⚠️ 自动优化失败，保持原始结果")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Log.w(TAG, "自动优化异常，保持原始结果", e)
                    showToast("⚠️ 自动优化异常，保持原始结果")
                }
            }
        }
    }

    // ASR优化相关方法已移至LLMManager

    // ASR优化对话框相关方法已移至LLMManager

    /**
     * 生成会议总结 - 打开完整的会议详情页面
     */
    private fun generateMeetingSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以总结")
            return
        }

        // 确保会议记录已保存
        ensureMeetingRecordSaved()

        // 打开会议详情页面
        openMeetingDetailPage()
    }

    // 私有的ensureMeetingRecordSaved方法已移除，使用公共版本

    // 私有的openMeetingDetailPage方法已移除，使用公共版本

    // Gemini API调用已移至LLMManager
    // 会议总结对话框已移除，现在直接打开会议详情页面

    /**
     * 复制文本到剪贴板
     */
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }

    /**
     * 显示LLM配置对话框
     */
    private fun showLLMConfigDialog() {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        AppleInfoDialog(
            context = this,
            title = "LLM 配置",
            message = "当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。",
            positiveButtonText = "打开设置",
            negativeButtonText = "取消",
            onPositiveClick = {
                openSettings()
            }
        ).show()
    }

    // API密钥配置对话框已移至SettingsActivity

    /**
     * 自动保存会议记录
     */
    private fun autoSaveMeetingRecord() {
        try {
            val originalContent = recognitionResults.toString()
            if (originalContent.trim().isEmpty()) {
                Log.d(TAG, "识别结果为空，跳过自动保存")
                return
            }

            // 计算录音时长
            val duration = if (sessionStartTime > 0) {
                (System.currentTimeMillis() - sessionStartTime) / 1000
            } else 0

            // 获取说话人数量（从ASR引擎）
            val speakerCount = try {
                asrEngine.getSpeakerCount()
            } catch (e: Exception) {
                0
            }

            // 创建会议记录
            val meetingRecord = meetingRecordManager.createMeetingRecord(
                originalContent = originalContent,
                wordCount = wordCount,
                duration = duration,
                speakerCount = speakerCount,
                audioFilePath = currentAudioFilePath ?: ""
            )

            // 保存记录
            val success = meetingRecordManager.saveMeetingRecord(meetingRecord)
            if (success) {
                // 保存当前会议记录ID，用于后续更新优化内容和总结
                currentMeetingRecordId = meetingRecord.id
                Log.i(TAG, "会议记录自动保存成功: ${meetingRecord.title}")

                // 标题和标签生成已统一到MeetingRecordManager中
                
                // 可以显示一个轻微的提示，但不要打断用户
                // showToast("📝 历史记录已自动保存")
            } else {
                Log.w(TAG, "会议记录自动保存失败")
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动保存会议记录异常", e)
        }
    }

    /**
     * 开始音频文件转录
     */
    private fun startAudioFileTranscription(uri: Uri, fileName: String) {
        try {
            // 清空当前结果
            clearResults()

            // 显示进度对话框 - 苹果风格
            val progressDialog = AppleProgressDialog(
                context = this,
                title = "🎵 转录音频文件",
                message = "正在处理「$fileName」...\n\n🔄 准备转换音频格式...\n\n✨ 转录将包含完整的声纹识别功能",
                cancelable = true,
                cancelButtonText = "取消",
                onCancelClick = {
                    // TODO: 实现取消转录
                }
            )
            progressDialog.show()

            // 更新UI状态 - 通过RecordingFragment处理
            // btnImportAudio.isEnabled = false

            // 复制文件到临时目录并转换为WAV格式
            CoroutineScope(Dispatchers.IO).launch {
                var tempWavFile: File? = null
                try {
                    // 更新进度：开始转换
                    runOnUiThread {
                        progressDialog.updateMessage("正在处理「$fileName」...\n\n🔄 正在转换音频格式...\n\n请稍候，这可能需要一些时间")
                    }

                    tempWavFile = copyAndConvertToWav(uri, fileName)
                    if (tempWavFile == null) {
                        runOnUiThread {
                            progressDialog.dismiss()
                            showConversionFailedDialog(fileName)
                            resetUIAfterTranscription()
                        }
                        return@launch
                    }

                    // 更新进度：转换完成，开始转录
                    runOnUiThread {
                        progressDialog.updateMessage("正在处理「$fileName」...\n\n✅ 格式转换完成\n🎙️ 开始语音转录...")
                    }

                    // 复制原始文件到永久存储位置
                    val permanentAudioFile = copyOriginalAudioFile(uri, fileName)

                    // 开始转录
                    val transcriber = AudioFileTranscriber(this@SingleModelActivity)
                    transcriber.transcribeAudioFile(
                        audioFilePath = tempWavFile.absolutePath,
                        callback = object : AudioFileTranscriber.TranscriptionCallback {
                            override fun onProgress(progress: Int, currentText: String) {
                                runOnUiThread {
                                    val displayText = currentText.take(100)
                                    val hasSpeaker = currentText.contains(":")
                                    val speakerInfo = if (hasSpeaker) "✅ 检测到说话人信息" else "🔍 正在识别说话人..."
                                    progressDialog.updateMessage("正在处理「$fileName」...\n\n进度: $progress%\n$speakerInfo\n\n当前识别: ${displayText}${if (currentText.length > 100) "..." else ""}")

                                    // 实时更新UI
                                    recognitionResults.clear()
                                    recognitionResults.append(currentText)
                                    wordCount = currentText.length
                                    // 通知RecordingFragment更新结果
                                    getRecordingFragment()?.updateResults(currentText)
                                }
                            }

                            override fun onComplete(finalText: String) {
                                runOnUiThread {
                                    progressDialog.dismiss()
                                    handleTranscriptionComplete(finalText, tempWavFile, permanentAudioFile, fileName)
                                }
                            }

                            override fun onError(error: String) {
                                runOnUiThread {
                                    progressDialog.dismiss()
                                    Log.e(TAG, "音频文件转录失败: $error")
                                    showToast("转录失败: $error")
                                    // 清理临时文件
                                    cleanupTempFile(tempWavFile)
                                    resetUIAfterTranscription()
                                }
                            }
                        }
                    )

                } catch (e: Exception) {
                    runOnUiThread {
                        progressDialog.dismiss()
                        Log.e(TAG, "音频文件转录异常", e)
                        showToast("转录异常: ${e.message}")
                        // 清理临时文件
                        cleanupTempFile(tempWavFile)
                        resetUIAfterTranscription()
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "开始音频文件转录失败", e)
            showToast("开始转录失败: ${e.message}")
        }
    }

    /**
     * 更新会议记录的优化内容
     */
    private fun updateMeetingRecordOptimizedContent(optimizedContent: String) {
        val recordId = currentMeetingRecordId ?: return

        try {
            val existingRecord = meetingRecordManager.getRecordById(recordId) ?: return
            val updatedRecord = existingRecord.copy(
                optimizedContent = optimizedContent,
                wordCount = optimizedContent.length
            )

            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                Log.d(TAG, "会议记录优化内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录优化内容失败", e)
        }
    }

    /**
     * 更新会议记录的总结内容
     */
    private fun updateMeetingRecordSummaryContent(summaryContent: String) {
        val recordId = currentMeetingRecordId ?: return

        try {
            val existingRecord = meetingRecordManager.getRecordById(recordId) ?: return
            val updatedRecord = existingRecord.copy(summaryContent = summaryContent)

            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                Log.d(TAG, "会议记录总结内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录总结内容失败", e)
        }
    }

    /**
     * 处理转录完成
     */
    private fun handleTranscriptionComplete(finalText: String, tempWavFile: File, permanentAudioFile: String?, originalFileName: String) {
        try {
            if (finalText.trim().isEmpty()) {
                showToast("转录结果为空")
                cleanupTempFile(tempWavFile)
                resetUIAfterTranscription()
                return
            }

            // 更新UI
            recognitionResults.clear()
            recognitionResults.append(finalText)
            wordCount = finalText.length
            // 通知RecordingFragment更新结果
            getRecordingFragment()?.updateResults(finalText)

            // 计算文件时长（简单估算）
            val fileSizeBytes = tempWavFile.length()
            val estimatedDuration = (fileSizeBytes / (16000 * 2)).toLong() // 16kHz, 16bit

            // 创建会议记录（使用永久音频文件路径）
            val meetingRecord = meetingRecordManager.createMeetingRecord(
                originalContent = finalText,
                wordCount = wordCount,
                duration = estimatedDuration,
                speakerCount = countSpeakers(finalText),
                audioFilePath = permanentAudioFile ?: ""
            )

            // 保存记录
            val success = meetingRecordManager.saveMeetingRecord(meetingRecord)
            if (success) {
                currentMeetingRecordId = meetingRecord.id
                Log.i(TAG, "导入音频文件转录完成并保存: ${meetingRecord.title}")
                showToast("✅ 转录完成！已保存到会议记录")
            } else {
                Log.w(TAG, "转录完成但保存失败")
                showToast("✅ 转录完成！但保存失败")
            }

            // 转录完成
            Log.i(TAG, "音频文件转录完成")

        } catch (e: Exception) {
            Log.e(TAG, "处理转录完成异常", e)
            showToast("处理转录结果失败: ${e.message}")
        } finally {
            // 清理临时WAV文件
            cleanupTempFile(tempWavFile)
            resetUIAfterTranscription()
        }
    }

    /**
     * 显示转换失败对话框 - 苹果风格
     */
    private fun showConversionFailedDialog(fileName: String) {
        val dialog = AppleErrorDialog(
            context = this,
            title = "⚠️ 音频转换失败",
            message = "文件「$fileName」转换失败，可能的原因：\n\n" +
                    "• 文件格式不支持或已损坏\n" +
                    "• 文件过大导致内存不足\n" +
                    "• 设备存储空间不足\n\n" +
                    "建议：\n" +
                    "• 尝试使用WAV格式文件\n" +
                    "• 选择较小的音频文件\n" +
                    "• 关闭其他应用释放内存",
            positiveButtonText = "重新选择",
            negativeButtonText = "取消",
            onPositiveClick = {
                importAudioFile()
            }
        )
        dialog.show()
    }

    /**
     * 重置转录后的UI状态
     */
    private fun resetUIAfterTranscription() {
        // UI状态重置现在由RecordingFragment处理
        Log.d(TAG, "转录后UI状态重置")
    }

    /**
     * 统计说话人数量
     */
    private fun countSpeakers(text: String): Int {
        return try {
            val speakers = mutableSetOf<String>()
            text.split("\n").forEach { line ->
                val colonIndex = line.indexOf(":")
                if (colonIndex > 0) {
                    val speaker = line.substring(0, colonIndex).trim()
                    if (speaker.isNotEmpty()) {
                        speakers.add(speaker)
                    }
                }
            }
            speakers.size
        } catch (e: Exception) {
            0
        }
    }

    /**
     * 获取文件名
     */
    private fun getFileName(uri: Uri): String {
        return try {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                cursor.moveToFirst()
                cursor.getString(nameIndex)
            } ?: "未知文件"
        } catch (e: Exception) {
            "未知文件"
        }
    }

    /**
     * 获取文件大小
     */
    private fun getFileSize(uri: Uri): Long {
        return try {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                cursor.moveToFirst()
                cursor.getLong(sizeIndex)
            } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 复制并转换音频文件为WAV格式
     */
    private fun copyAndConvertToWav(uri: Uri, fileName: String): File? {
        return try {
            // 创建临时文件
            val tempDir = File(cacheDir, "imported_audio")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            val tempFile = File(tempDir, "converted_${System.currentTimeMillis()}.wav")

            // 使用音频格式转换器
            val converter = AudioFormatConverter(this)
            val success = converter.convertToWav(uri, tempFile)

            if (success && tempFile.exists()) {
                Log.i(TAG, "音频文件转换完成: ${tempFile.absolutePath}")
                return tempFile
            } else {
                Log.w(TAG, "音频文件转换失败: $fileName")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "音频文件转换异常", e)
            null
        }
    }

    /**
     * 复制原始音频文件到永久存储位置
     */
    private fun copyOriginalAudioFile(uri: Uri, fileName: String): String? {
        return try {
            // 创建录音目录
            val recordingsDir = File(filesDir, "recordings")
            if (!recordingsDir.exists()) {
                recordingsDir.mkdirs()
            }

            // 生成永久文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val extension = fileName.substringAfterLast(".", "")
            val permanentFileName = "imported_${timestamp}.${extension}"
            val permanentFile = File(recordingsDir, permanentFileName)

            // 复制文件
            contentResolver.openInputStream(uri)?.use { inputStream ->
                FileOutputStream(permanentFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            if (permanentFile.exists()) {
                Log.i(TAG, "原始音频文件已保存: ${permanentFile.absolutePath}")
                return permanentFile.absolutePath
            } else {
                Log.w(TAG, "原始音频文件保存失败")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "复制原始音频文件异常", e)
            null
        }
    }

    /**
     * 清理临时文件
     */
    private fun cleanupTempFile(tempFile: File?) {
        try {
            if (tempFile != null && tempFile.exists()) {
                val deleted = tempFile.delete()
                if (deleted) {
                    Log.i(TAG, "临时文件已清理: ${tempFile.absolutePath}")
                } else {
                    Log.w(TAG, "临时文件清理失败: ${tempFile.absolutePath}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理临时文件异常", e)
        }
    }

    // override fun onRequestPermissionsResult(
    //     requestCode: Int,
    //     permissions: Array<String>,
    //     grantResults: IntArray
    // ) {
    //     super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    //
    //     if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
    //         if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
    //             Log.i(TAG, "录音权限已授予")
    //             showToast("录音权限已授予")
    //         } else {
    //             Log.w(TAG, "录音权限被拒绝")
    //             showToast("录音权限被拒绝，部分功能可能无法使用")
    //         }
    //     }
    // }
    
    // generateLLMTitleForRecord 方法已移除
    // 标题和标签生成已统一到 MeetingRecordManager.generateTitleTagsAsync 中

    // ==================== 悬浮窗功能 ====================

    /**
     * 注册录音切换广播接收器
     */
    private fun registerRecordingToggleReceiver() {
        try {
            val filter = IntentFilter("com.vectora.vocalmind.TOGGLE_RECORDING")
            registerReceiver(recordingToggleReceiver, filter)
            Log.d(TAG, "录音切换广播接收器已注册")
        } catch (e: Exception) {
            Log.e(TAG, "注册录音切换广播接收器失败", e)
        }
    }

    /**
     * 注销录音切换广播接收器
     */
    private fun unregisterRecordingToggleReceiver() {
        try {
            unregisterReceiver(recordingToggleReceiver)
            Log.d(TAG, "录音切换广播接收器已注销")
        } catch (e: Exception) {
            Log.e(TAG, "注销录音切换广播接收器失败", e)
        }
    }

    /**
     * 绑定悬浮窗服务
     */
    private fun bindFloatingWindowService() {
        try {
            val intent = Intent(this, FloatingWindowService::class.java)
            val success = bindService(intent, floatingServiceConnection, Context.BIND_AUTO_CREATE)
            if (success) {
                Log.i(TAG, "正在绑定FloatingWindowService")
            } else {
                Log.e(TAG, "绑定FloatingWindowService失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "绑定FloatingWindowService异常", e)
        }
    }

    /**
     * 解绑悬浮窗服务
     */
    private fun unbindFloatingWindowService() {
        try {
            if (isFloatingServiceBound) {
                unbindService(floatingServiceConnection)
                isFloatingServiceBound = false
                floatingWindowService = null
                Log.i(TAG, "FloatingWindowService已解绑")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑FloatingWindowService异常", e)
        }
    }

    /**
     * 检查是否需要自动启动悬浮窗
     */
    private fun checkAutoStartFloatingWindow() {
        try {
            // 检查悬浮窗功能是否启用
            if (!FloatingWindowSettings.isFloatingWindowEnabled(this)) {
                Log.d(TAG, "悬浮窗功能未启用")
                return
            }

            // 检查是否启用自动启动
            if (!FloatingWindowSettings.isAutoStartEnabled(this)) {
                Log.d(TAG, "悬浮窗自动启动未启用")
                return
            }

            // 检查悬浮窗权限
            if (!FloatingWindowPermissionManager.hasOverlayPermission(this)) {
                Log.w(TAG, "没有悬浮窗权限，无法自动启动")
                return
            }

            // 延迟启动悬浮窗，确保主应用完全加载
            Handler(Looper.getMainLooper()).postDelayed({
                startFloatingWindowService()
            }, 1000)

        } catch (e: Exception) {
            Log.e(TAG, "检查自动启动悬浮窗失败", e)
        }
    }

    /**
     * 启动悬浮窗服务
     */
    private fun startFloatingWindowService() {
        try {
            val intent = Intent(this, FloatingWindowService::class.java)
            intent.action = "SHOW_FLOATING_WINDOW"
            startService(intent)
            Log.i(TAG, "悬浮窗服务已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动悬浮窗服务失败", e)
        }
    }

    /**
     * 停止悬浮窗服务
     */
    private fun stopFloatingWindowService() {
        try {
            val intent = Intent(this, FloatingWindowService::class.java)
            intent.action = "HIDE_FLOATING_WINDOW"
            startService(intent)
            Log.i(TAG, "悬浮窗服务已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止悬浮窗服务失败", e)
        }
    }

    /**
     * 通知悬浮窗服务录音状态变化
     */
    private fun notifyFloatingWindowRecordingState(recording: Boolean) {
        try {
            val intent = Intent(this, FloatingWindowService::class.java)
            intent.action = "UPDATE_RECORDING_STATE"
            intent.putExtra("recording", recording)
            startService(intent)
            Log.d(TAG, "已通知悬浮窗服务录音状态: $recording")
        } catch (e: Exception) {
            Log.e(TAG, "通知悬浮窗服务录音状态失败", e)
        }
    }

    /**
     * 切换悬浮窗显示状态 - 内部方法
     */
    private fun toggleFloatingWindowInternal() {
        try {
            // 检查悬浮窗功能是否启用
            if (!FloatingWindowSettings.isFloatingWindowEnabled(this)) {
                showFloatingWindowDisabledDialog()
                return
            }

            // 检查悬浮窗权限
            if (!FloatingWindowPermissionManager.hasOverlayPermission(this)) {
                FloatingWindowPermissionManager.requestOverlayPermission(this, object : FloatingWindowPermissionManager.PermissionCallback {
                    override fun onPermissionGranted() {
                        startFloatingWindowService()
                        showToast("悬浮窗已启动")
                    }

                    override fun onPermissionDenied(reason: String) {
                        showToast("悬浮窗权限被拒绝")
                    }
                })
                return
            }

            // 检查悬浮窗是否已经显示
            if (isFloatingServiceBound && floatingWindowService?.isFloatingWindowShowing() == true) {
                stopFloatingWindowService()
                showToast("悬浮窗已关闭")
            } else {
                startFloatingWindowService()
                showToast("悬浮窗已启动")
            }

        } catch (e: Exception) {
            Log.e(TAG, "切换悬浮窗失败", e)
            showToast("切换悬浮窗失败: ${e.message}")
        }
    }

    /**
     * 显示悬浮窗功能未启用对话框 - 苹果风格
     */
    private fun showFloatingWindowDisabledDialog() {
        val dialog = AppleErrorDialog(
            context = this,
            title = "🎈 悬浮窗功能",
            message = "悬浮窗功能未启用。\n\n请前往设置页面开启悬浮窗功能。",
            positiveButtonText = "打开设置",
            negativeButtonText = "取消",
            onPositiveClick = {
                openSettings()
            }
        )
        dialog.show()
    }
}
