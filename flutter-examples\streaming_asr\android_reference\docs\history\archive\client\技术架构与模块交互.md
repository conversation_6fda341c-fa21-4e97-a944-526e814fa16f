# VocalMind 技术架构与模块交互分析

## 整体架构概览

VocalMind 采用分层架构设计，从底层到上层分为：数据层、业务逻辑层、服务层、表现层。

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A1[SingleModelActivity]
        A2[MeetingRecordsActivity]
        A3[AiChatActivity]
        A4[SettingsActivity]
        A5[FloatingWindowView]
    end
    
    subgraph "服务层 (Service Layer)"
        B1[AudioRecordingService]
        B2[FloatingWindowService]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        C1[SingleModelASREngine]
        C2[LLMManager]
        C3[MeetingRecordManager]
        C4[ChatManager]
        C5[AudioRecordingManager]
        C6[SpeakerDataManager]
    end
    
    subgraph "数据层 (Data Layer)"
        D1[SharedPreferences]
        D2[File System]
        D3[Memory Cache]
    end
    
    subgraph "外部服务 (External Services)"
        E1[Gemini API]
        E2[DeepSeek API]
        E3[Audio Hardware]
    end
    
    A1 --> B1
    A1 --> C1
    A1 --> C3
    A2 --> C3
    A3 --> C4
    A3 --> C2
    A5 --> B2
    
    B1 --> C1
    B1 --> C5
    B2 --> B1
    
    C1 --> C6
    C2 --> E1
    C2 --> E2
    C3 --> D1
    C4 --> D1
    C5 --> D2
    C6 --> D1
    
    C1 --> E3
```

## 核心模块详细分析

### 1. ASR引擎模块架构

```mermaid
classDiagram
    class SingleModelASREngine {
        -OnlineRecognizer recognizer
        -OnlineStream stream
        -SpeakerEmbeddingExtractor speakerExtractor
        -SpeakerEmbeddingManager speakerManager
        -List~FloatArray~ audioChunks
        +initialize() boolean
        +startRecognition()
        +processAudio(FloatArray)
        +stopRecognition()
        +reset()
    }
    
    class OnlineRecognizer {
        +create(AssetManager, config) OnlineRecognizer
        +createStream() OnlineStream
        +isReady() boolean
        +decode(OnlineStream)
    }
    
    class OnlineStream {
        +acceptWaveform(FloatArray)
        +getResult() String
        +isEndpoint() boolean
        +reset()
    }
    
    class SpeakerEmbeddingExtractor {
        +extractEmbedding(FloatArray) FloatArray
        +isReady() boolean
    }
    
    class SpeakerEmbeddingManager {
        +identifySpeaker(FloatArray) String
        +registerSpeaker(String, FloatArray)
        +getSpeakerCount() int
    }
    
    class ASRListener {
        <<interface>>
        +onRecognitionResult(ASRResult)
        +onError(String)
        +onStatusChanged(String)
    }
    
    SingleModelASREngine --> OnlineRecognizer
    SingleModelASREngine --> OnlineStream
    SingleModelASREngine --> SpeakerEmbeddingExtractor
    SingleModelASREngine --> SpeakerEmbeddingManager
    SingleModelASREngine ..> ASRListener
```

### 2. LLM管理模块架构

```mermaid
classDiagram
    class LLMManager {
        <<singleton>>
        -Map~LLMProvider, LLMConfig~ llmConfigs
        +getCurrentLLMConfig(Context) LLMConfig
        +optimizeAsrContent(Context, String) LLMResult
        +generateSummary(Context, String, SummaryType) LLMResult
        +generateMermaidDiagram(Context, String, MermaidType) LLMResult
    }
    
    class LLMConfig {
        <<abstract>>
        +getProvider() LLMProvider
        +getApiUrl() String
        +getApiKey(Context) String
        +buildRequest(String) String
        +parseResponse(String) LLMResult
    }
    
    class GeminiConfig {
        +getProvider() LLMProvider
        +buildRequest(String) String
        +parseResponse(String) LLMResult
    }
    
    class DeepSeekConfig {
        +getProvider() LLMProvider
        +buildRequest(String) String
        +parseResponse(String) LLMResult
    }
    
    class StreamingLLMManager {
        +streamChat(Context, String, StreamCallback)
        +stopStreaming()
    }
    
    class LLMApiKeyManager {
        <<singleton>>
        +getCurrentProvider(Context) LLMProvider
        +setCurrentProvider(Context, LLMProvider)
        +getApiKey(Context, LLMProvider) String
        +setApiKey(Context, LLMProvider, String)
    }
    
    LLMManager --> LLMConfig
    LLMConfig <|-- GeminiConfig
    LLMConfig <|-- DeepSeekConfig
    LLMManager --> StreamingLLMManager
    LLMManager --> LLMApiKeyManager
```

### 3. 数据管理模块架构

```mermaid
classDiagram
    class MeetingRecordManager {
        <<singleton>>
        -SharedPreferences prefs
        -Gson gson
        -AudioRecordingManager audioManager
        +saveMeetingRecord(MeetingRecord) boolean
        +getAllRecords() List~MeetingRecord~
        +getRecordById(String) MeetingRecord
        +deleteRecord(String) boolean
        +searchRecords(String) List~MeetingRecord~
    }
    
    class MeetingRecord {
        +String id
        +String title
        +long timestamp
        +String originalContent
        +String optimizedContent
        +String summaryContent
        +String mermaidContent
        +int wordCount
        +long duration
        +String audioFilePath
        +getFormattedDateTime() String
        +getContentPreview() String
    }
    
    class ChatManager {
        <<singleton>>
        -SharedPreferences prefs
        -Gson gson
        +saveChatMessage(ChatMessage)
        +getChatHistory(String) List~ChatMessage~
        +clearChatHistory(String)
    }
    
    class ChatMessage {
        +String id
        +String meetingRecordId
        +String content
        +boolean isUser
        +long timestamp
        +boolean isStreaming
    }
    
    class AudioRecordingManager {
        -FileOutputStream audioOutputStream
        -File currentRecordingFile
        +startRecording(String) String
        +writeAudioData(ShortArray, int)
        +stopRecording() String
        +getRecordingFile(String) File
    }
    
    MeetingRecordManager --> MeetingRecord
    MeetingRecordManager --> AudioRecordingManager
    ChatManager --> ChatMessage
```

## 关键业务流程详解

### 1. 完整的录音识别流程

```mermaid
sequenceDiagram
    participant U as User
    participant MA as MainActivity
    participant ARS as AudioRecordingService
    participant ASR as ASREngine
    participant ARM as AudioRecordingManager
    participant MRM as MeetingRecordManager
    participant LLM as LLMManager
    
    U->>MA: 点击开始录音
    MA->>ARS: startRecording()
    ARS->>ASR: initialize()
    ARS->>ARM: startRecording(meetingId)
    ARM-->>ARS: audioFilePath
    
    loop 录音过程
        ARS->>ASR: processAudio(audioData)
        ARS->>ARM: writeAudioData(audioData)
        ASR->>ASR: 实时识别处理
        ASR->>MA: onRecognitionResult(result)
        MA->>U: 显示实时文字
    end
    
    U->>MA: 点击停止录音
    MA->>ARS: stopRecording()
    ARS->>ASR: stopRecognition()
    ARS->>ARM: stopRecording()
    ARM-->>ARS: finalAudioPath
    ASR-->>ARS: finalResult
    
    ARS->>MRM: 创建MeetingRecord
    MRM->>LLM: optimizeAsrContent(originalText)
    LLM-->>MRM: optimizedText
    MRM->>LLM: generateSummary(optimizedText)
    LLM-->>MRM: summaryText
    MRM->>MRM: saveMeetingRecord()
    MRM-->>MA: 保存完成
    MA->>U: 显示保存结果
```

### 2. AI聊天交互流程

```mermaid
sequenceDiagram
    participant U as User
    participant AC as AiChatActivity
    participant CM as ChatManager
    participant LLM as LLMManager
    participant SLM as StreamingLLMManager
    
    U->>AC: 输入问题
    AC->>CM: 保存用户消息
    AC->>AC: 显示用户消息
    
    AC->>LLM: 构建上下文Prompt
    LLM->>SLM: streamChat(prompt, callback)
    
    loop 流式响应
        SLM->>AC: onStreamUpdate(partialResponse)
        AC->>AC: 更新AI消息显示
        AC->>CM: 更新AI消息内容
    end
    
    SLM->>AC: onStreamComplete(finalResponse)
    AC->>CM: 保存最终AI消息
    AC->>U: 显示完整回答
```

### 3. 悬浮窗状态同步流程

```mermaid
stateDiagram-v2
    [*] --> 悬浮窗初始化
    悬浮窗初始化 --> 等待录音服务
    等待录音服务 --> 服务连接成功 : 绑定成功
    等待录音服务 --> 连接失败 : 绑定失败
    
    服务连接成功 --> 状态同步
    状态同步 --> 录音空闲 : 无录音
    状态同步 --> 录音中 : 有录音
    
    录音空闲 --> 录音中 : 开始录音
    录音中 --> 录音空闲 : 停止录音
    
    录音空闲 --> 悬浮窗关闭 : 用户关闭
    录音中 --> 悬浮窗关闭 : 用户关闭
    悬浮窗关闭 --> [*]
    
    连接失败 --> 重试连接
    重试连接 --> 服务连接成功 : 重试成功
    重试连接 --> [*] : 重试失败
```

## 数据流向分析

### 1. 音频数据流

```mermaid
flowchart LR
    A[麦克风] --> B[AudioRecord]
    B --> C[AudioRecordingService]
    C --> D[音频缓冲区]
    D --> E[ASR引擎]
    D --> F[WAV文件]
    E --> G[识别结果]
    F --> H[本地存储]
    G --> I[UI显示]
    G --> J[会议记录]
```

### 2. 文本数据流

```mermaid
flowchart TD
    A[ASR原始文本] --> B[LLM优化]
    B --> C[优化后文本]
    C --> D[会议总结]
    C --> E[Mermaid图表]
    A --> F[会议记录]
    C --> F
    D --> F
    E --> F
    F --> G[本地存储]
    F --> H[AI聊天上下文]
```

### 3. 用户交互数据流

```mermaid
flowchart TD
    A[用户输入] --> B{操作类型}
    B -->|录音控制| C[录音服务]
    B -->|查看记录| D[记录管理]
    B -->|AI聊天| E[聊天管理]
    B -->|设置配置| F[配置管理]
    
    C --> G[ASR处理]
    D --> H[数据查询]
    E --> I[LLM调用]
    F --> J[偏好存储]
    
    G --> K[结果反馈]
    H --> K
    I --> K
    J --> K
    K --> L[UI更新]
```

## 性能优化策略

### 1. 内存管理优化

```mermaid
mindmap
  root((内存优化))
    音频缓冲区管理
      及时释放音频数据
      控制缓冲区大小
      避免内存泄漏
    对象生命周期
      单例模式减少实例
      弱引用避免循环引用
      及时清理临时对象
    缓存策略
      LRU缓存机制
      内存阈值控制
      定期清理过期数据
```

### 2. 线程调度优化

```mermaid
gantt
    title 线程调度时序图
    dateFormat X
    axisFormat %s
    
    section 主线程
    UI更新           :active, ui, 0, 10s
    用户交互处理      :active, interaction, 0, 10s
    
    section 录音线程
    音频采集         :active, audio, 1s, 8s
    
    section ASR线程
    语音识别         :active, asr, 1s, 8s
    
    section 网络线程
    LLM API调用      :llm, 8s, 10s
    
    section 存储线程
    数据持久化       :storage, 9s, 10s
```

### 3. 网络请求优化

```mermaid
flowchart TD
    A[网络请求] --> B{连接检查}
    B -->|无网络| C[离线模式]
    B -->|有网络| D[请求队列]
    
    D --> E{请求类型}
    E -->|优化请求| F[高优先级]
    E -->|聊天请求| G[中优先级]
    E -->|其他请求| H[低优先级]
    
    F --> I[立即执行]
    G --> J[排队等待]
    H --> J
    
    I --> K{请求结果}
    J --> K
    K -->|成功| L[缓存结果]
    K -->|失败| M[重试机制]
    
    M --> N{重试次数}
    N -->|未超限| D
    N -->|超限| O[错误处理]
    
    L --> P[返回结果]
    O --> P
    C --> P
```

## 错误处理机制

### 1. 异常分类处理

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型}
    
    B -->|网络异常| C[NetworkException]
    B -->|权限异常| D[PermissionException]
    B -->|ASR异常| E[ASRException]
    B -->|存储异常| F[StorageException]
    B -->|其他异常| G[GeneralException]
    
    C --> H[网络重试机制]
    D --> I[权限申请引导]
    E --> J[ASR引擎重启]
    F --> K[存储空间检查]
    G --> L[通用错误处理]
    
    H --> M[用户提示]
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[错误日志记录]
    N --> O[恢复策略执行]
```

### 2. 容错恢复策略

```mermaid
stateDiagram-v2
    [*] --> 正常运行
    正常运行 --> 异常检测 : 发生异常
    异常检测 --> 自动恢复 : 可恢复异常
    异常检测 --> 用户干预 : 需用户处理
    
    自动恢复 --> 恢复成功 : 恢复操作
    自动恢复 --> 恢复失败 : 恢复失败
    
    恢复成功 --> 正常运行
    恢复失败 --> 用户干预
    
    用户干预 --> 正常运行 : 用户修复
    用户干预 --> [*] : 用户放弃
```

## 扩展性设计

### 1. 插件化架构

```mermaid
classDiagram
    class PluginManager {
        +registerPlugin(Plugin)
        +unregisterPlugin(String)
        +getPlugin(String) Plugin
        +getAllPlugins() List~Plugin~
    }
    
    class Plugin {
        <<interface>>
        +getName() String
        +getVersion() String
        +initialize(Context)
        +execute(Map~String,Object~) Object
        +destroy()
    }
    
    class LLMPlugin {
        +getName() String
        +execute(Map~String,Object~) LLMResult
    }
    
    class ASRPlugin {
        +getName() String
        +execute(Map~String,Object~) ASRResult
    }
    
    class StoragePlugin {
        +getName() String
        +execute(Map~String,Object~) StorageResult
    }
    
    PluginManager --> Plugin
    Plugin <|.. LLMPlugin
    Plugin <|.. ASRPlugin
    Plugin <|.. StoragePlugin
```

### 2. 配置化管理

```mermaid
flowchart TD
    A[应用启动] --> B[加载配置文件]
    B --> C{配置类型}
    
    C -->|功能开关| D[FeatureConfig]
    C -->|LLM配置| E[LLMConfig]
    C -->|ASR配置| F[ASRConfig]
    C -->|UI配置| G[UIConfig]
    
    D --> H[功能模块初始化]
    E --> I[LLM服务初始化]
    F --> J[ASR引擎初始化]
    G --> K[UI主题初始化]
    
    H --> L[应用就绪]
    I --> L
    J --> L
    K --> L
```

---

*本文档详细分析了VocalMind的技术架构和模块交互关系，为后续的功能扩展和性能优化提供参考。*