package com.vectora.vocalmind

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.media.MediaPlayer
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import android.app.ProgressDialog
import android.widget.Toast
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.*
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import io.noties.markwon.Markwon
import io.noties.markwon.ext.strikethrough.StrikethroughPlugin
import io.noties.markwon.ext.tables.TablePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin
import android.app.Dialog
import android.view.MotionEvent
import android.view.WindowManager
import android.graphics.Color
import android.view.Gravity

/**
 * 会议详情页面
 * 采用苹果设计风格，分模块显示会议内容
 */
class MeetingDetailActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MeetingDetailActivity"
    }

    private lateinit var btnBack: ImageButton
    private lateinit var btnDelete: ImageButton
    private lateinit var tvTitle: TextView
    private lateinit var tvDateTime: TextView
    private lateinit var tvDuration: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvSpeakerCount: TextView

    // 内容卡片
    private lateinit var cardAudio: CardView
    private lateinit var cardSummary: CardView
    private lateinit var cardOptimized: CardView
    private lateinit var cardOriginal: CardView
    private lateinit var cardMermaid: CardView
    private lateinit var cardTodo: CardView
    private lateinit var cardAiChat: CardView

    // 内容文本
    private lateinit var tvSummaryContent: TextView
    private lateinit var tvOptimizedContent: TextView
    private lateinit var tvOriginalContent: TextView
    private lateinit var tvMermaidContent: TextView
    
    // Mermaid相关组件
    private lateinit var webViewMermaid: WebView
    private lateinit var scrollMermaidCode: ScrollView
    private lateinit var spinnerMermaidType: Spinner
    private var isMermaidCodeView = false // true显示代码，false显示图表
    
    // 总结类型选择器
    private lateinit var spinnerSummaryType: Spinner

    // 录音播放相关
    private lateinit var btnPlayAudio: Button
    private lateinit var tvAudioInfo: TextView
    private var mediaPlayer: MediaPlayer? = null
    private var isPlaying = false

    // 复制按钮
    private lateinit var btnCopySummary: Button
    private lateinit var btnCopyOptimized: Button
    private lateinit var btnCopyOriginal: Button
    private lateinit var btnCopyMermaid: Button
    private lateinit var btnCopyAll: Button

    // AI处理按钮
    private lateinit var btnGenerateSummary: Button
    private lateinit var btnGenerateOptimized: Button
    private lateinit var btnGenerateMermaid: Button
    // private lateinit var btnRetranscribe: Button
    
    // Mermaid视图切换按钮
    private lateinit var btnToggleMermaidView: Button
    
    // Mermaid全屏按钮
    private lateinit var btnFullscreenMermaid: ImageButton
    
    // TODO相关
    private lateinit var btnGenerateTodo: Button
    private lateinit var btnOpenTodo: Button
    private lateinit var layoutTodoList: LinearLayout
    private lateinit var layoutTodoEmpty: LinearLayout

    // AI聊天相关
    private lateinit var btnStartChat: Button
    private lateinit var llChatHistory: LinearLayout

    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var todoManager: TodoManager
    private lateinit var todoGenerator: TodoGenerator
    private var meetingRecord: MeetingRecord? = null
    
    // Markdown渲染器
    private lateinit var markwon: Markwon

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_meeting_detail)

        initViews()
        initMarkwon()
        initMeetingRecordManager()
        initTodoManager()
        loadMeetingRecord()
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnDelete = findViewById(R.id.btn_delete)
        tvTitle = findViewById(R.id.tv_meeting_title)
        tvDateTime = findViewById(R.id.tv_meeting_datetime)
        tvDuration = findViewById(R.id.tv_meeting_duration)
        tvWordCount = findViewById(R.id.tv_meeting_word_count)
        tvSpeakerCount = findViewById(R.id.tv_meeting_speaker_count)

        cardAudio = findViewById(R.id.card_audio)
        cardSummary = findViewById(R.id.card_summary)
        cardOptimized = findViewById(R.id.card_optimized)
        cardOriginal = findViewById(R.id.card_original)
        cardMermaid = findViewById(R.id.cardMermaid)
        cardTodo = findViewById(R.id.card_todo)
        cardAiChat = findViewById(R.id.card_ai_chat)

        tvSummaryContent = findViewById(R.id.tv_summary_content)
        tvOptimizedContent = findViewById(R.id.tv_optimized_content)
        tvOriginalContent = findViewById(R.id.tv_original_content)
        tvMermaidContent = findViewById(R.id.tvMermaidContent)
        
        // Mermaid相关组件
        webViewMermaid = findViewById(R.id.webViewMermaid)
        scrollMermaidCode = findViewById(R.id.scrollMermaidCode)
        spinnerMermaidType = findViewById(R.id.spinnerMermaidType)
        
        // 总结类型选择器
        spinnerSummaryType = findViewById(R.id.spinner_summary_type)

        btnPlayAudio = findViewById(R.id.btn_play_audio)
        tvAudioInfo = findViewById(R.id.tv_audio_info)

        btnCopySummary = findViewById(R.id.btn_copy_summary)
        btnCopyOptimized = findViewById(R.id.btn_copy_optimized)
        btnCopyOriginal = findViewById(R.id.btn_copy_original)
        btnCopyMermaid = findViewById(R.id.btnCopyMermaid)
        btnCopyAll = findViewById(R.id.btn_copy_all)

        btnGenerateSummary = findViewById(R.id.btn_generate_summary)
        btnGenerateOptimized = findViewById(R.id.btn_generate_optimized)
        btnGenerateMermaid = findViewById(R.id.btnGenerateMermaid)
        // btnRetranscribe = findViewById(R.id.btn_retranscribe)
        
        btnToggleMermaidView = findViewById(R.id.btnToggleMermaidView)
        btnFullscreenMermaid = findViewById(R.id.btnFullscreenMermaid)
        
        // TODO相关组件
        btnGenerateTodo = findViewById(R.id.btn_generate_todo)
        btnOpenTodo = findViewById(R.id.btn_open_todo)
        layoutTodoList = findViewById(R.id.layout_todo_list)
        layoutTodoEmpty = findViewById(R.id.layout_todo_empty)

        btnStartChat = findViewById(R.id.btn_open_chat)
        llChatHistory = findViewById(R.id.layout_chat_history)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnDelete.setOnClickListener { showDeleteDialog() }
        btnPlayAudio.setOnClickListener { toggleAudioPlayback() }
        btnCopySummary.setOnClickListener { copyContent("智能总结", tvSummaryContent.text.toString()) }
        btnCopyOptimized.setOnClickListener { copyContent("优化内容", tvOptimizedContent.text.toString()) }
        btnCopyOriginal.setOnClickListener { copyContent("原始记录", tvOriginalContent.text.toString()) }
        btnCopyMermaid.setOnClickListener { copyContent("Mermaid流程图", tvMermaidContent.text.toString()) }
        btnCopyAll.setOnClickListener { copyAllContent() }

        // AI处理按钮点击事件
        btnGenerateSummary.setOnClickListener { generateMeetingSummary() }
        btnGenerateOptimized.setOnClickListener { generateOptimizedContent() }
        btnGenerateMermaid.setOnClickListener { generateMermaidDiagram() }
        // btnRetranscribe.setOnClickListener { showRetranscribeDialog() }
        
        // Mermaid视图切换按钮点击事件
        btnToggleMermaidView.setOnClickListener { toggleMermaidView() }
        
        // Mermaid全屏按钮点击事件
        btnFullscreenMermaid.setOnClickListener { showFullscreenMermaid() }
        
        // TODO按钮点击事件
        btnGenerateTodo.setOnClickListener { generateTodoFromMeeting() }
        btnOpenTodo.setOnClickListener { openTodoList() }

        // AI聊天按钮点击事件
        btnStartChat.setOnClickListener { startAiChat() }
        
        // 初始化WebView设置
        initWebView()
        
        // 初始化Mermaid类型选择器
        initMermaidTypeSpinner()
        
        // 初始化总结类型选择器
        initSummaryTypeSpinner()
    }
    
    /**
     * 初始化WebView设置
     */
    private fun initWebView() {
        webViewMermaid.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
        }
        webViewMermaid.webViewClient = WebViewClient()
    }
    
    /**
     * 初始化Mermaid类型选择器
     */
    private fun initMermaidTypeSpinner() {
        val adapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            MermaidType.getDisplayNames()
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerMermaidType.adapter = adapter
        
        // 默认选择思维导图
        spinnerMermaidType.setSelection(0)
    }
    
    /**
     * 初始化总结类型选择器
     */
    private fun initSummaryTypeSpinner() {
        val adapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            SummaryType.getDisplayNames()
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerSummaryType.adapter = adapter
        
        // 默认选择会议总结
        spinnerSummaryType.setSelection(0)
    }

    private fun copyMermaidContent() {
        val content = tvMermaidContent.text.toString()
        if (content.isNotEmpty() && content != getString(R.string.mermaid_empty_hint)) {
            copyContent("Mermaid流程图", content)
        }
    }

    private fun generateMermaidDiagram() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("原始内容为空，无法生成Mermaid图表")
            return
        }

        // 检查当前LLM是否可用
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@MeetingDetailActivity)) {
                showLLMConfigDialog()
                return@launch
            }

            // 继续执行Mermaid生成逻辑
            performMermaidGeneration()
        }
    }

    private suspend fun performMermaidGeneration() {
        val record = meetingRecord ?: return

        // 获取选中的图表类型
        val selectedType = MermaidType.fromPosition(spinnerMermaidType.selectedItemPosition)

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 准备卡片UI进行流式显示
        setupMermaidStreamingUI(selectedType, currentProvider)

        // 使用流式生成
        LLMManager.generateMermaidDiagramStream(
            context = this,
            originalContent = record.originalContent,
            optimizedContent = record.optimizedContent,
            mermaidType = selectedType,
            callback = object : StreamingCallback {
                override fun onConnectionOpened() {
                    Log.d(TAG, "流式Mermaid生成连接已建立")
                    runOnUiThread {
                        updateMermaidStreamingStatus("连接已建立，开始生成...")
                    }
                }

                override fun onDataReceived(content: String) {
                    Log.d(TAG, "收到流式Mermaid内容: ${content.take(50)}...")
                    runOnUiThread {
                        appendMermaidStreamingContent(content)
                    }
                }

                override fun onCompleted(fullContent: String) {
                    Log.d(TAG, "流式Mermaid生成完成，总长度: ${fullContent.length}")
                    runOnUiThread {
                        completeMermaidStreamingGeneration(fullContent, selectedType)
                    }
                }

                override fun onError(error: String) {
                    Log.e(TAG, "流式Mermaid生成失败: $error")
                    runOnUiThread {
                        handleMermaidStreamingError(error, selectedType)
                    }
                }

                override fun onConnectionClosed() {
                    Log.d(TAG, "流式Mermaid生成连接已关闭")
                }
            }
        )
    }

    private fun toggleMermaidView() {
        if (isMermaidCodeView) {
            // 切换到图表视图
            scrollMermaidCode.visibility = View.GONE
            webViewMermaid.visibility = View.VISIBLE
            btnFullscreenMermaid.visibility = View.VISIBLE
            btnToggleMermaidView.text = getString(R.string.mermaid_view_code)
            
            // 渲染Mermaid图表
            renderMermaidDiagram(tvMermaidContent.text.toString())
        } else {
            // 切换到代码视图
            scrollMermaidCode.visibility = View.VISIBLE
            webViewMermaid.visibility = View.GONE
            btnFullscreenMermaid.visibility = View.GONE
            btnToggleMermaidView.text = getString(R.string.mermaid_view_chart)
        }
        isMermaidCodeView = !isMermaidCodeView
    }

    private fun renderMermaidDiagram(mermaidCode: String) {
        // 首先清理代码标记，然后清理和转义Mermaid代码
        val cleanedCode = LLMManager.cleanMermaidCodeMarkers(mermaidCode)
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;")
        
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
                <style>
                    body {
                        margin: 0;
                        padding: 10px;
                        font-family: Arial, sans-serif;
                        background-color: #ffffff;
                    }
                    .mermaid {
                        text-align: center;
                        max-width: 100%;
                        overflow-x: auto;
                    }
                </style>
            </head>
            <body>
                <div class="mermaid">
$cleanedCode
                </div>
                <script>
                    try {
                        mermaid.initialize({
                            startOnLoad: true,
                            theme: 'default',
                            securityLevel: 'loose',
                            flowchart: {
                                useMaxWidth: true,
                                htmlLabels: true,
                                curve: 'basis'
                            },
                            themeVariables: {
                                fontFamily: 'Arial, sans-serif'
                            }
                        });
                        
                        // 手动渲染以捕获错误
                        mermaid.run().catch(function(error) {
                            console.error('Mermaid rendering error:', error);
                            document.body.innerHTML = '<div style="color: red; padding: 20px;">图表渲染失败: ' + error.message + '</div>';
                        });
                    } catch (error) {
                        console.error('Mermaid initialization error:', error);
                        document.body.innerHTML = '<div style="color: red; padding: 20px;">图表初始化失败: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
        """.trimIndent()
        
        webViewMermaid.loadDataWithBaseURL(null, html, "text/html", "UTF-8", null)
    }
    
    /**
     * 显示全屏Mermaid弹窗
     */
    private fun showFullscreenMermaid() {
        val dialog = Dialog(this, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
        dialog.setContentView(R.layout.dialog_fullscreen_mermaid)
        
        val webView = dialog.findViewById<WebView>(R.id.webViewFullscreenMermaid)
        val btnClose = dialog.findViewById<ImageButton>(R.id.btnCloseFullscreen)
        val dragHandle = dialog.findViewById<View>(R.id.dragHandle)
        
        // 设置WebView基本配置
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
        }
        webView.webViewClient = WebViewClient()
        
        // 创建增强的弹窗管理器
        val dialogManager = FullscreenDialogManager(this, dialog)
        
        // 设置增强的触摸功能（自动拖动、全方向拖动、手指缩放）
        dialogManager.setupEnhancedTouch(webView, dragHandle)
        
        // 渲染Mermaid图表
        renderMermaidInWebView(webView, tvMermaidContent.text.toString())
        
        // 关闭按钮
        btnClose.setOnClickListener {
            dialogManager.cleanup()
            dialog.dismiss()
        }
        
        // 弹窗关闭时清理资源
        dialog.setOnDismissListener {
            dialogManager.cleanup()
        }
        
        dialog.show()
    }
    
    /**
     * 在指定WebView中渲染Mermaid图表
     */
    private fun renderMermaidInWebView(webView: WebView, mermaidCode: String) {
        val cleanedCode = LLMManager.cleanMermaidCodeMarkers(mermaidCode)
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;")
        
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        background-color: #ffffff;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                    }
                    .mermaid {
                        text-align: center;
                        max-width: 100%;
                        overflow: auto;
                    }
                </style>
            </head>
            <body>
                <div class="mermaid">
$cleanedCode
                </div>
                <script>
                    try {
                        mermaid.initialize({
                            startOnLoad: true,
                            theme: 'default',
                            securityLevel: 'loose',
                            flowchart: {
                                useMaxWidth: true,
                                htmlLabels: true,
                                curve: 'basis'
                            },
                            themeVariables: {
                                fontFamily: 'Arial, sans-serif'
                            }
                        });
                        
                        mermaid.run().catch(function(error) {
                            console.error('Mermaid rendering error:', error);
                            document.body.innerHTML = '<div style="color: red; padding: 20px;">图表渲染失败: ' + error.message + '</div>';
                        });
                    } catch (error) {
                        console.error('Mermaid initialization error:', error);
                        document.body.innerHTML = '<div style="color: red; padding: 20px;">图表初始化失败: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
        """.trimIndent()
        
        webView.loadDataWithBaseURL(null, html, "text/html", "UTF-8", null)
    }
    
    /**
     * 设置弹窗拖动功能（已被 FullscreenDialogManager 替代）
     * 保留此方法以维持向后兼容性
     */
    @Deprecated("使用 FullscreenDialogManager.setupEnhancedTouch() 替代")
    private fun setupDragFunctionality(dialog: Dialog, dragHandle: View) {
        // 此方法已被 FullscreenDialogManager 的增强功能替代
        // 新的实现支持：
        // 1. 自动拖动（惯性滑动）
        // 2. 全方向自由拖动（整个弹窗区域）
        // 3. 手指缩放功能
        // 4. 边缘吸附
        // 5. 平滑动画效果
    }

    private fun updateMeetingRecordMermaid(mermaidContent: String) {
        lifecycleScope.launch {
            try {
                meetingRecord?.let { meetingRecordManager.saveMeetingRecord(it) }
            } catch (e: Exception) {
                Log.e("MeetingDetailActivity", "更新会议记录Mermaid内容失败", e)
            }
        }
    }

    private fun initMarkwon() {
        markwon = Markwon.builder(this)
            .usePlugin(StrikethroughPlugin.create())
            .usePlugin(TablePlugin.create(this))
            .usePlugin(TaskListPlugin.create(this))
            .build()
    }

    private fun initMeetingRecordManager() {
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun initTodoManager() {
        todoManager = TodoManager.getInstance(this)
        todoGenerator = TodoGenerator(this)
    }

    /**
     * 设置TextView的Markdown内容
     */
    private fun setMarkdownText(textView: TextView, markdownText: String?) {
        val safeText = markdownText ?: ""
        if (safeText.trim().isEmpty()) {
            textView.text = safeText
            return
        }
        
        try {
            markwon.setMarkdown(textView, safeText)
        } catch (e: Exception) {
            Log.w(TAG, "Markdown渲染失败，使用原始文本: ${e.message}")
            textView.text = safeText
        }
    }

    private fun loadMeetingRecord() {
        val recordId = intent.getStringExtra("meeting_record_id")
        if (recordId.isNullOrEmpty()) {
            Log.e(TAG, "会议记录ID为空")
            showToast("会议记录ID无效")
            finish()
            return
        }

        meetingRecord = meetingRecordManager.getRecordById(recordId)
        if (meetingRecord == null) {
            Log.e(TAG, "找不到会议记录: $recordId")
            showToast("找不到会议记录")
            finish()
            return
        }

        displayMeetingRecord(meetingRecord!!)
    }

    private fun displayMeetingRecord(record: MeetingRecord) {
        // 设置基本信息
        tvTitle.text = record.title ?: "未命名会议"
        tvDateTime.text = record.getFormattedDateTime() ?: ""
        tvDuration.text = "时长: ${record.getFormattedDuration() ?: "00:00"}"
        tvWordCount.text = "字数: ${record.wordCount}"
        
        if (record.speakerCount > 0) {
            tvSpeakerCount.text = "说话人: ${record.speakerCount} 人"
            tvSpeakerCount.visibility = View.VISIBLE
        } else {
            tvSpeakerCount.visibility = View.GONE
        }

        // 显示录音文件（仅在启用录音保存且有录音文件时显示）
        if (SettingsActivity.getSaveRecordingSetting(this) && record.hasAudioFile()) {
            cardAudio.visibility = View.VISIBLE
            val audioRecordingManager = AudioRecordingManager(this)
            val fileSize = audioRecordingManager.getRecordingSize(record.audioFilePath!!)
            tvAudioInfo.text = "录音文件 (${String.format("%.1f", fileSize)} MB)"
        } else {
            cardAudio.visibility = View.GONE
        }

        // 始终显示智能总结卡片
        cardSummary.visibility = View.VISIBLE
        if (record.hasSummaryContent()) {
            setMarkdownText(tvSummaryContent, record.summaryContent)
            tvSummaryContent.setTextColor(getColor(R.color.apple_label))
            btnCopySummary.visibility = View.VISIBLE
            btnGenerateSummary.visibility = View.GONE
        } else {
            tvSummaryContent.text = "点击下方按钮生成AI总结"
            tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopySummary.visibility = View.GONE
            btnGenerateSummary.visibility = View.VISIBLE
        }

        // 始终显示优化内容卡片
        cardOptimized.visibility = View.VISIBLE
        if (record.hasOptimizedContent()) {
            setMarkdownText(tvOptimizedContent, record.optimizedContent)
            tvOptimizedContent.setTextColor(getColor(R.color.apple_label))
            btnCopyOptimized.visibility = View.VISIBLE
            btnGenerateOptimized.visibility = View.GONE
        } else {
            tvOptimizedContent.text = "点击下方按钮生成AI优化内容"
            tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopyOptimized.visibility = View.GONE
            btnGenerateOptimized.visibility = View.VISIBLE
        }

        // 显示原始内容
        tvOriginalContent.text = record.originalContent

        // 始终显示Mermaid流程图卡片
        cardMermaid.visibility = View.VISIBLE
        if (record.hasMermaidContent()) {
            tvMermaidContent.text = record.mermaidContent ?: ""
            tvMermaidContent.setTextColor(getColor(R.color.apple_label))
            btnCopyMermaid.visibility = View.VISIBLE
            btnGenerateMermaid.visibility = View.GONE
            btnToggleMermaidView.visibility = View.VISIBLE
            // 默认显示图表视图
            isMermaidCodeView = false
            scrollMermaidCode.visibility = View.GONE
            webViewMermaid.visibility = View.VISIBLE
            btnFullscreenMermaid.visibility = View.VISIBLE
            btnToggleMermaidView.text = getString(R.string.mermaid_view_code)
            
            // 渲染Mermaid图表
            renderMermaidDiagram(record.mermaidContent ?: "")
        } else {
            tvMermaidContent.text = getString(R.string.mermaid_empty_hint)
            tvMermaidContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopyMermaid.visibility = View.GONE
            btnGenerateMermaid.visibility = View.VISIBLE
            btnToggleMermaidView.visibility = View.GONE
            // 隐藏WebView
            webViewMermaid.visibility = View.GONE
            scrollMermaidCode.visibility = View.VISIBLE
        }

        // 显示重新转录按钮（只有在启用录音保存且有录音文件时才显示）
        // if (SettingsActivity.getSaveRecordingSetting(this) && record.hasAudioFile()) {
        //     btnRetranscribe.visibility = View.VISIBLE
        // } else {
        //     btnRetranscribe.visibility = View.GONE
        // }
        
        // 显示AI聊天卡片
        cardAiChat.visibility = View.VISIBLE
    }

    private fun copyContent(label: String, content: String) {
        if (content.isBlank()) {
            showToast("内容为空")
            return
        }

        try {
            // 获取导出格式设置
            val exportFormat = SettingsActivity.getExportFormat(this)
            val formattedContent = formatContentForExport(content, exportFormat, label)

            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText(label, formattedContent)
            clipboard.setPrimaryClip(clip)
            showToast("$label 已复制到剪贴板 ($exportFormat 格式)")
        } catch (e: Exception) {
            Log.e(TAG, "复制内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun copyAllContent() {
        val record = meetingRecord ?: return

        try {
            // 获取导出格式设置
            val exportFormat = SettingsActivity.getExportFormat(this)
            val fullContent = record.getFullContent(exportFormat)

            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("完整会议记录", fullContent)
            clipboard.setPrimaryClip(clip)
            showToast("完整会议记录已复制到剪贴板 ($exportFormat 格式)")
        } catch (e: Exception) {
            Log.e(TAG, "复制完整内容失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun showDeleteDialog() {
        val record = meetingRecord ?: return

        AlertDialog.Builder(this)
            .setTitle("删除会议记录")
            .setMessage("确定要删除「${record.title}」吗？\n\n此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                deleteRecord()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteRecord() {
        val record = meetingRecord ?: return

        try {
            val success = meetingRecordManager.deleteRecord(record.id)
            if (success) {
                showToast("会议记录已删除")
                finish()
            } else {
                showToast("删除失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "删除会议记录失败", e)
            showToast("删除失败: ${e.message}")
        }
    }

    /**
     * 生成会议总结
     */
    private fun generateMeetingSummary() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("原始内容为空，无法生成总结")
            return
        }

        // 检查当前LLM是否可用
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@MeetingDetailActivity)) {
                showLLMConfigDialog()
                return@launch
            }

            // 继续执行总结逻辑
            performSummaryGeneration()
        }
    }

    private suspend fun performSummaryGeneration() {
        // 获取选中的总结类型
        val selectedType = SummaryType.fromPosition(spinnerSummaryType.selectedItemPosition)
        val record = meetingRecord ?: return

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 准备卡片UI进行流式显示
        setupStreamingUI(selectedType, currentProvider)

        // 使用流式生成
        LLMManager.generateSummaryStream(
            context = this,
            originalContent = record.originalContent,
            optimizedContent = record.optimizedContent,
            summaryType = selectedType,
            callback = object : StreamingCallback {
                override fun onConnectionOpened() {
                    Log.d(TAG, "流式总结连接已建立")
                    runOnUiThread {
                        updateStreamingStatus("连接已建立，开始生成...")
                    }
                }

                override fun onDataReceived(content: String) {
                    Log.d(TAG, "收到流式总结内容: ${content.take(50)}...")
                    runOnUiThread {
                        appendStreamingContent(content)
                    }
                }

                override fun onCompleted(fullContent: String) {
                    Log.d(TAG, "流式总结完成，总长度: ${fullContent.length}")
                    runOnUiThread {
                        completeStreamingGeneration(fullContent, selectedType)
                    }
                }

                override fun onError(error: String) {
                    Log.e(TAG, "流式总结失败: $error")
                    runOnUiThread {
                        handleStreamingError(error, selectedType)
                    }
                }

                override fun onConnectionClosed() {
                    Log.d(TAG, "流式总结连接已关闭")
                }
            }
        )
    }

    /**
     * 生成优化内容
     */
    private fun generateOptimizedContent() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("原始内容为空，无法生成优化内容")
            return
        }

        // 检查当前LLM是否可用
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@MeetingDetailActivity)) {
                showLLMConfigDialog()
                return@launch
            }

            // 继续执行优化逻辑
            performOptimizedContentGeneration()
        }
    }

    private suspend fun performOptimizedContentGeneration() {
        val record = meetingRecord ?: return

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 准备卡片UI进行流式显示
        setupOptimizedStreamingUI(currentProvider)

        // 使用流式生成
        LLMManager.optimizeAsrContentStream(
            context = this,
            originalContent = record.originalContent,
            callback = object : StreamingCallback {
                override fun onConnectionOpened() {
                    Log.d(TAG, "流式优化连接已建立")
                    runOnUiThread {
                        updateOptimizedStreamingStatus("连接已建立，开始优化...")
                    }
                }

                override fun onDataReceived(content: String) {
                    Log.d(TAG, "收到流式优化内容: ${content.take(50)}...")
                    runOnUiThread {
                        appendOptimizedStreamingContent(content)
                    }
                }

                override fun onCompleted(fullContent: String) {
                    Log.d(TAG, "流式优化完成，总长度: ${fullContent.length}")
                    runOnUiThread {
                        completeOptimizedStreamingGeneration(fullContent)
                    }
                }

                override fun onError(error: String) {
                    Log.e(TAG, "流式优化失败: $error")
                    runOnUiThread {
                        handleOptimizedStreamingError(error)
                    }
                }

                override fun onConnectionClosed() {
                    Log.d(TAG, "流式优化连接已关闭")
                }
            }
        )
    }

    /**
     * 更新会议记录的总结内容
     */
    private fun updateMeetingRecordSummary(summaryContent: String) {
        val record = meetingRecord ?: return

        try {
            val updatedRecord = record.copy(summaryContent = summaryContent)
            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                meetingRecord = updatedRecord
                Log.d(TAG, "会议记录总结内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录总结内容失败", e)
        }
    }

    /**
     * 更新会议记录的优化内容
     */
    private fun updateMeetingRecordOptimized(optimizedContent: String) {
        val record = meetingRecord ?: return

        try {
            val updatedRecord = record.copy(optimizedContent = optimizedContent)
            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                meetingRecord = updatedRecord
                Log.d(TAG, "会议记录优化内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录优化内容失败", e)
        }
    }

    /**
     * 显示重新转录确认对话框
     */
    private fun showRetranscribeDialog() {
        val record = meetingRecord ?: return

        if (!SettingsActivity.getSaveRecordingSetting(this)) {
            showToast("录音保存功能已关闭，无法重新转录")
            return
        }

        if (!record.hasAudioFile()) {
            showToast("没有找到录音文件")
            return
        }

        AlertDialog.Builder(this)
            .setTitle("重新转录")
            .setMessage("确定要重新转录这段录音吗？\n\n• 将使用最新的ASR引擎重新处理录音\n• 可能获得更好的识别效果\n• 原始转录内容将被替换\n• 建议在转录完成后重新生成优化内容和总结")
            .setPositiveButton("开始转录") { _, _ ->
                startRetranscription()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 开始重新转录
     */
    private fun startRetranscription() {
        val record = meetingRecord ?: return

        // 创建进度对话框
        val progressDialog = AlertDialog.Builder(this)
            .setTitle("🎙️ 重新转录中")
            .setMessage("正在处理录音文件，包含声纹识别...\n\n进度: 0%\n\n✨ 重新转录将包含完整的说话人识别信息")
            .setCancelable(false)
            .setNegativeButton("取消") { _, _ ->
                // TODO: 实现取消转录
            }
            .create()
        progressDialog.show()

        // 创建音频转录器
        val transcriber = AudioFileTranscriber(this)

        // 开始转录
        CoroutineScope(Dispatchers.Main).launch {
            val audioPath = record.audioFilePath
            if (audioPath.isNullOrEmpty()) {
                Log.e(TAG, "音频文件路径为空")
                showToast("音频文件路径无效")
                return@launch
            }
            
            transcriber.transcribeAudioFile(
                audioFilePath = audioPath,
                callback = object : AudioFileTranscriber.TranscriptionCallback {
                    override fun onProgress(progress: Int, currentText: String) {
                        val displayText = currentText.take(100)
                        val hasSpeaker = currentText.contains(":")
                        val speakerInfo = if (hasSpeaker) "✅ 检测到说话人信息" else "🔍 正在识别说话人..."
                        progressDialog.setMessage("正在处理录音文件，包含声纹识别...\n\n进度: $progress%\n$speakerInfo\n\n当前识别: ${displayText}${if (currentText.length > 100) "..." else ""}")
                    }

                    override fun onComplete(finalText: String) {
                        progressDialog.dismiss()
                        handleRetranscriptionComplete(finalText)
                    }

                    override fun onError(error: String) {
                        progressDialog.dismiss()
                        Log.e(TAG, "重新转录失败: $error")
                        showToast("重新转录失败: $error")
                    }
                }
            )
        }
    }

    /**
     * 处理重新转录完成
     */
    private fun handleRetranscriptionComplete(newTranscription: String) {
        val record = meetingRecord ?: return

        if (newTranscription.trim().isEmpty()) {
            showToast("转录结果为空")
            return
        }

        // 更新原始内容
        setMarkdownText(tvOriginalContent, newTranscription)

        // 更新数据库
        val updatedRecord = record.copy(
            originalContent = newTranscription,
            wordCount = newTranscription.length
        )

        val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
        if (success) {
            meetingRecord = updatedRecord
            Log.i(TAG, "重新转录完成，内容已更新")

            // 询问是否重新生成AI内容
            showRegenerateAIContentDialog()
        } else {
            showToast("保存转录结果失败")
        }
    }

    /**
     * 显示重新生成AI内容的对话框
     */
    private fun showRegenerateAIContentDialog() {
        val record = meetingRecord ?: return

        val hasOptimized = record.hasOptimizedContent()
        val hasSummary = record.hasSummaryContent()

        if (!hasOptimized && !hasSummary) {
            showToast("✅ 重新转录完成！")
            return
        }

        val message = buildString {
            append("✅ 重新转录完成！\n\n")
            append("检测到您之前已生成过：\n")
            if (hasOptimized) append("• AI优化内容\n")
            if (hasSummary) append("• AI总结\n")
            append("\n建议基于新的转录内容重新生成这些AI内容，以获得更好的效果。")
        }

        AlertDialog.Builder(this)
            .setTitle("重新生成AI内容")
            .setMessage(message)
            .setPositiveButton("重新生成") { _, _ ->
                regenerateAIContent(hasOptimized, hasSummary)
            }
            .setNegativeButton("稍后手动生成", null)
            .show()
    }

    /**
     * 重新生成AI内容
     */
    private fun regenerateAIContent(regenerateOptimized: Boolean, regenerateSummary: Boolean) {
        if (regenerateOptimized) {
            // 清空优化内容并触发重新生成
            val record = meetingRecord ?: return
            val clearedRecord = record.copy(optimizedContent = "")
            meetingRecord = clearedRecord
            meetingRecordManager.saveMeetingRecord(clearedRecord)

            // 更新UI显示
            tvOptimizedContent.text = "点击下方按钮生成AI优化内容"
            tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopyOptimized.visibility = View.GONE
            btnGenerateOptimized.visibility = View.VISIBLE
        }

        if (regenerateSummary) {
            // 清空总结内容并触发重新生成
            val record = meetingRecord ?: return
            val clearedRecord = record.copy(summaryContent = "")
            meetingRecord = clearedRecord
            meetingRecordManager.saveMeetingRecord(clearedRecord)

            // 更新UI显示
            tvSummaryContent.text = "点击下方按钮生成AI总结"
            tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))
            btnCopySummary.visibility = View.GONE
            btnGenerateSummary.visibility = View.VISIBLE
        }

        showToast("AI内容已重置，请点击相应按钮重新生成")
    }

    /**
     * 显示LLM配置对话框
     */
    private fun showLLMConfigDialog() {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        AlertDialog.Builder(this)
            .setTitle("LLM 配置")
            .setMessage("当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。")
            .setPositiveButton("打开设置") { _, _ ->
                val intent = android.content.Intent(this, SettingsActivity::class.java)
                startActivity(intent)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 切换录音播放状态
     */
    private fun toggleAudioPlayback() {
        val record = meetingRecord ?: return

        if (!SettingsActivity.getSaveRecordingSetting(this)) {
            showToast("录音保存功能已关闭")
            return
        }

        if (!record.hasAudioFile()) {
            showToast("录音文件不存在")
            return
        }

        try {
            if (isPlaying) {
                stopAudioPlayback()
            } else {
                startAudioPlayback(record.audioFilePath!!)
            }
        } catch (e: Exception) {
            Log.e(TAG, "录音播放操作失败", e)
            showToast("录音播放失败: ${e.message}")
        }
    }

    /**
     * 开始播放录音
     */
    private fun startAudioPlayback(audioFilePath: String) {
        try {
            mediaPlayer = MediaPlayer().apply {
                setDataSource(audioFilePath)
                prepareAsync()
                setOnPreparedListener {
                    start()
                    <EMAIL> = true
                    btnPlayAudio.text = "⏸️ 暂停播放"
                    Log.i(TAG, "开始播放录音: $audioFilePath")
                }
                setOnCompletionListener {
                    stopAudioPlayback()
                    showToast("录音播放完成")
                }
                setOnErrorListener { _, what, extra ->
                    Log.e(TAG, "录音播放错误: what=$what, extra=$extra")
                    stopAudioPlayback()
                    showToast("录音播放出错")
                    true
                }
            }
        } catch (e: IOException) {
            Log.e(TAG, "录音播放初始化失败", e)
            showToast("录音文件无法播放")
        }
    }

    /**
     * 停止播放录音
     */
    private fun stopAudioPlayback() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            }
            mediaPlayer = null
            isPlaying = false
            btnPlayAudio.text = "▶️ 播放"
            Log.i(TAG, "停止播放录音")
        } catch (e: Exception) {
            Log.e(TAG, "停止录音播放失败", e)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 根据导出格式格式化内容
     */
    private fun formatContentForExport(content: String, format: String, label: String): String {
        return when (format.uppercase()) {
            "MARKDOWN" -> {
                when (label) {
                    "智能总结" -> "## 🤖 智能总结\n\n$content"
                    "优化内容" -> "## ✨ 优化内容\n\n$content"
                    "原始记录" -> "## 📄 原始记录\n\n$content"
                    "流程图", "Mermaid流程图" -> "## 📊 流程图\n\n```mermaid\n$content\n```"
                    else -> "## $label\n\n$content"
                }
            }
            else -> {
                // TXT格式
                when (label) {
                    "智能总结" -> "🤖 智能总结\n------------------------------\n$content"
                    "优化内容" -> "✨ 优化内容\n------------------------------\n$content"
                    "原始记录" -> "📄 原始记录\n------------------------------\n$content"
                    "流程图", "Mermaid流程图" -> "📊 流程图\n------------------------------\n$content"
                    else -> "$label\n------------------------------\n$content"
                }
            }
        }
    }

    // 流式生成相关变量
    private var isStreamingInProgress = false
    private var streamingContent = StringBuilder()

    // 滚动优化相关变量
    private var lastScrollTime = 0L
    private val scrollThrottleMs = 100L // 滚动节流时间
    private var pendingScrollRunnable: Runnable? = null
    private var isUserScrolling = false
    private var lastContentLength = 0

    // 多卡片流式生成状态管理
    private enum class StreamingCardType {
        SUMMARY, OPTIMIZED, MERMAID
    }
    private var currentStreamingCard: StreamingCardType? = null

    /**
     * 设置流式UI状态（智能总结卡片）
     */
    private fun setupStreamingUI(summaryType: SummaryType, provider: LLMProvider) {
        isStreamingInProgress = true
        streamingContent.clear()
        currentStreamingCard = StreamingCardType.SUMMARY

        // 重置滚动相关状态
        isUserScrolling = false
        lastContentLength = 0
        lastScrollTime = 0L
        pendingScrollRunnable?.let { tvSummaryContent.removeCallbacks(it) }
        pendingScrollRunnable = null

        // 设置滚动监听
        setupScrollListener(tvSummaryContent)

        // 更新UI状态
        tvSummaryContent.text = "🔄 正在使用 ${provider.displayName} 生成${summaryType.displayName}...\n\n💡 内容将实时显示在下方"
        tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))

        // 更新按钮状态：隐藏复制按钮，将生成按钮改为取消按钮
        btnCopySummary.visibility = View.GONE
        btnGenerateSummary.text = "⏹️ 取消生成"
        btnGenerateSummary.visibility = View.VISIBLE

        // 临时改变生成按钮的点击事件为取消功能
        btnGenerateSummary.setOnClickListener {
            cancelStreamingGeneration(summaryType)
        }
    }

    /**
     * 设置优化内容流式UI状态
     */
    private fun setupOptimizedStreamingUI(provider: LLMProvider) {
        isStreamingInProgress = true
        streamingContent.clear()
        currentStreamingCard = StreamingCardType.OPTIMIZED

        // 重置滚动相关状态
        isUserScrolling = false
        lastContentLength = 0
        lastScrollTime = 0L
        pendingScrollRunnable?.let { tvOptimizedContent.removeCallbacks(it) }
        pendingScrollRunnable = null

        // 设置滚动监听
        setupScrollListener(tvOptimizedContent)

        // 更新UI状态
        tvOptimizedContent.text = "🔄 正在使用 ${provider.displayName} 优化内容...\n\n💡 优化后的内容将实时显示在下方"
        tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))

        // 更新按钮状态：隐藏复制按钮，将生成按钮改为取消按钮
        btnCopyOptimized.visibility = View.GONE
        btnGenerateOptimized.text = "⏹️ 取消优化"
        btnGenerateOptimized.visibility = View.VISIBLE

        // 临时改变生成按钮的点击事件为取消功能
        btnGenerateOptimized.setOnClickListener {
            cancelOptimizedStreamingGeneration()
        }
    }

    /**
     * 设置Mermaid流式UI状态
     */
    private fun setupMermaidStreamingUI(mermaidType: MermaidType, provider: LLMProvider) {
        isStreamingInProgress = true
        streamingContent.clear()
        currentStreamingCard = StreamingCardType.MERMAID

        // 重置滚动相关状态
        isUserScrolling = false
        lastContentLength = 0
        lastScrollTime = 0L
        pendingScrollRunnable?.let { tvMermaidContent.removeCallbacks(it) }
        pendingScrollRunnable = null

        // 设置滚动监听
        setupScrollListener(tvMermaidContent)

        // 更新UI状态
        tvMermaidContent.text = "🔄 正在使用 ${provider.displayName} 生成${mermaidType.displayName}...\n\n💡 图表代码将实时显示在下方"
        tvMermaidContent.setTextColor(getColor(R.color.apple_secondary_label))

        // 更新按钮状态：隐藏复制按钮，将生成按钮改为取消按钮
        btnCopyMermaid.visibility = View.GONE
        btnGenerateMermaid.text = "⏹️ 取消生成"
        btnGenerateMermaid.visibility = View.VISIBLE
        btnToggleMermaidView.visibility = View.GONE

        // 隐藏WebView，显示代码视图
        webViewMermaid.visibility = View.GONE
        scrollMermaidCode.visibility = View.VISIBLE
        btnFullscreenMermaid.visibility = View.GONE

        // 临时改变生成按钮的点击事件为取消功能
        btnGenerateMermaid.setOnClickListener {
            cancelMermaidStreamingGeneration(mermaidType)
        }
    }

    /**
     * 更新流式状态信息
     */
    private fun updateStreamingStatus(status: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.SUMMARY) return

        val currentText = tvSummaryContent.text.toString()
        val lines = currentText.split("\n").toMutableList()

        // 更新第一行状态信息
        if (lines.isNotEmpty()) {
            lines[0] = "🔄 $status"
            tvSummaryContent.text = lines.joinToString("\n")
        }
    }

    /**
     * 更新优化内容流式状态信息
     */
    private fun updateOptimizedStreamingStatus(status: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.OPTIMIZED) return

        val currentText = tvOptimizedContent.text.toString()
        val lines = currentText.split("\n").toMutableList()

        // 更新第一行状态信息
        if (lines.isNotEmpty()) {
            lines[0] = "🔄 $status"
            tvOptimizedContent.text = lines.joinToString("\n")
        }
    }

    /**
     * 更新Mermaid流式状态信息
     */
    private fun updateMermaidStreamingStatus(status: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.MERMAID) return

        val currentText = tvMermaidContent.text.toString()
        val lines = currentText.split("\n").toMutableList()

        // 更新第一行状态信息
        if (lines.isNotEmpty()) {
            lines[0] = "🔄 $status"
            tvMermaidContent.text = lines.joinToString("\n")
        }
    }

    /**
     * 追加流式内容（智能总结）
     */
    private fun appendStreamingContent(content: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.SUMMARY) return

        streamingContent.append(content)

        // 只有内容长度变化较大时才更新UI（减少频繁更新）
        val currentLength = streamingContent.length
        if (currentLength - lastContentLength < 10 && currentLength < 500) {
            return // 跳过小幅更新，减少UI刷新频率
        }
        lastContentLength = currentLength

        updateStreamingUI()

        // 使用节流滚动
        scheduleSmartScroll(tvSummaryContent)
    }

    /**
     * 追加优化内容流式内容
     */
    private fun appendOptimizedStreamingContent(content: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.OPTIMIZED) return

        streamingContent.append(content)

        // 只有内容长度变化较大时才更新UI（减少频繁更新）
        val currentLength = streamingContent.length
        if (currentLength - lastContentLength < 10 && currentLength < 500) {
            return // 跳过小幅更新，减少UI刷新频率
        }
        lastContentLength = currentLength

        updateOptimizedStreamingUI()

        // 使用节流滚动
        scheduleSmartScroll(tvOptimizedContent)
    }

    /**
     * 追加Mermaid流式内容
     */
    private fun appendMermaidStreamingContent(content: String) {
        if (!isStreamingInProgress || currentStreamingCard != StreamingCardType.MERMAID) return

        streamingContent.append(content)

        // 只有内容长度变化较大时才更新UI（减少频繁更新）
        val currentLength = streamingContent.length
        if (currentLength - lastContentLength < 10 && currentLength < 500) {
            return // 跳过小幅更新，减少UI刷新频率
        }
        lastContentLength = currentLength

        updateMermaidStreamingUI()

        // 使用节流滚动
        scheduleSmartScroll(tvMermaidContent)
    }

    /**
     * 更新流式UI内容（智能总结）
     */
    private fun updateStreamingUI() {
        // 创建带有状态指示的完整内容
        val statusLine = "🔄 正在实时生成中... (${streamingContent.length} 字符)\n\n"
        val dividerLine = "─".repeat(30) + "\n\n"
        val fullContent = statusLine + dividerLine + streamingContent.toString()

        // 使用Markdown渲染（如果内容足够长且包含Markdown语法）
        if (streamingContent.length > 100 && (streamingContent.contains("**") || streamingContent.contains("#") || streamingContent.contains("-"))) {
            try {
                setMarkdownText(tvSummaryContent, fullContent)
            } catch (e: Exception) {
                Log.w(TAG, "Markdown渲染失败，使用纯文本: ${e.message}")
                tvSummaryContent.text = fullContent
            }
        } else {
            tvSummaryContent.text = fullContent
        }

        tvSummaryContent.setTextColor(getColor(R.color.apple_label))
    }

    /**
     * 更新优化内容流式UI内容
     */
    private fun updateOptimizedStreamingUI() {
        // 创建带有状态指示的完整内容
        val statusLine = "🔄 正在实时优化中... (${streamingContent.length} 字符)\n\n"
        val dividerLine = "─".repeat(30) + "\n\n"
        val fullContent = statusLine + dividerLine + streamingContent.toString()

        // 使用Markdown渲染（如果内容足够长且包含Markdown语法）
        if (streamingContent.length > 100 && (streamingContent.contains("**") || streamingContent.contains("#") || streamingContent.contains("-"))) {
            try {
                setMarkdownText(tvOptimizedContent, fullContent)
            } catch (e: Exception) {
                Log.w(TAG, "Markdown渲染失败，使用纯文本: ${e.message}")
                tvOptimizedContent.text = fullContent
            }
        } else {
            tvOptimizedContent.text = fullContent
        }

        tvOptimizedContent.setTextColor(getColor(R.color.apple_label))
    }

    /**
     * 更新Mermaid流式UI内容
     */
    private fun updateMermaidStreamingUI() {
        // 创建带有状态指示的完整内容
        val statusLine = "🔄 正在实时生成中... (${streamingContent.length} 字符)\n\n"
        val dividerLine = "─".repeat(30) + "\n\n"
        val fullContent = statusLine + dividerLine + streamingContent.toString()

        // 对于Mermaid内容，直接显示纯文本（代码）
        tvMermaidContent.text = fullContent
        tvMermaidContent.setTextColor(getColor(R.color.apple_label))
    }

    /**
     * 智能滚动调度（节流版本）
     */
    private fun scheduleSmartScroll(targetTextView: TextView) {
        // 如果用户正在手动滚动，则不自动滚动
        if (isUserScrolling) return

        val currentTime = System.currentTimeMillis()

        // 取消之前的滚动任务
        pendingScrollRunnable?.let { runnable ->
            targetTextView.removeCallbacks(runnable)
        }

        // 如果距离上次滚动时间太短，延迟执行
        val timeSinceLastScroll = currentTime - lastScrollTime
        val delay = if (timeSinceLastScroll < scrollThrottleMs) {
            scrollThrottleMs - timeSinceLastScroll
        } else {
            0L
        }

        pendingScrollRunnable = Runnable {
            performSmartScroll(targetTextView)
            lastScrollTime = System.currentTimeMillis()
            pendingScrollRunnable = null
        }

        targetTextView.postDelayed(pendingScrollRunnable, delay)
    }

    /**
     * 执行智能滚动
     */
    private fun performSmartScroll(targetTextView: TextView) {
        if (!isStreamingInProgress || isUserScrolling) return

        // 查找包含TextView的ScrollView
        var parent = targetTextView.parent
        while (parent != null) {
            if (parent is ScrollView) {
                val scrollView = parent

                // 检查是否已经在底部附近
                val scrollY = scrollView.scrollY
                val height = scrollView.height
                val contentHeight = scrollView.getChildAt(0).height
                val isNearBottom = scrollY + height >= contentHeight - 100

                // 只有在接近底部时才滚动，避免打断用户阅读
                if (isNearBottom) {
                    scrollView.smoothScrollTo(0, contentHeight)
                }
                break
            }
            parent = parent.parent
        }
    }

    /**
     * 设置用户滚动状态监听
     */
    private fun setupScrollListener(targetTextView: TextView) {
        // 查找ScrollView并设置滚动监听
        var parent = targetTextView.parent
        while (parent != null) {
            if (parent is ScrollView) {
                val scrollView = parent
                scrollView.setOnScrollChangeListener { _, _, _, _, _ ->
                    // 用户手动滚动时，暂停自动滚动
                    isUserScrolling = true

                    // 延迟恢复自动滚动
                    targetTextView.removeCallbacks(resumeAutoScrollRunnable)
                    targetTextView.postDelayed(resumeAutoScrollRunnable, 2000)
                }
                break
            }
            parent = parent.parent
        }
    }

    private val resumeAutoScrollRunnable = Runnable {
        isUserScrolling = false
    }

    /**
     * 完成流式生成（智能总结）
     */
    private fun completeStreamingGeneration(fullContent: String, summaryType: SummaryType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 清理滚动相关资源
        cleanupScrollResources()

        // 更新UI
        setMarkdownText(tvSummaryContent, fullContent)
        tvSummaryContent.setTextColor(getColor(R.color.apple_label))
        btnCopySummary.visibility = View.VISIBLE
        btnGenerateSummary.visibility = View.GONE

        // 恢复生成按钮的原始点击事件
        restoreGenerateButtonState()

        // 更新数据库
        updateMeetingRecordSummary(fullContent)

        showToast("✅ ${summaryType.displayName}生成成功")
    }

    /**
     * 完成优化内容流式生成
     */
    private fun completeOptimizedStreamingGeneration(fullContent: String) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 清理滚动相关资源
        cleanupScrollResources()

        // 更新UI
        setMarkdownText(tvOptimizedContent, fullContent)
        tvOptimizedContent.setTextColor(getColor(R.color.apple_label))
        btnCopyOptimized.visibility = View.VISIBLE
        btnGenerateOptimized.visibility = View.GONE

        // 恢复生成按钮的原始点击事件
        restoreOptimizedGenerateButtonState()

        // 更新数据库
        updateMeetingRecordOptimized(fullContent)

        showToast("✅ 内容优化完成")
    }

    /**
     * 完成Mermaid流式生成
     */
    private fun completeMermaidStreamingGeneration(fullContent: String, mermaidType: MermaidType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 清理滚动相关资源
        cleanupScrollResources()

        // 清理代码标记
        val cleanedContent = LLMManager.cleanMermaidCodeMarkers(fullContent)
        meetingRecord?.mermaidContent = cleanedContent
        updateMeetingRecordMermaid(cleanedContent)

        // 更新UI
        tvMermaidContent.text = cleanedContent
        tvMermaidContent.setTextColor(getColor(R.color.apple_label))
        btnCopyMermaid.visibility = View.VISIBLE
        btnGenerateMermaid.visibility = View.GONE
        btnToggleMermaidView.visibility = View.VISIBLE

        // 恢复生成按钮的原始点击事件
        restoreMermaidGenerateButtonState()

        // 默认显示图表视图
        isMermaidCodeView = false
        scrollMermaidCode.visibility = View.GONE
        webViewMermaid.visibility = View.VISIBLE
        btnFullscreenMermaid.visibility = View.VISIBLE
        btnToggleMermaidView.text = getString(R.string.mermaid_view_code)

        // 渲染Mermaid图表
        renderMermaidDiagram(cleanedContent)

        showToast("✅ ${mermaidType.displayName}生成完成")
    }

    /**
     * 处理流式生成错误（智能总结）
     */
    private fun handleStreamingError(error: String, summaryType: SummaryType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 恢复UI状态
        tvSummaryContent.text = "点击下方按钮生成AI总结"
        tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopySummary.visibility = View.GONE
        btnGenerateSummary.visibility = View.VISIBLE

        // 恢复生成按钮的原始点击事件
        restoreGenerateButtonState()

        showToast("生成${summaryType.displayName}失败: $error")
    }

    /**
     * 处理优化内容流式生成错误
     */
    private fun handleOptimizedStreamingError(error: String) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 恢复UI状态
        tvOptimizedContent.text = "点击下方按钮生成AI优化内容"
        tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopyOptimized.visibility = View.GONE
        btnGenerateOptimized.visibility = View.VISIBLE

        // 恢复生成按钮的原始点击事件
        restoreOptimizedGenerateButtonState()

        showToast("内容优化失败: $error")
    }

    /**
     * 处理Mermaid流式生成错误
     */
    private fun handleMermaidStreamingError(error: String, mermaidType: MermaidType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // 恢复UI状态
        tvMermaidContent.text = getString(R.string.mermaid_empty_hint)
        tvMermaidContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopyMermaid.visibility = View.GONE
        btnGenerateMermaid.visibility = View.VISIBLE
        btnToggleMermaidView.visibility = View.GONE

        // 隐藏WebView
        webViewMermaid.visibility = View.GONE
        scrollMermaidCode.visibility = View.VISIBLE
        btnFullscreenMermaid.visibility = View.GONE

        // 恢复生成按钮的原始点击事件
        restoreMermaidGenerateButtonState()

        showToast("生成${mermaidType.displayName}失败: $error")
    }

    /**
     * 取消流式生成（智能总结）
     */
    private fun cancelStreamingGeneration(summaryType: SummaryType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // TODO: 实现实际的取消逻辑（取消网络请求等）

        // 恢复UI状态
        tvSummaryContent.text = "点击下方按钮生成AI总结"
        tvSummaryContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopySummary.visibility = View.GONE
        btnGenerateSummary.visibility = View.VISIBLE

        // 恢复生成按钮的原始点击事件
        restoreGenerateButtonState()

        showToast("已取消${summaryType.displayName}生成")
    }

    /**
     * 取消优化内容流式生成
     */
    private fun cancelOptimizedStreamingGeneration() {
        isStreamingInProgress = false
        currentStreamingCard = null

        // TODO: 实现实际的取消逻辑（取消网络请求等）

        // 恢复UI状态
        tvOptimizedContent.text = "点击下方按钮生成AI优化内容"
        tvOptimizedContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopyOptimized.visibility = View.GONE
        btnGenerateOptimized.visibility = View.VISIBLE

        // 恢复生成按钮的原始点击事件
        restoreOptimizedGenerateButtonState()

        showToast("已取消内容优化")
    }

    /**
     * 取消Mermaid流式生成
     */
    private fun cancelMermaidStreamingGeneration(mermaidType: MermaidType) {
        isStreamingInProgress = false
        currentStreamingCard = null

        // TODO: 实现实际的取消逻辑（取消网络请求等）

        // 恢复UI状态
        tvMermaidContent.text = getString(R.string.mermaid_empty_hint)
        tvMermaidContent.setTextColor(getColor(R.color.apple_secondary_label))
        btnCopyMermaid.visibility = View.GONE
        btnGenerateMermaid.visibility = View.VISIBLE
        btnToggleMermaidView.visibility = View.GONE

        // 隐藏WebView
        webViewMermaid.visibility = View.GONE
        scrollMermaidCode.visibility = View.VISIBLE
        btnFullscreenMermaid.visibility = View.GONE

        // 恢复生成按钮的原始点击事件
        restoreMermaidGenerateButtonState()

        showToast("已取消${mermaidType.displayName}生成")
    }

    /**
     * 恢复生成按钮的原始状态
     */
     private fun restoreGenerateButtonState() {
        btnGenerateSummary.text = "🤖 生成总结"
        btnGenerateSummary.setOnClickListener { generateMeetingSummary() }
    }

    /**
     * 恢复优化内容生成按钮的原始状态
     */
    private fun restoreOptimizedGenerateButtonState() {
        btnGenerateOptimized.text = "🤖 生成优化"
        btnGenerateOptimized.setOnClickListener { generateOptimizedContent() }
    }

    /**
     * 恢复Mermaid生成按钮的原始状态
     */
    private fun restoreMermaidGenerateButtonState() {
        btnGenerateMermaid.text = "🤖 生成图表"
        btnGenerateMermaid.setOnClickListener { generateMermaidDiagram() }
    }

    /**
     * 清理滚动相关资源
     */
    private fun cleanupScrollResources() {
        // 取消待执行的滚动任务
        pendingScrollRunnable?.let { runnable ->
            tvSummaryContent.removeCallbacks(runnable)
            tvOptimizedContent.removeCallbacks(runnable)
            tvMermaidContent.removeCallbacks(runnable)
        }
        pendingScrollRunnable = null

        // 取消恢复自动滚动的任务
        tvSummaryContent.removeCallbacks(resumeAutoScrollRunnable)
        tvOptimizedContent.removeCallbacks(resumeAutoScrollRunnable)
        tvMermaidContent.removeCallbacks(resumeAutoScrollRunnable)

        // 重置状态
        isUserScrolling = false
        lastContentLength = 0
        lastScrollTime = 0L
    }

    // 注释：已移除流式对话框相关代码，改为直接在卡片上显示流式内容
    // 这样可以提供更好的用户体验，避免弹窗干扰

    /**
     * 基于会议内容生成TODO
     */
    private fun generateTodoFromMeeting() {
        val record = meetingRecord ?: return

        if (record.originalContent.trim().isEmpty()) {
            showToast("会议内容为空，无法生成TODO")
            return
        }

        // 检查LLM是否可用
        lifecycleScope.launch {
            if (!LLMManager.isCurrentLLMAvailable(this@MeetingDetailActivity)) {
                showLLMConfigDialog()
                return@launch
            }

            // 显示进度对话框
            val progressDialog = AppleProgressDialog(
                context = this@MeetingDetailActivity,
                title = "🤖 AI生成待办事项",
                message = "正在分析会议内容...\n\n✨ 智能识别关键任务和行动项\n📝 自动设置优先级和分类",
                cancelable = true,
                cancelButtonText = "取消"
            )
            progressDialog.show()

            try {
                val todoItems = todoGenerator.generateTodosFromMeeting(
                    originalContent = record.originalContent,
                    optimizedContent = record.optimizedContent,
                    meetingRecordId = record.id,
                    meetingTitle = record.title
                )

                runOnUiThread {
                    progressDialog.dismiss()

                    if (todoItems.isNotEmpty()) {
                        // 保存生成的TODO项
                        val success = todoManager.addTodos(todoItems)
                        if (success) {
                            loadTodoList()
                            showToast("✅ 成功生成 ${todoItems.size} 个待办事项")
                        } else {
                            showToast("保存TODO失败")
                        }
                    } else {
                        showToast("未能从会议内容中识别出待办事项")
                    }
                }

            } catch (e: Exception) {
                runOnUiThread {
                    progressDialog.dismiss()
                    Log.e(TAG, "生成TODO失败", e)
                    showToast("生成失败: ${e.message}")
                }
            }
        }
    }



    /**
     * 打开TODO列表页面
     */
    private fun openTodoList() {
        val intent = Intent(this, TodoActivity::class.java)
        startActivity(intent)
    }

    /**
     * 加载TODO列表
     */
    private fun loadTodoList() {
        val record = meetingRecord ?: return

        lifecycleScope.launch {
            try {
                val todos = todoManager.getTodosByMeetingRecord(record.id)

                runOnUiThread {
                    displayTodoList(todos)
                }

            } catch (e: Exception) {
                Log.e(TAG, "加载TODO列表失败", e)
            }
        }
    }

    /**
     * 显示TODO列表
     */
    private fun displayTodoList(todos: List<TodoItem>) {
        layoutTodoList.removeAllViews()

        if (todos.isEmpty()) {
            layoutTodoEmpty.visibility = View.VISIBLE
            layoutTodoList.visibility = View.GONE
        } else {
            layoutTodoEmpty.visibility = View.GONE
            layoutTodoList.visibility = View.VISIBLE

            todos.take(5).forEach { todo -> // 最多显示5个
                val todoView = createTodoItemView(todo)
                layoutTodoList.addView(todoView)
            }

            // 如果有更多TODO项，显示查看全部按钮
            if (todos.size > 5) {
                val moreView = createMoreTodoView(todos.size - 5)
                layoutTodoList.addView(moreView)
            }
        }
    }

    /**
     * 创建TODO项视图
     */
    private fun createTodoItemView(todo: TodoItem): View {
        val itemView = layoutInflater.inflate(R.layout.item_todo_simple, null)

        val checkBox = itemView.findViewById<CheckBox>(R.id.cb_completed)
        val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        val tvPriority = itemView.findViewById<TextView>(R.id.tv_priority)
        val tvCategory = itemView.findViewById<TextView>(R.id.tv_category)

        checkBox.isChecked = todo.isCompleted
        tvTitle.text = todo.title
        tvPriority.text = todo.priority.displayName
        tvCategory.text = todo.category

        // 设置优先级颜色
        tvPriority.setBackgroundColor(android.graphics.Color.parseColor(todo.priority.color))
        tvPriority.setTextColor(android.graphics.Color.WHITE)

        // 设置完成状态样式
        if (todo.isCompleted) {
            tvTitle.paintFlags = tvTitle.paintFlags or android.graphics.Paint.STRIKE_THRU_TEXT_FLAG
            tvTitle.alpha = 0.6f
        }

        // 点击事件
        checkBox.setOnClickListener {
            toggleTodoCompletion(todo)
        }

        itemView.setOnClickListener {
            // 打开TODO编辑页面
            val intent = Intent(this, TodoEditActivity::class.java)
            intent.putExtra("todo_id", todo.id)
            startActivity(intent)
        }

        return itemView
    }

    /**
     * 创建"查看更多"视图
     */
    private fun createMoreTodoView(moreCount: Int): View {
        val moreView = TextView(this)
        moreView.text = "还有 $moreCount 个待办事项，点击查看全部"
        moreView.textSize = 14f
        moreView.setTextColor(resources.getColor(R.color.apple_blue, null))
        moreView.gravity = android.view.Gravity.CENTER
        moreView.setPadding(16, 12, 16, 12)
        moreView.background = resources.getDrawable(R.drawable.more_todo_background, null)

        moreView.setOnClickListener {
            openTodoList()
        }

        return moreView
    }

    /**
     * 切换TODO完成状态
     */
    private fun toggleTodoCompletion(todo: TodoItem) {
        lifecycleScope.launch {
            try {
                val success = if (todo.isCompleted) {
                    todoManager.markTodoAsIncomplete(todo.id)
                } else {
                    todoManager.markTodoAsCompleted(todo.id)
                }

                if (success) {
                    loadTodoList()
                    val message = if (todo.isCompleted) "已标记为未完成" else "已标记为完成"
                    showToast(message)
                }

            } catch (e: Exception) {
                Log.e(TAG, "切换TODO状态失败", e)
            }
        }
    }

    /**
     * 显示LLM配置对话框
     */
    // private fun showLLMConfigDialog() {
    //     val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
    //
    //     AppleInfoDialog(
    //         context = this,
    //         title = "LLM 配置",
    //         message = "当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。",
    //         positiveButtonText = "打开设置",
    //         negativeButtonText = "取消",
    //         onPositiveClick = {
    //             val intent = Intent(this, SettingsActivity::class.java)
    //             startActivity(intent)
    //         }
    //     ).show()
    // }

    /**
     * 启动AI聊天页面
     */
    private fun startAiChat() {
        val record = meetingRecord ?: return

        val intent = Intent(this, AiChatActivity::class.java)
        intent.putExtra("meeting_record_id", record.id)
        intent.putExtra("meeting_title", record.title)
        intent.putExtra("meeting_content", record.originalContent)
        startActivityForResult(intent, 1001)
    }
    

    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1001 && resultCode == RESULT_OK) {
            // 从聊天页面返回，无需刷新聊天历史（聊天卡片仅作为入口）
        }
    }

    override fun onResume() {
        super.onResume()
        // 从设置页面返回时刷新显示状态
        meetingRecord?.let {
            displayMeetingRecord(it)
            loadTodoList()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 释放MediaPlayer资源
        stopAudioPlayback()
    }
}
