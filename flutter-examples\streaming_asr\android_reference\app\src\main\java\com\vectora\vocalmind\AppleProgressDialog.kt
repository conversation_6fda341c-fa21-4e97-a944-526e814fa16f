package com.vectora.vocalmind

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView

/**
 * 苹果风格的进度对话框
 */
class AppleProgressDialog(
    context: Context,
    private val title: String,
    private val message: String,
    private val cancelable: Boolean = true,
    private val cancelButtonText: String? = null,
    private val onCancelClick: (() -> Unit)? = null
) : Dialog(context) {

    private lateinit var tvTitle: TextView
    private lateinit var tvMessage: TextView
    private lateinit var btnCancel: Button
    private lateinit var btnClose: ImageView
    private lateinit var progressBar: ProgressBar
    private lateinit var cancelContainer: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置无标题栏
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        
        // 加载布局
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_apple_progress, null)
        setContentView(view)
        
        // 设置窗口属性
        setupWindow()
        
        // 初始化视图
        initViews(view)
        
        // 设置数据
        setupData()
        
        // 设置点击事件
        setupClickListeners()
    }
    
    private fun setupWindow() {
        window?.let { window ->
            // 设置背景透明
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            
            // 设置窗口属性
            val layoutParams = window.attributes
            layoutParams.width = (context.resources.displayMetrics.widthPixels * 0.85).toInt()
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.gravity = Gravity.CENTER
            
            // 设置窗口动画
            window.setWindowAnimations(android.R.style.Animation_Dialog)
            
            // 设置窗口标志
            window.setFlags(
                WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                WindowManager.LayoutParams.FLAG_DIM_BEHIND
            )
            
            // 设置背景暗度
            layoutParams.dimAmount = 0.5f
            
            window.attributes = layoutParams
        }
    }
    
    private fun initViews(view: View) {
        tvTitle = view.findViewById(R.id.tv_title)
        tvMessage = view.findViewById(R.id.tv_message)
        btnCancel = view.findViewById(R.id.btn_cancel)
        btnClose = view.findViewById(R.id.btn_close)
        progressBar = view.findViewById(R.id.progress_bar)
        cancelContainer = view.findViewById(R.id.cancel_container)
    }
    
    private fun setupData() {
        tvTitle.text = title
        tvMessage.text = message
        
        // 根据是否可取消来调整布局
        if (cancelable && cancelButtonText != null) {
            btnCancel.text = cancelButtonText
            cancelContainer.visibility = View.VISIBLE
        } else {
            cancelContainer.visibility = View.GONE
        }
        
        // 设置是否可以通过返回键取消
        setCancelable(cancelable)
    }
    
    private fun setupClickListeners() {
        // 关闭按钮（只有在可取消时显示）
        if (cancelable) {
            btnClose.setOnClickListener {
                onCancelClick?.invoke()
                dismiss()
            }
        } else {
            btnClose.visibility = View.GONE
        }
        
        // 取消按钮
        btnCancel.setOnClickListener {
            onCancelClick?.invoke()
            dismiss()
        }
        
        // 设置点击外部区域是否可以关闭弹窗
        setCanceledOnTouchOutside(cancelable)
    }
    
    /**
     * 更新进度消息
     */
    fun updateMessage(newMessage: String) {
        tvMessage.text = newMessage
    }
}