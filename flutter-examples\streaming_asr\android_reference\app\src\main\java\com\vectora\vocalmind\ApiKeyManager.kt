package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import android.util.Log
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

/**
 * API 密钥安全管理器
 * 支持多种密钥来源：BuildConfig、加密存储、用户输入
 */
object ApiKeyManager {
    private const val TAG = "ApiKeyManager"
    private const val PREFS_NAME = "secure_prefs"
    private const val KEY_GEMINI_API = "gemini_api_key"
    private const val KEY_ENCRYPTION_KEY = "encryption_key"
    
    /**
     * 获取 Gemini API 密钥
     * 优先级：BuildConfig > 加密存储 > 空字符串
     */
    fun getGeminiApiKey(context: Context): String {
        // 1. 首先检查 BuildConfig 中的预配置密钥
        val buildConfigKey = BuildConfig.GEMINI_API_KEY
        if (buildConfigKey.isNotBlank() && buildConfigKey != "YOUR_GEMINI_API_KEY_HERE") {
            Log.d(TAG, "使用 BuildConfig 中的 API 密钥")
            return buildConfigKey
        }
        
        // 2. 检查加密存储中的用户输入密钥
        val storedKey = getStoredApiKey(context)
        if (storedKey.isNotBlank()) {
            Log.d(TAG, "使用存储的 API 密钥")
            return storedKey
        }
        
        // 3. 没有找到有效密钥
        Log.d(TAG, "未找到有效的 API 密钥")
        return ""
    }
    
    /**
     * 保存 API 密钥到加密存储
     */
    fun saveApiKey(context: Context, apiKey: String): Boolean {
        return try {
            val encryptedKey = encryptString(context, apiKey)
            val prefs = getSecurePrefs(context)
            prefs.edit().putString(KEY_GEMINI_API, encryptedKey).apply()
            Log.d(TAG, "API 密钥已保存")
            true
        } catch (e: Exception) {
            Log.e(TAG, "保存 API 密钥失败", e)
            false
        }
    }
    
    /**
     * 从加密存储获取 API 密钥
     */
    private fun getStoredApiKey(context: Context): String {
        return try {
            val prefs = getSecurePrefs(context)
            val encryptedKey = prefs.getString(KEY_GEMINI_API, "") ?: ""
            if (encryptedKey.isNotBlank()) {
                decryptString(context, encryptedKey)
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取存储的 API 密钥失败", e)
            ""
        }
    }
    
    /**
     * 清除存储的 API 密钥
     */
    fun clearStoredApiKey(context: Context): Boolean {
        return try {
            val prefs = getSecurePrefs(context)
            prefs.edit().remove(KEY_GEMINI_API).apply()
            Log.d(TAG, "已清除存储的 API 密钥")
            true
        } catch (e: Exception) {
            Log.e(TAG, "清除 API 密钥失败", e)
            false
        }
    }
    
    /**
     * 检查是否有可用的 API 密钥
     */
    fun hasValidApiKey(context: Context): Boolean {
        val apiKey = getGeminiApiKey(context)
        return apiKey.isNotBlank() && apiKey != "YOUR_GEMINI_API_KEY_HERE"
    }
    
    /**
     * 获取完整的 API URL
     */
    fun getApiUrl(context: Context): String {
        val apiKey = getGeminiApiKey(context)
        return "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=$apiKey"
    }
    
    // ==================== 加密相关方法 ====================
    
    private fun getSecurePrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    private fun getOrCreateEncryptionKey(context: Context): SecretKey {
        val prefs = getSecurePrefs(context)
        val keyString = prefs.getString(KEY_ENCRYPTION_KEY, null)
        
        return if (keyString != null) {
            // 使用现有密钥
            val keyBytes = Base64.decode(keyString, Base64.DEFAULT)
            SecretKeySpec(keyBytes, "AES")
        } else {
            // 生成新密钥
            val keyGenerator = KeyGenerator.getInstance("AES")
            keyGenerator.init(256)
            val secretKey = keyGenerator.generateKey()
            
            // 保存密钥
            val keyBytes = secretKey.encoded
            val keyString64 = Base64.encodeToString(keyBytes, Base64.DEFAULT)
            prefs.edit().putString(KEY_ENCRYPTION_KEY, keyString64).apply()
            
            secretKey
        }
    }
    
    private fun encryptString(context: Context, plainText: String): String {
        val secretKey = getOrCreateEncryptionKey(context)
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.ENCRYPT_MODE, secretKey)
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        return Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
    }
    
    private fun decryptString(context: Context, encryptedText: String): String {
        val secretKey = getOrCreateEncryptionKey(context)
        val cipher = Cipher.getInstance("AES")
        cipher.init(Cipher.DECRYPT_MODE, secretKey)
        val encryptedBytes = Base64.decode(encryptedText, Base64.DEFAULT)
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }
}
