package com.vectora.vocalmind

import android.content.Context
import android.media.MediaMetadataRetriever
import android.util.Log
import kotlinx.coroutines.*
import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 音频文件转录器
 * 负责读取WAV文件并进行ASR转录
 */
class AudioFileTranscriber(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioFileTranscriber"
        private const val SAMPLE_RATE = 16000
        private const val CHUNK_SIZE = 3200 // 200ms的音频数据，为声纹识别提供更多上下文
    }
    
    interface TranscriptionCallback {
        fun onProgress(progress: Int, currentText: String)
        fun onComplete(finalText: String)
        fun onError(error: String)
    }
    
    /**
     * 转录音频文件
     */
    suspend fun transcribeAudioFile(
        audioFilePath: String,
        callback: TranscriptionCallback
    ) = withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "开始转录音频文件: $audioFilePath")
            
            // 检查文件是否存在
            val audioFile = File(audioFilePath)
            if (!audioFile.exists()) {
                callback.onError("音频文件不存在")
                return@withContext
            }
            
            // 获取音频文件信息
            val audioInfo = getAudioFileInfo(audioFilePath)
            Log.d(TAG, "音频文件信息: 时长=${audioInfo.duration}ms, 大小=${audioFile.length()}bytes")

            // 创建新的ASR引擎实例用于文件转录
            val asrEngine = SingleModelASREngine(
                assetManager = context.assets,
                sampleRateInHz = SAMPLE_RATE,
                enableSpeakerIdentification = true, // 启用声纹识别以保持完整功能
                speakerThreshold = 0.3f, // 使用与实时录音相同的阈值
                context = context
            )

            // 初始化ASR引擎
            if (!asrEngine.initialize()) {
                callback.onError("ASR引擎初始化失败")
                return@withContext
            }

            // 使用流式处理音频数据（避免内存溢出）
            val success = processAudioFileStreaming(audioFilePath, asrEngine, callback)
            if (!success) {
                callback.onError("音频文件处理失败")
                return@withContext
            }

            // 释放ASR引擎资源
            asrEngine.release()
            
        } catch (e: Exception) {
            Log.e(TAG, "转录过程中发生错误", e)
            withContext(Dispatchers.Main) {
                callback.onError("转录失败: ${e.message}")
            }
        }
    }

    /**
     * 流式处理音频文件（真正的流式处理，避免内存溢出）
     */
    private suspend fun processAudioFileStreaming(
        audioFilePath: String,
        asrEngine: SingleModelASREngine,
        callback: TranscriptionCallback
    ): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val file = File(audioFilePath)
            val fileSize = file.length()
            Log.i(TAG, "开始流式处理音频文件: $audioFilePath, 大小: ${fileSize / 1024 / 1024}MB")

            // 严格的内存检查
            val runtime = Runtime.getRuntime()
            val freeMemory = runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val usedMemory = runtime.totalMemory() - freeMemory
            val availableMemory = maxMemory - usedMemory

            Log.i(TAG, "内存状态 - 可用: ${availableMemory / 1024 / 1024}MB, 已用: ${usedMemory / 1024 / 1024}MB, 最大: ${maxMemory / 1024 / 1024}MB")

            // 如果可用内存不足20MB，先进行垃圾回收
            if (availableMemory < 20 * 1024 * 1024) {
                Log.w(TAG, "可用内存不足20MB，执行垃圾回收")
                System.gc()
                Thread.sleep(100) // 给GC一些时间

                // 重新检查内存
                val newFreeMemory = runtime.freeMemory()
                val newUsedMemory = runtime.totalMemory() - newFreeMemory
                val newAvailableMemory = maxMemory - newUsedMemory

                Log.i(TAG, "GC后内存状态 - 可用: ${newAvailableMemory / 1024 / 1024}MB")

                // 如果GC后仍然内存不足，返回错误
                if (newAvailableMemory < 15 * 1024 * 1024) {
                    Log.e(TAG, "内存不足，无法处理音频文件")
                    withContext(Dispatchers.Main) {
                        callback.onError("内存不足，请关闭其他应用后重试")
                    }
                    return@withContext false
                }
            }

            val transcriptionResult = StringBuilder()
            var lastResult = ""
            var processedChunks = 0

            // 估算总块数（用于进度计算）
            val estimatedTotalSamples = (fileSize - 44) / 2 // 减去WAV头，除以2（16位）
            val estimatedTotalChunks = (estimatedTotalSamples / CHUNK_SIZE).toInt() + 1

            // 设置ASR监听器来收集结果
            asrEngine.setListener(object : SingleModelASREngine.ASRListener {
                override fun onResult(result: SingleModelASREngine.ASRResult) {
                    when (result.type) {
                        SingleModelASREngine.ResultType.PREVIEW -> {
                            // 更新当前预览结果，包含说话人信息
                            val previewText = if (result.hasSpeakerInfo && result.speakerName.isNotEmpty()) {
                                "${result.speakerName}: ${result.text}"
                            } else {
                                result.text
                            }
                            lastResult = previewText
                        }
                        SingleModelASREngine.ResultType.FINAL -> {
                            // 累积最终结果，包含说话人信息
                            if (result.text.isNotEmpty()) {
                                val finalText = if (result.hasSpeakerInfo && result.speakerName.isNotEmpty()) {
                                    "${result.speakerName}: ${result.text}"
                                } else {
                                    result.text
                                }

                                if (transcriptionResult.isNotEmpty()) {
                                    transcriptionResult.append("\n")
                                }
                                transcriptionResult.append(finalText)
                                lastResult = transcriptionResult.toString()

                                Log.d(TAG, "收集到最终结果: $finalText (说话人: ${result.speakerName})")
                            }
                        }
                        SingleModelASREngine.ResultType.ENDPOINT -> {
                            // 端点检测，可以用于分段
                        }
                    }
                }

                override fun onError(error: String) {
                    Log.w(TAG, "ASR处理错误: $error")
                }

                override fun onStatusChanged(status: String) {
                    Log.d(TAG, "ASR状态变化: $status")
                }

                override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
                    Log.d(TAG, "识别到说话人: ${speakerInfo.name}")
                }

                override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
                    Log.d(TAG, "说话人注册: $speakerName, 成功: $success")
                }

                override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
                    Log.d(TAG, "说话人移除: $speakerName, 成功: $success")
                }

                override fun onVadStatusChanged(isSpeech: Boolean) {
                    Log.d(TAG, "VAD状态变化: ${if (isSpeech) "检测到语音" else "静音"}")
                }
            })

            // 流式读取和处理音频数据
            FileInputStream(audioFilePath).use { fis ->
                // 跳过WAV文件头（44字节）
                fis.skip(44)

                val chunkBuffer = ByteArray(CHUNK_SIZE * 2) // 每个样本2字节
                var bytesRead: Int

                while (fis.read(chunkBuffer).also { bytesRead = it } > 0 && isActive) {
                    // 确保字节数是偶数（16位样本）
                    val actualBytes = if (bytesRead % 2 == 0) bytesRead else bytesRead - 1
                    if (actualBytes <= 0) break

                    // 转换为Short数组
                    val sampleCount = actualBytes / 2
                    val samples = FloatArray(sampleCount)
                    val byteBuffer = ByteBuffer.wrap(chunkBuffer, 0, actualBytes).order(ByteOrder.LITTLE_ENDIAN)

                    for (i in 0 until sampleCount) {
                        val shortSample = byteBuffer.short
                        samples[i] = shortSample / 32768.0f // 归一化到[-1, 1]
                    }

                    // 处理音频块
                    asrEngine.processAudio(samples)
                    processedChunks++

                    // 更新进度
                    val progress = minOf((processedChunks * 100) / estimatedTotalChunks, 100)
                    withContext(Dispatchers.Main) {
                        callback.onProgress(progress, lastResult)
                    }

                    // 适当延迟，给ASR引擎处理时间
                    delay(50)

                    // 定期进行垃圾回收
                    if (processedChunks % 100 == 0) {
                        System.gc()
                        Log.d(TAG, "已处理 $processedChunks 个音频块")
                    }
                }
            }

            // 等待最终处理完成
            delay(1000)

            val finalText = if (transcriptionResult.isNotEmpty()) {
                transcriptionResult.toString()
            } else {
                lastResult
            }

            Log.i(TAG, "流式处理完成，最终文本长度: ${finalText.length}")

            withContext(Dispatchers.Main) {
                callback.onComplete(finalText)
            }

            true

        } catch (e: Exception) {
            Log.e(TAG, "流式处理音频文件失败", e)
            withContext(Dispatchers.Main) {
                callback.onError("音频处理失败: ${e.message}")
            }
            false
        }
    }

    /**
     * 读取WAV文件数据（已弃用，使用流式处理替代）
     * @deprecated 此方法可能导致内存溢出，请使用 processAudioFileStreaming
     */
    @Deprecated("使用 processAudioFileStreaming 替代，避免内存溢出")
    private fun readWavFile(filePath: String): ShortArray {
        Log.w(TAG, "使用已弃用的 readWavFile 方法，建议使用流式处理")
        return shortArrayOf()
    }

    /**
     * 流式读取WAV文件数据（已弃用，使用真正的流式处理替代）
     * @deprecated 此方法仍可能导致内存溢出，请使用 processAudioFileStreaming
     */
    @Deprecated("使用 processAudioFileStreaming 替代，避免内存溢出")
    private fun readWavFileStreaming(filePath: String): ShortArray {
        Log.w(TAG, "使用已弃用的 readWavFileStreaming 方法，建议使用真正的流式处理")
        return shortArrayOf()
    }
    
    /**
     * 获取音频文件信息
     */
    private fun getAudioFileInfo(filePath: String): AudioInfo {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(filePath)
            
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            val sampleRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_SAMPLERATE)?.toIntOrNull() ?: SAMPLE_RATE
            
            retriever.release()
            
            AudioInfo(duration, sampleRate)
        } catch (e: Exception) {
            Log.w(TAG, "获取音频文件信息失败", e)
            AudioInfo(0L, SAMPLE_RATE)
        }
    }
    
    /**
     * 音频文件信息
     */
    data class AudioInfo(
        val duration: Long,  // 时长（毫秒）
        val sampleRate: Int  // 采样率
    )
}
