package com.vectora.vocalmind

import android.content.Context
import org.json.JSONArray
import org.json.JSONObject

/**
 * DeepSeek API 配置
 * 实现LLMConfig接口，提供DeepSeek特定的API调用逻辑
 */
object DeepSeekConfig : LLMConfig {
    
    // DeepSeek API 端点
    private const val API_ENDPOINT = "https://api.deepseek.com/v1/chat/completions"
    
    // 默认模型
    private const val MODEL_NAME = "deepseek-chat"
    
    override fun getProvider(): LLMProvider = LLMProvider.DEEPSEEK
    
    override fun isApiKeyConfigured(context: Context): Bo<PERSON>an {
        return LLMApiKeyManager.hasValidApiKey(context, LLMProvider.DEEPSEEK)
    }
    
    override fun getApiUrl(context: Context): String {
        return API_ENDPOINT
    }
    
    override fun getApiKey(context: Context): String {
        return LLMApiKeyManager.getApiKey(context, LLMProvider.DEEPSEEK)
    }
    
    override fun getModelName(): String = MODEL_NAME
    
    override fun buildRequestBody(prompt: String): String {
        val requestBody = JSONObject().apply {
            put("model", MODEL_NAME)
            put("messages", JSONArray().apply {
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", prompt)
                })
            })
            put("temperature", 0.3)
            put("max_tokens", 8192)
            put("top_p", 0.8)
            put("stream", false)
        }
        return requestBody.toString()
    }
    
    override fun buildStreamingRequestBody(prompt: String): String {
        val requestBody = JSONObject().apply {
            put("model", MODEL_NAME)
            put("messages", JSONArray().apply {
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", prompt)
                })
            })
            put("temperature", 0.3)
            put("max_tokens", 8192)
            put("top_p", 0.8)
            put("stream", true)
        }
        return requestBody.toString()
    }
    
    override fun parseResponse(response: String): String {
        return try {
            val jsonResponse = JSONObject(response)
            val choices = jsonResponse.getJSONArray("choices")
            if (choices.length() > 0) {
                val choice = choices.getJSONObject(0)
                val message = choice.getJSONObject("message")
                message.getString("content").trim()
            } else {
                throw Exception("API返回空结果")
            }
        } catch (e: Exception) {
            throw Exception("解析DeepSeek响应失败: ${e.message}")
        }
    }
    
    override fun parseStreamingChunk(chunk: String): String? {
        return try {
            // DeepSeek流式响应格式: data: {"choices":[{"delta":{"content":"文本"}}]}
            if (chunk.startsWith("data: ")) {
                val jsonData = chunk.substring(6).trim()
                if (jsonData == "[DONE]") {
                    return null // 流结束标志
                }
                val jsonObject = JSONObject(jsonData)
                val choices = jsonObject.getJSONArray("choices")
                if (choices.length() > 0) {
                    val choice = choices.getJSONObject(0)
                    val delta = choice.getJSONObject("delta")
                    if (delta.has("content")) {
                        return delta.getString("content")
                    }
                }
            }
            null
        } catch (e: Exception) {
            null
        }
    }
    
    override fun getHeaders(context: Context): Map<String, String> {
        val apiKey = getApiKey(context)
        return mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer $apiKey"
        )
    }
    
    override fun getStreamingHeaders(context: Context): Map<String, String> {
        val apiKey = getApiKey(context)
        return mapOf(
            "Content-Type" to "application/json",
            "Authorization" to "Bearer $apiKey",
            "Accept" to "text/event-stream",
            "Cache-Control" to "no-cache"
        )
    }
}
