package com.vectora.vocalmind

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 聊天管理器 - 负责聊天记录的存储和管理
 */
class ChatManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "ChatManager"
        private const val PREFS_NAME = "chat_prefs"
        private const val KEY_CHAT_HISTORY = "chat_history_"
        private const val MAX_MESSAGES_PER_MEETING = 100 // 每个会议最多保存100条聊天记录

        @Volatile
        private var INSTANCE: ChatManager? = null

        fun getInstance(context: Context): ChatManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ChatManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * 保存聊天消息
     */
    fun saveChatMessage(message: ChatMessage) {
        try {
            Log.d(TAG, "开始保存聊天消息: ID=${message.id}, meetingRecordId=${message.meetingRecordId}")
            
            val messages = getChatHistory(message.meetingRecordId).toMutableList()
            Log.d(TAG, "当前历史消息数量: ${messages.size}")
            
            // 检查消息是否已存在（基于ID）
            val existingIndex = messages.indexOfFirst { it.id == message.id }
            if (existingIndex != -1) {
                // 更新已存在的消息内容（用于流式输出场景）
                Log.d(TAG, "找到已存在的消息，索引: $existingIndex")
                messages[existingIndex] = message
                Log.d(TAG, "更新已存在的聊天消息: ${message.id}")
            } else {
                // 添加新消息
                Log.d(TAG, "添加新消息到历史记录")
                messages.add(message)
                Log.d(TAG, "添加新的聊天消息: ${message.id}")
            }
            
            // 限制消息数量
            if (messages.size > MAX_MESSAGES_PER_MEETING) {
                Log.d(TAG, "消息数量超限，移除最旧的消息")
                messages.removeAt(0) // 移除最旧的消息
            }
            
            val json = gson.toJson(messages)
            Log.d(TAG, "准备保存JSON，长度: ${json.length}")
            
            val success = prefs.edit()
                .putString(KEY_CHAT_HISTORY + message.meetingRecordId, json)
                .commit() // 使用commit()而不是apply()以获得同步结果
            
            if (success) {
                Log.d(TAG, "聊天消息已成功保存到SharedPreferences: ${message.meetingRecordId}")
            } else {
                Log.e(TAG, "保存到SharedPreferences失败: ${message.meetingRecordId}")
            }
            
            // 验证保存结果
            val verifyMessages = getChatHistory(message.meetingRecordId)
            Log.d(TAG, "验证保存结果，消息数量: ${verifyMessages.size}")
            
        } catch (e: Exception) {
            Log.e(TAG, "保存聊天消息失败", e)
            throw e // 重新抛出异常以便上层捕获
        }
    }

    /**
     * 获取指定会议的聊天历史
     */
    fun getChatHistory(meetingRecordId: String): List<ChatMessage> {
        return try {
            val json = prefs.getString(KEY_CHAT_HISTORY + meetingRecordId, null)
            if (json != null) {
                val type = object : TypeToken<List<ChatMessage>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取聊天历史失败", e)
            emptyList()
        }
    }

    /**
     * 清空指定会议的聊天历史
     */
    fun clearChatHistory(meetingRecordId: String) {
        try {
            prefs.edit()
                .remove(KEY_CHAT_HISTORY + meetingRecordId)
                .apply()
            Log.d(TAG, "聊天历史已清空: $meetingRecordId")
        } catch (e: Exception) {
            Log.e(TAG, "清空聊天历史失败", e)
        }
    }

    /**
     * 获取最近的聊天记录（用于在详情页显示）
     */
    fun getRecentChatHistory(meetingRecordId: String, limit: Int = 3): List<ChatMessage> {
        val allMessages = getChatHistory(meetingRecordId)
        return if (allMessages.size <= limit) {
            allMessages
        } else {
            allMessages.takeLast(limit)
        }
    }

    /**
     * 检查是否有聊天记录
     */
    fun hasChatHistory(meetingRecordId: String): Boolean {
        return getChatHistory(meetingRecordId).isNotEmpty()
    }

    /**
     * 删除指定会议的所有聊天数据
     */
    fun deleteChatData(meetingRecordId: String) {
        clearChatHistory(meetingRecordId)
    }
}