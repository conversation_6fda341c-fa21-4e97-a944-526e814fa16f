package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

/**
 * 用户流程状态枚举
 */
enum class UserFlowState {
    DIRECT_TO_MAIN,    // 直接进入主应用（已登录或隐私模式且已完成引导）
    SHOW_AUTH_ONLY,    // 只显示认证选项（服务器模式且已完成引导但未登录）
    SHOW_WELCOME       // 显示完整欢迎流程（首次使用）
}

/**
 * 欢迎页面 - SaaS标准入口页面
 * 提供品牌展示、功能介绍和认证入口
 */
class WelcomeActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "WelcomeActivity"
    }

    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private lateinit var btnLogin: Button
    private lateinit var btnRegister: Button
    private lateinit var btnSkip: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_welcome)

        // 检查用户状态并决定流程
        when (getUserFlowState()) {
            UserFlowState.DIRECT_TO_MAIN -> {
                navigateToMain()
                return
            }
            UserFlowState.SHOW_AUTH_ONLY -> {
                showAuthOnlyMode()
                return
            }
            UserFlowState.SHOW_WELCOME -> {
                // 继续显示完整的欢迎流程
            }
        }

        initViews()
        setupViewPager()
        setupEventListeners()
    }

    private fun getUserFlowState(): UserFlowState {
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val hasCompletedOnboarding = prefs.getBoolean("completed_onboarding", false)
        val isLoggedIn = com.vectora.vocalmind.server.UserAuthManager.isLoggedIn(this)
        val useServerMode = com.vectora.vocalmind.server.ServerConfigManager.isUseServerMode(this)

        return when {
            // 已登录用户直接进入主应用
            isLoggedIn -> UserFlowState.DIRECT_TO_MAIN

            // 隐私模式且已完成引导，直接进入主应用
            !useServerMode && hasCompletedOnboarding -> UserFlowState.DIRECT_TO_MAIN

            // 服务器模式且已完成引导但未登录，显示认证选项
            useServerMode && hasCompletedOnboarding -> UserFlowState.SHOW_AUTH_ONLY

            // 首次使用，显示完整欢迎流程
            else -> UserFlowState.SHOW_WELCOME
        }
    }

    private fun showAuthOnlyMode() {
        // 设置简化的认证界面
        setContentView(R.layout.activity_welcome_auth_only)

        val btnLogin = findViewById<Button>(R.id.btn_login)
        val btnRegister = findViewById<Button>(R.id.btn_register)
        val btnSwitchToPrivacy = findViewById<Button>(R.id.btn_switch_to_privacy)

        btnLogin.setOnClickListener {
            startActivity(Intent(this, LoginActivity::class.java))
        }

        btnRegister.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
        }

        btnSwitchToPrivacy.setOnClickListener {
            // 切换到隐私模式
            com.vectora.vocalmind.server.ServerConfigManager.setUseServerMode(this, false)
            markOnboardingCompleted()
            navigateToMain()
        }
    }

    private fun initViews() {
        viewPager = findViewById(R.id.viewpager_welcome)
        tabLayout = findViewById(R.id.tablayout_indicators)
        btnLogin = findViewById(R.id.btn_login)
        btnRegister = findViewById(R.id.btn_register)
        btnSkip = findViewById(R.id.btn_skip)
    }

    private fun setupViewPager() {
        val adapter = WelcomeViewPagerAdapter(this)
        viewPager.adapter = adapter

        // 连接TabLayout和ViewPager2
        TabLayoutMediator(tabLayout, viewPager) { _, _ ->
            // 不需要设置文本，只显示指示器
        }.attach()
    }

    private fun setupEventListeners() {
        btnLogin.setOnClickListener {
            startActivity(Intent(this, LoginActivity::class.java))
        }

        btnRegister.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
        }

        btnSkip.setOnClickListener {
            // 标记为已完成引导，直接进入主应用
            markOnboardingCompleted()
            navigateToMain()
        }
    }

    private fun markOnboardingCompleted() {
        val prefs = getSharedPreferences("app_prefs", MODE_PRIVATE)
        prefs.edit().putBoolean("completed_onboarding", true).apply()
    }

    private fun navigateToMain() {
        startActivity(Intent(this, SingleModelActivity::class.java))
        finish()
    }

    override fun onBackPressed() {
        // 检查 viewPager 是否已初始化（在某些流程中可能跳过了初始化）
        if (::viewPager.isInitialized) {
            if (viewPager.currentItem == 0) {
                super.onBackPressed()
            } else {
                viewPager.currentItem = viewPager.currentItem - 1
            }
        } else {
            // viewPager 未初始化，直接调用父类方法
            super.onBackPressed()
        }
    }
}

/**
 * 欢迎页面ViewPager适配器
 */
class WelcomeViewPagerAdapter(activity: FragmentActivity) : FragmentStateAdapter(activity) {
    
    override fun getItemCount(): Int = 3

    override fun createFragment(position: Int): Fragment {
        return WelcomeFragment.newInstance(position)
    }
}

/**
 * 欢迎页面Fragment
 */
class WelcomeFragment : Fragment() {
    
    companion object {
        private const val ARG_POSITION = "position"
        
        fun newInstance(position: Int): WelcomeFragment {
            val fragment = WelcomeFragment()
            val args = Bundle()
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_welcome, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        val position = arguments?.getInt(ARG_POSITION) ?: 0
        setupContent(view, position)
    }

    private fun setupContent(view: View, position: Int) {
        val imageView = view.findViewById<ImageView>(R.id.iv_welcome_image)
        val titleText = view.findViewById<TextView>(R.id.tv_welcome_title)
        val descText = view.findViewById<TextView>(R.id.tv_welcome_description)

        when (position) {
            0 -> {
                imageView.setImageResource(R.drawable.ic_welcome_voice)
                titleText.text = "🎙️ 智能语音识别"
                descText.text = "先进的语音识别技术，支持多种语言和方言，准确率高达99%"
            }
            1 -> {
                imageView.setImageResource(R.drawable.ic_welcome_ai)
                titleText.text = "🤖 AI优化"
                descText.text = "集成多种AI模型，自动优化识别结果，提供更准确的文本转换"
            }
            2 -> {
                imageView.setImageResource(R.drawable.ic_welcome_privacy)
                titleText.text = "🔒 隐私保护"
                descText.text = "支持本地处理和云端处理两种模式，您的数据安全由您掌控"
            }
        }
    }
}
