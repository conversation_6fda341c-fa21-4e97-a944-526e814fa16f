package com.vectora.vocalmind

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * TODO编辑Activity
 * 支持添加和编辑TODO项
 */
class TodoEditActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TodoEditActivity"
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSave: MaterialButton
    private lateinit var tvTitle: TextView
    private lateinit var etTitle: EditText
    private lateinit var etDescription: EditText
    private lateinit var etCategory: EditText
    private lateinit var spinnerPriority: Spinner
    private lateinit var switchDueDate: Switch
    private lateinit var layoutDueDate: LinearLayout
    private lateinit var btnSelectDate: Button
    private lateinit var btnSelectTime: Button
    private lateinit var tvSelectedDateTime: TextView
    private lateinit var switchReminder: Switch
    private lateinit var layoutReminder: LinearLayout
    private lateinit var btnSelectReminderDate: Button
    private lateinit var btnSelectReminderTime: Button
    private lateinit var tvSelectedReminderDateTime: TextView
    private lateinit var btnDelete: MaterialButton

    // 数据
    private lateinit var todoManager: TodoManager
    private lateinit var reminderManager: TodoReminderManager
    private var editingTodoId: String? = null
    private var isEditMode = false
    private var selectedDueDate: Calendar? = null
    private var selectedReminderDate: Calendar? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_todo_edit)

        // 检查是否需要停止响铃
        if (intent.getBooleanExtra("stop_ringtone", false)) {
            TodoNotificationHelper.stopCurrentRingtone()
        }

        initViews()
        initTodoManager()
        initPrioritySpinner()
        setupListeners()
        loadTodoData()
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSave = findViewById(R.id.btn_save)
        tvTitle = findViewById(R.id.tv_title)
        etTitle = findViewById(R.id.et_title)
        etDescription = findViewById(R.id.et_description)
        etCategory = findViewById(R.id.et_category)
        spinnerPriority = findViewById(R.id.spinner_priority)
        switchDueDate = findViewById(R.id.switch_due_date)
        layoutDueDate = findViewById(R.id.layout_due_date)
        btnSelectDate = findViewById(R.id.btn_select_date)
        btnSelectTime = findViewById(R.id.btn_select_time)
        tvSelectedDateTime = findViewById(R.id.tv_selected_datetime)
        switchReminder = findViewById(R.id.switch_reminder)
        layoutReminder = findViewById(R.id.layout_reminder)
        btnSelectReminderDate = findViewById(R.id.btn_select_reminder_date)
        btnSelectReminderTime = findViewById(R.id.btn_select_reminder_time)
        tvSelectedReminderDateTime = findViewById(R.id.tv_selected_reminder_datetime)
        btnDelete = findViewById(R.id.btn_delete)
    }
    
    private fun initTodoManager() {
        todoManager = TodoManager.getInstance(this)
        reminderManager = TodoReminderManager.getInstance(this)
    }
    
    private fun initPrioritySpinner() {
        val priorities = TodoItem.Priority.values().map { it.displayName }
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, priorities)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerPriority.adapter = adapter
    }
    
    private fun setupListeners() {
        btnBack.setOnClickListener { finish() }
        
        btnSave.setOnClickListener { saveTodo() }
        
        btnDelete.setOnClickListener { deleteTodo() }
        
        switchDueDate.setOnCheckedChangeListener { _, isChecked ->
            layoutDueDate.visibility = if (isChecked) View.VISIBLE else View.GONE
            if (!isChecked) {
                selectedDueDate = null
                updateDateTimeDisplay()
            }
        }

        switchReminder.setOnCheckedChangeListener { _, isChecked ->
            layoutReminder.visibility = if (isChecked) View.VISIBLE else View.GONE
            if (!isChecked) {
                selectedReminderDate = null
                updateReminderDateTimeDisplay()
            }
        }

        btnSelectDate.setOnClickListener { showDatePicker() }

        btnSelectTime.setOnClickListener { showTimePicker() }

        btnSelectReminderDate.setOnClickListener { showReminderDatePicker() }

        btnSelectReminderTime.setOnClickListener { showReminderTimePicker() }
    }
    
    private fun loadTodoData() {
        editingTodoId = intent.getStringExtra("todo_id")
        
        if (editingTodoId != null) {
            // 编辑模式
            isEditMode = true
            tvTitle.text = "编辑待办事项"
            btnDelete.visibility = View.VISIBLE
            
            val todoItem = todoManager.getTodo(editingTodoId!!)
            if (todoItem != null) {
                etTitle.setText(todoItem.title)
                etDescription.setText(todoItem.description)
                etCategory.setText(todoItem.category)
                
                // 设置优先级
                val priorityIndex = TodoItem.Priority.values().indexOf(todoItem.priority)
                spinnerPriority.setSelection(priorityIndex)
                
                // 设置截止时间
                if (todoItem.dueDate != null) {
                    switchDueDate.isChecked = true
                    selectedDueDate = Calendar.getInstance().apply {
                        timeInMillis = todoItem.dueDate
                    }
                    updateDateTimeDisplay()
                }

                // 设置提醒时间
                if (todoItem.reminderTime != null) {
                    switchReminder.isChecked = true
                    selectedReminderDate = Calendar.getInstance().apply {
                        timeInMillis = todoItem.reminderTime
                    }
                    updateReminderDateTimeDisplay()
                }
            }
        } else {
            // 添加模式
            isEditMode = false
            tvTitle.text = "添加待办事项"
            btnDelete.visibility = View.GONE
            
            // 设置默认值
            etCategory.setText("通用")
            spinnerPriority.setSelection(1) // 默认中等优先级
        }
    }
    
    private fun showDatePicker() {
        val calendar = selectedDueDate ?: Calendar.getInstance()
        
        DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                if (selectedDueDate == null) {
                    selectedDueDate = Calendar.getInstance()
                }
                selectedDueDate!!.set(Calendar.YEAR, year)
                selectedDueDate!!.set(Calendar.MONTH, month)
                selectedDueDate!!.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                updateDateTimeDisplay()
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }
    
    private fun showTimePicker() {
        val calendar = selectedDueDate ?: Calendar.getInstance()
        
        TimePickerDialog(
            this,
            { _, hourOfDay, minute ->
                if (selectedDueDate == null) {
                    selectedDueDate = Calendar.getInstance()
                }
                selectedDueDate!!.set(Calendar.HOUR_OF_DAY, hourOfDay)
                selectedDueDate!!.set(Calendar.MINUTE, minute)
                selectedDueDate!!.set(Calendar.SECOND, 0)
                updateDateTimeDisplay()
            },
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE),
            true
        ).show()
    }
    
    private fun updateDateTimeDisplay() {
        if (selectedDueDate != null) {
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            tvSelectedDateTime.text = formatter.format(selectedDueDate!!.time)
            tvSelectedDateTime.visibility = View.VISIBLE
        } else {
            tvSelectedDateTime.visibility = View.GONE
        }
    }

    private fun showReminderDatePicker() {
        val calendar = selectedReminderDate ?: Calendar.getInstance()

        DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                if (selectedReminderDate == null) {
                    selectedReminderDate = Calendar.getInstance()
                }
                selectedReminderDate!!.set(Calendar.YEAR, year)
                selectedReminderDate!!.set(Calendar.MONTH, month)
                selectedReminderDate!!.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                updateReminderDateTimeDisplay()
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun showReminderTimePicker() {
        val calendar = selectedReminderDate ?: Calendar.getInstance()

        TimePickerDialog(
            this,
            { _, hourOfDay, minute ->
                if (selectedReminderDate == null) {
                    selectedReminderDate = Calendar.getInstance()
                }
                selectedReminderDate!!.set(Calendar.HOUR_OF_DAY, hourOfDay)
                selectedReminderDate!!.set(Calendar.MINUTE, minute)
                selectedReminderDate!!.set(Calendar.SECOND, 0)
                selectedReminderDate!!.set(Calendar.MILLISECOND, 0)
                updateReminderDateTimeDisplay()
            },
            calendar.get(Calendar.HOUR_OF_DAY),
            calendar.get(Calendar.MINUTE),
            true
        ).show()
    }

    private fun updateReminderDateTimeDisplay() {
        if (selectedReminderDate != null) {
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            tvSelectedReminderDateTime.text = formatter.format(selectedReminderDate!!.time)
            tvSelectedReminderDateTime.visibility = View.VISIBLE
        } else {
            tvSelectedReminderDateTime.visibility = View.GONE
        }
    }
    
    private fun saveTodo() {
        val title = etTitle.text.toString().trim()
        val description = etDescription.text.toString().trim()
        val category = etCategory.text.toString().trim().ifEmpty { "通用" }
        val priority = TodoItem.Priority.values()[spinnerPriority.selectedItemPosition]
        val dueDate = if (switchDueDate.isChecked) selectedDueDate?.timeInMillis else null
        val reminderTime = if (switchReminder.isChecked) selectedReminderDate?.timeInMillis else null
        
        // 验证输入
        if (title.isEmpty()) {
            etTitle.error = "请输入标题"
            etTitle.requestFocus()
            return
        }
        
        lifecycleScope.launch {
            try {
                val success = if (isEditMode) {
                    // 更新现有TODO
                    val existingTodo = todoManager.getTodo(editingTodoId!!)
                    if (existingTodo != null) {
                        val updatedTodo = existingTodo.copy(
                            title = title,
                            description = description,
                            category = category,
                            priority = priority,
                            dueDate = dueDate,
                            reminderTime = reminderTime
                        )
                        val updateSuccess = todoManager.updateTodo(updatedTodo)

                        // 更新提醒
                        if (updateSuccess) {
                            reminderManager.updateReminder(updatedTodo)
                        }

                        updateSuccess
                    } else {
                        false
                    }
                } else {
                    // 创建新TODO
                    val newTodo = TodoItem(
                        title = title,
                        description = description,
                        category = category,
                        priority = priority,
                        dueDate = dueDate,
                        reminderTime = reminderTime
                    )
                    val addSuccess = todoManager.addTodo(newTodo)

                    // 设置提醒
                    if (addSuccess && reminderTime != null) {
                        reminderManager.setReminder(newTodo)
                    }

                    addSuccess
                }
                
                if (success) {
                    setResult(RESULT_OK)
                    finish()
                    showToast(if (isEditMode) "更新成功" else "添加成功")
                } else {
                    showToast("保存失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "保存TODO失败", e)
                showToast("保存失败: ${e.message}")
            }
        }
    }
    
    private fun deleteTodo() {
        if (!isEditMode || editingTodoId == null) return
        
        AppleInfoDialog(
            context = this,
            title = "删除待办事项",
            message = "确定要删除这个待办事项吗？\n\n此操作无法撤销。",
            positiveButtonText = "删除",
            negativeButtonText = "取消",
            onPositiveClick = {
                lifecycleScope.launch {
                    try {
                        // 先取消提醒
                        reminderManager.cancelReminder(editingTodoId!!)

                        val success = todoManager.deleteTodo(editingTodoId!!)
                        if (success) {
                            setResult(RESULT_OK)
                            finish()
                            showToast("已删除")
                        } else {
                            showToast("删除失败")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "删除TODO失败", e)
                        showToast("删除失败: ${e.message}")
                    }
                }
            }
        ).show()
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
