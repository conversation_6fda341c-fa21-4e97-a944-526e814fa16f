package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Patterns
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import kotlinx.coroutines.launch

/**
 * 忘记密码页面 - SaaS标准密码重置界面
 * 提供邮箱验证和密码重置功能
 */
class ForgotPasswordActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ForgotPasswordActivity"
    }

    // UI组件
    private lateinit var tilEmail: TextInputLayout
    private lateinit var etEmail: TextInputEditText
    private lateinit var btnSendCode: MaterialButton
    private lateinit var tvBackToLogin: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvError: TextView
    private lateinit var tvSuccess: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_forgot_password)

        initViews()
        setupEventListeners()
        setupFormValidation()
    }

    private fun initViews() {
        tilEmail = findViewById(R.id.til_email)
        etEmail = findViewById(R.id.et_email)
        btnSendCode = findViewById(R.id.btn_send_code)
        tvBackToLogin = findViewById(R.id.tv_back_to_login)
        progressBar = findViewById(R.id.progress_bar)
        tvError = findViewById(R.id.tv_error)
        tvSuccess = findViewById(R.id.tv_success)
    }

    private fun setupEventListeners() {
        btnSendCode.setOnClickListener { sendResetCode() }
        
        tvBackToLogin.setOnClickListener {
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
        }

        // 返回按钮
        findViewById<ImageView>(R.id.iv_back).setOnClickListener {
            onBackPressed()
        }
    }

    private fun setupFormValidation() {
        etEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                validateForm()
                clearMessages()
            }
        })
    }

    private fun validateForm(): Boolean {
        val email = etEmail.text.toString().trim()

        if (email.isEmpty()) {
            tilEmail.error = "请输入邮箱"
            btnSendCode.isEnabled = false
            return false
        } else if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            tilEmail.error = "请输入有效的邮箱地址"
            btnSendCode.isEnabled = false
            return false
        } else {
            tilEmail.error = null
            btnSendCode.isEnabled = true
            return true
        }
    }

    private fun clearMessages() {
        tvError.visibility = View.GONE
        tvSuccess.visibility = View.GONE
    }

    private fun showError(message: String) {
        tvError.text = message
        tvError.visibility = View.VISIBLE
        tvSuccess.visibility = View.GONE
    }

    private fun showSuccess(message: String) {
        tvSuccess.text = message
        tvSuccess.visibility = View.VISIBLE
        tvError.visibility = View.GONE
    }

    private fun sendResetCode() {
        if (!validateForm()) return

        val email = etEmail.text.toString().trim()

        setLoading(true)
        clearMessages()

        // 模拟发送重置邮件的过程
        lifecycleScope.launch {
            try {
                // TODO: 实际的API调用
                kotlinx.coroutines.delay(2000) // 模拟网络请求

                runOnUiThread {
                    setLoading(false)
                    showSuccess("重置链接已发送到您的邮箱，请查收邮件并按照指示重置密码。")
                    
                    // 3秒后自动返回登录页面
                    kotlinx.coroutines.GlobalScope.launch {
                        kotlinx.coroutines.delay(3000)
                        runOnUiThread {
                            startActivity(Intent(this@ForgotPasswordActivity, LoginActivity::class.java))
                            finish()
                        }
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    setLoading(false)
                    showError("发送失败，请检查网络连接后重试")
                }
            }
        }
    }

    private fun setLoading(loading: Boolean) {
        progressBar.visibility = if (loading) View.VISIBLE else View.GONE
        btnSendCode.isEnabled = !loading && validateForm()
        etEmail.isEnabled = !loading
    }

    override fun onBackPressed() {
        startActivity(Intent(this, LoginActivity::class.java))
        finish()
    }
}
