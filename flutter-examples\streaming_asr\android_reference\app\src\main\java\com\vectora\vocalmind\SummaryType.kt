package com.vectora.vocalmind

/**
 * 总结类型枚举
 * 定义不同场景下的总结模板
 */
enum class SummaryType(val displayName: String, val description: String) {
    MEETING("会议总结", "提取会议要点、决策和行动项"),
    BRIEF("简要总结", "生成简洁的要点总结"),
    DETAILED("详细总结", "生成详细的内容总结"),
    ACTION_ITEMS("行动项", "提取行动项和待办事项"),
    DECISIONS("决策总结", "提取重要决策和结论"),
    INTERVIEW("面试总结", "整理面试问答、评估要点"),
    LECTURE("课堂总结", "归纳知识点、重点内容"),
    SPEECH("演讲总结", "提炼演讲主题、核心观点"),
    INTERVIEW_MEDIA("访谈总结", "整理访谈内容、关键信息"),
    DISCUSSION("讨论总结", "梳理讨论话题、不同观点"),
    TRAINING("培训总结", "总结培训内容、学习要点"),
    PRESENTATION("汇报总结", "整理汇报内容、关键数据"),
    DIARY("日记总结", "整理日常记录、感悟思考"),
    DEV_LOG("开发日志", "记录开发过程、技术要点");

    companion object {
        /**
         * 获取所有显示名称
         */
        fun getDisplayNames(): Array<String> {
            return values().map { it.displayName }.toTypedArray()
        }

        /**
         * 根据位置获取类型
         */
        fun fromPosition(position: Int): SummaryType {
            return if (position in 0 until values().size) {
                values()[position]
            } else {
                MEETING // 默认返回会议总结
            }
        }

        /**
         * 根据显示名称获取类型
         */
        fun fromDisplayName(displayName: String): SummaryType {
            return values().find { it.displayName == displayName } ?: MEETING
        }
    }
}