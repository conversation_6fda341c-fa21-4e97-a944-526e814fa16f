package com.vectora.vocalmind

/**
 * Mermaid图表类型枚举
 */
enum class MermaidType(val displayName: String, val mermaidKeyword: String) {
    MINDMAP("思维导图", "flowchart LR"),
    FLOWCHART_TD("竖向流程图", "flowchart TD");
    
    companion object {
        fun fromPosition(position: Int): MermaidType {
            return values()[position]
        }
        
        fun getDisplayNames(): Array<String> {
            return values().map { it.displayName }.toTypedArray()
        }
    }
}