<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorOnSurface">
  <!-- Clipboard background -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M19,3h-4.18C14.4,1.84 13.3,1 12,1c-1.3,0 -2.4,0.84 -2.82,2H5C3.9,3 3,3.9 3,5v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2V5C21,3.9 20.1,3 19,3zM12,3c0.55,0 1,0.45 1,1s-0.45,1 -1,1s-1,-0.45 -1,-1S11.45,3 12,3zM19,19H5V5h2v3h10V5h2V19z"/>
  <!-- First checkmark -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M7,10l1,1l2,-2l0.7,0.7l-2.7,2.7l-1.7,-1.7z"/>
  <!-- First line -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M13,10.5h5v1.5h-5z"/>
  <!-- Second checkmark -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M7,13l1,1l2,-2l0.7,0.7l-2.7,2.7l-1.7,-1.7z"/>
  <!-- Second line -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M13,13.5h5v1.5h-5z"/>
  <!-- Third checkmark -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M7,16l1,1l2,-2l0.7,0.7l-2.7,2.7l-1.7,-1.7z"/>
  <!-- Third line -->
  <path
      android:fillColor="@android:color/white"
      android:pathData="M13,16.5h5v1.5h-5z"/>
</vector>
