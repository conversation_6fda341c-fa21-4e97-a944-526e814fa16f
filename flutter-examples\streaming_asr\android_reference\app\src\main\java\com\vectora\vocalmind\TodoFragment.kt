package com.vectora.vocalmind

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ImageButton
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import com.google.android.material.button.MaterialButton
import com.google.android.material.floatingactionbutton.FloatingActionButton
import kotlinx.coroutines.launch

/**
 * TODO Fragment
 * 从TodoActivity迁移而来，用于Tab布局
 */
class TodoFragment : Fragment() {

    companion object {
        private const val TAG = "TodoFragment"
        private const val REQUEST_ADD_TODO = 1001
        private const val REQUEST_EDIT_TODO = 1002
        
        fun newInstance(): TodoFragment {
            return TodoFragment()
        }
    }

    // UI组件
    private lateinit var tvTitle: TextView
    private lateinit var tvStatistics: TextView
    private lateinit var btnSettings: ImageButton
    private lateinit var spinnerFilter: Spinner
    private lateinit var etSearch: EditText
    private lateinit var recyclerView: RecyclerView
    private lateinit var fabAdd: FloatingActionButton
    private lateinit var layoutEmpty: LinearLayout
    private lateinit var btnCreateSample: MaterialButton

    // 数据管理
    private lateinit var todoManager: TodoManager
    private lateinit var reminderManager: TodoReminderManager
    private lateinit var adapter: GroupedTodoAdapter
    private var currentFilter = FilterType.ALL
    private var allTodos = mutableListOf<TodoItem>()
    private var filteredTodos = mutableListOf<TodoItem>()

    // 过滤类型
    enum class FilterType(val displayName: String) {
        ALL("全部"),
        INCOMPLETE("未完成"),
        COMPLETED("已完成"),
        HIGH_PRIORITY("高优先级"),
        OVERDUE("已过期"),
        DUE_SOON("即将到期")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_todo, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        initTodoManager()
        initRecyclerView()
        initFilterSpinner()
        setupListeners()
        loadTodos()
    }

    private fun initViews(view: View) {
        tvTitle = view.findViewById(R.id.tv_title)
        tvStatistics = view.findViewById(R.id.tv_statistics)
        btnSettings = view.findViewById(R.id.btn_settings)
        spinnerFilter = view.findViewById(R.id.spinner_filter)
        etSearch = view.findViewById(R.id.et_search)
        recyclerView = view.findViewById(R.id.recycler_view)
        fabAdd = view.findViewById(R.id.fab_add)
        layoutEmpty = view.findViewById(R.id.layout_empty)
        btnCreateSample = view.findViewById(R.id.btn_create_sample)

        tvTitle.text = "📝 AI待办"
    }

    private fun initTodoManager() {
        todoManager = TodoManager.getInstance(requireContext())
        reminderManager = TodoReminderManager.getInstance(requireContext())
    }

    private fun initRecyclerView() {
        adapter = GroupedTodoAdapter(
            onItemClick = { todo -> editTodo(todo) },
            onItemToggle = { todo -> updateTodoStatus(todo, !todo.isCompleted) },
            onItemDelete = { todo -> deleteTodo(todo) }
        )

        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = adapter

        // 设置拖拽排序功能
        val itemTouchHelper = ItemTouchHelper(GroupedTodoItemTouchHelperCallback(adapter) { reorderedList ->
            // 更新TODO排序
            lifecycleScope.launch {
                try {
                    val success = todoManager.updateTodoOrder(reorderedList)
                    if (!success) {
                        showToast("排序更新失败")
                        // 重新加载数据以恢复原始顺序
                        loadTodos()
                    }
                } catch (e: Exception) {
                    showToast("排序更新失败: ${e.message}")
                    loadTodos()
                }
            }
        })
        itemTouchHelper.attachToRecyclerView(recyclerView)
    }

    private fun initFilterSpinner() {
        val filterAdapter = android.widget.ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            FilterType.values().map { it.displayName }
        )
        filterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerFilter.adapter = filterAdapter
        
        spinnerFilter.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                currentFilter = FilterType.values()[position]
                loadTodos()
            }
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }
    }

    private fun setupListeners() {
        fabAdd.setOnClickListener { addTodo() }
        btnCreateSample.setOnClickListener { createSampleTodos() }
        btnSettings.setOnClickListener { openSettings() }

        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                filterTodos(s?.toString() ?: "")
            }
        })
    }

    private fun loadTodos() {
        lifecycleScope.launch {
            try {
                val todos = when (currentFilter) {
                    FilterType.ALL -> todoManager.getAllTodos()
                    FilterType.INCOMPLETE -> todoManager.getIncompleteTodos()
                    FilterType.COMPLETED -> todoManager.getCompletedTodos()
                    FilterType.HIGH_PRIORITY -> todoManager.getTodosByPriority(TodoItem.Priority.HIGH) + 
                                              todoManager.getTodosByPriority(TodoItem.Priority.URGENT)
                    FilterType.OVERDUE -> todoManager.getOverdueTodos()
                    FilterType.DUE_SOON -> todoManager.getDueSoonTodos()
                }
                
                allTodos.clear()
                allTodos.addAll(todos)

                filterTodos(etSearch.text.toString())
                updateStatistics()
                
            } catch (e: Exception) {
                Log.e(TAG, "加载TODO失败", e)
                showToast("加载TODO失败: ${e.message}")
            }
        }
    }

    private fun filterTodos(query: String) {
        filteredTodos.clear()

        if (query.isEmpty()) {
            filteredTodos.addAll(allTodos)
        } else {
            filteredTodos.addAll(allTodos.filter { todo ->
                todo.title.contains(query, ignoreCase = true) ||
                todo.description.contains(query, ignoreCase = true) ||
                todo.category.contains(query, ignoreCase = true)
            })
        }

        adapter.updateTodos(filteredTodos)
        updateEmptyView()
    }

    private fun updateStatistics() {
        val total = allTodos.size
        val completed = allTodos.count { it.isCompleted }
        val incomplete = total - completed
        val overdue = allTodos.count { it.isOverdue() }
        
        tvStatistics.text = "总计: $total | 已完成: $completed | 未完成: $incomplete | 过期: $overdue"
    }

    private fun updateEmptyView() {
        if (filteredTodos.isEmpty()) {
            recyclerView.visibility = View.GONE
            layoutEmpty.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            layoutEmpty.visibility = View.GONE
        }
    }

    private fun addTodo() {
        val intent = Intent(requireContext(), TodoEditActivity::class.java)
        startActivityForResult(intent, REQUEST_ADD_TODO)
    }

    private fun editTodo(todo: TodoItem) {
        val intent = Intent(requireContext(), TodoEditActivity::class.java)
        intent.putExtra("todo_id", todo.id)
        startActivityForResult(intent, REQUEST_EDIT_TODO)
    }

    private fun updateTodoStatus(todo: TodoItem, isCompleted: Boolean) {
        lifecycleScope.launch {
            try {
                val updatedTodo = todo.copy(
                    isCompleted = isCompleted,
                    completedAt = if (isCompleted) System.currentTimeMillis() else null
                )
                
                val success = todoManager.updateTodo(updatedTodo)
                if (success) {
                    loadTodos()
                    
                    // 如果完成了任务，取消提醒
                    if (isCompleted) {
                        reminderManager.cancelReminder(todo.id)
                    } else if (todo.dueDate != null) {
                        // 如果重新标记为未完成且有截止时间，重新设置提醒
                        reminderManager.setReminder(updatedTodo)
                    }
                } else {
                    showToast("更新失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "更新TODO状态失败", e)
                showToast("更新失败: ${e.message}")
            }
        }
    }

    private fun deleteTodo(todo: TodoItem) {
        AppleInfoDialog(
            context = requireContext(),
            title = "删除待办事项",
            message = "确定要删除「${todo.title}」吗？\n\n此操作不可撤销。",
            positiveButtonText = "删除",
            negativeButtonText = "取消",
            onPositiveClick = {
                performDeleteTodo(todo)
            }
        ).show()
    }

    private fun performDeleteTodo(todo: TodoItem) {
        lifecycleScope.launch {
            try {
                val success = todoManager.deleteTodo(todo.id)
                if (success) {
                    // 取消相关提醒
                    reminderManager.cancelReminder(todo.id)
                    loadTodos()
                    showToast("已删除")
                } else {
                    showToast("删除失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除TODO失败", e)
                showToast("删除失败: ${e.message}")
            }
        }
    }

    private fun createSampleTodos() {
        lifecycleScope.launch {
            try {
                val sampleTodos = TodoItem.createSample()
                val success = todoManager.addTodos(sampleTodos)
                if (success) {
                    loadTodos()
                    showToast("已创建示例待办事项")
                } else {
                    showToast("创建失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "创建示例TODO失败", e)
                showToast("创建失败: ${e.message}")
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode == android.app.Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_ADD_TODO, REQUEST_EDIT_TODO -> {
                    loadTodos()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        
        // 当用户打开TODO列表时，停止任何正在播放的提醒响铃
        TodoNotificationHelper.stopCurrentRingtone()
        
        loadTodos()
    }

    private fun openSettings() {
        (activity as? SingleModelActivity)?.openSettings()
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
}
