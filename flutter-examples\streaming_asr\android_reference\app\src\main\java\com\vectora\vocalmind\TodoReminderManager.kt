package com.vectora.vocalmind

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * TODO提醒管理器
 * 负责设置、取消和管理TODO项的提醒功能
 */
class TodoReminderManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "TodoReminderManager"
        
        @Volatile
        private var INSTANCE: TodoReminderManager? = null
        
        fun getInstance(context: Context): TodoReminderManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TodoReminderManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    /**
     * 为TODO项设置提醒
     */
    fun setReminder(todoItem: TodoItem): Boolean {
        return try {
            val reminderTime = todoItem.reminderTime
            if (reminderTime == null) {
                Log.w(TAG, "TODO项没有设置提醒时间: ${todoItem.title}")
                return false
            }
            
            // 检查提醒时间是否在未来
            if (reminderTime <= System.currentTimeMillis()) {
                Log.w(TAG, "提醒时间已过期: ${todoItem.title}")
                return false
            }
            
            // 检查TODO是否已完成
            if (todoItem.isCompleted) {
                Log.w(TAG, "TODO已完成，不设置提醒: ${todoItem.title}")
                return false
            }
            
            // 创建提醒Intent
            val intent = Intent(context, TodoReminderReceiver::class.java).apply {
                action = TodoReminderReceiver.ACTION_TODO_REMINDER
                putExtra(TodoReminderReceiver.EXTRA_TODO_ID, todoItem.id)
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                todoItem.id.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 设置精确的闹钟，确保后台唤醒
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
                    // Android 12+ 需要检查精确闹钟权限
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            reminderTime,
                            pendingIntent
                        )
                    } else {
                        // 如果没有精确闹钟权限，使用普通闹钟
                        alarmManager.setAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            reminderTime,
                            pendingIntent
                        )
                    }
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+ 使用 setExactAndAllowWhileIdle 以确保在低电耗模式下也能触发
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        reminderTime,
                        pendingIntent
                    )
                }
                else -> {
                    // Android 6.0 以下版本
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        reminderTime,
                        pendingIntent
                    )
                }
            }
            
            Log.i(TAG, "已设置TODO提醒: ${todoItem.title}, 时间: ${todoItem.getFormattedReminderTime()}")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "设置TODO提醒失败: ${todoItem.title}", e)
            false
        }
    }
    
    /**
     * 取消TODO项的提醒
     */
    fun cancelReminder(todoId: String): Boolean {
        return try {
            val intent = Intent(context, TodoReminderReceiver::class.java).apply {
                action = TodoReminderReceiver.ACTION_TODO_REMINDER
                putExtra(TodoReminderReceiver.EXTRA_TODO_ID, todoId)
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                todoId.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()
            
            // 同时取消通知
            val notificationHelper = TodoNotificationHelper(context)
            notificationHelper.cancelReminderNotification(todoId)
            
            Log.i(TAG, "已取消TODO提醒: $todoId")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "取消TODO提醒失败: $todoId", e)
            false
        }
    }
    
    /**
     * 更新TODO项的提醒
     */
    fun updateReminder(todoItem: TodoItem): Boolean {
        // 先取消旧的提醒
        cancelReminder(todoItem.id)
        
        // 如果有新的提醒时间，则设置新提醒
        return if (todoItem.reminderTime != null && !todoItem.isCompleted) {
            setReminder(todoItem)
        } else {
            true
        }
    }
    
    /**
     * 批量设置多个TODO项的提醒
     */
    fun setReminders(todoItems: List<TodoItem>): Int {
        var successCount = 0
        todoItems.forEach { todoItem ->
            if (setReminder(todoItem)) {
                successCount++
            }
        }
        Log.i(TAG, "批量设置提醒完成: 成功 $successCount/${todoItems.size}")
        return successCount
    }
    
    /**
     * 批量取消多个TODO项的提醒
     */
    fun cancelReminders(todoIds: List<String>): Int {
        var successCount = 0
        todoIds.forEach { todoId ->
            if (cancelReminder(todoId)) {
                successCount++
            }
        }
        Log.i(TAG, "批量取消提醒完成: 成功 $successCount/${todoIds.size}")
        return successCount
    }
    
    /**
     * 取消所有TODO提醒
     */
    fun cancelAllReminders() {
        try {
            val todoManager = TodoManager.getInstance(context)
            val allTodos = todoManager.getAllTodos()
            
            allTodos.forEach { todoItem ->
                if (todoItem.reminderTime != null) {
                    cancelReminder(todoItem.id)
                }
            }
            
            Log.i(TAG, "已取消所有TODO提醒")
            
        } catch (e: Exception) {
            Log.e(TAG, "取消所有TODO提醒失败", e)
        }
    }
    
    /**
     * 检查系统是否允许设置精确闹钟（Android 12+）
     */
    fun canScheduleExactAlarms(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }
    }
    
    /**
     * 重新调度所有活跃的提醒（用于系统重启后恢复）
     */
    fun rescheduleAllReminders() {
        try {
            val todoManager = TodoManager.getInstance(context)
            val activeTodos = todoManager.getAllTodos().filter { 
                it.hasActiveReminder() 
            }
            
            Log.i(TAG, "重新调度 ${activeTodos.size} 个活跃提醒")
            setReminders(activeTodos)
            
        } catch (e: Exception) {
            Log.e(TAG, "重新调度提醒失败", e)
        }
    }
}
